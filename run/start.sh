#! /usr/bin/sh
# 从 config.ini 中读取 CMD_LINE 配置，并作为命令执行进行启动.
# @11:01 2019/07/02

# config.ini 配置说明：
#
# | config                                        | 用于                          | 性能参数                          | setup_dpdk_env.sh 配合          |
# |-----------------------------------------------+-------------------------------+-----------------------------------+---------------------------------|
# | config.ini                                    | 线上生产运行                  | 10G 网卡 + 17 核心 cpu + 40G 巨页 | setup_dpdk_env.sh -b eth0 eth1  |
# | config_devel_1G-IF_12-cores_20G-hugepages.ini | 功能开发(replay 模式)         | 1 G 网卡 + 12 核心 cpu + 20G 巨页 | setup_dpdk_env.sh -m 20 -b eth0 |
# | config_devel_read_from_pcap_dir.ini           | 功能开发(6G内存 + 6核 centos) | 4 核心 cpu (巨页无需配置)         | setup_dpdk_env.sh -b            |
#
# 修改为 replay 模式的开发配置：
# ln -sf ../etc/config_devel_1G-IF_12-cores_20G-hugepages.ini config.ini
# ../env/setup_dpdk_env.sh -m 20 -b eth0

# change cwd to the dir containing this script.
cd $(dirname $0)

CONFIG_PATH='./config.ini'

# check config file exist.
if [ ! -f $CONFIG_PATH ]
then
    echo "there is no $CONFIG_PATH"
    exit
fi

# drop blanks around '=' and eval the config file.
eval "$(sed -n '/=/s/[[:space:]]*=[[:space:]]*/=/p' $CONFIG_PATH)"

# 获取 /etc/proc 里面的 hugepages  如果值小于10，配置环境变量为低配,程序根据环境变量选择性能参数
hugepages=`cat /proc/meminfo | grep HugePages_Total | awk '{print $2}'`
if [ $hugepages -lt 10 ]
then
    echo "hugepages is $hugepages, set low performance."
    export DPI_CONFIG_MODE=low
    # cmd_line = cmd_line_low
    CMD_LINE=$CMD_LINE_LOW
fi

# check CMD_LINE exist.
if [ -z "$CMD_LINE" ]
then
    echo "$CONFIG_PATH should define CMD_LINE."
    exit
fi

eval "echo 20304 restart yaDpi `date` >>log.txt"

export SDT_RULEFILE_MONITOR=1  #开关监测规则代理文件(SDT_Rule_Update.txt)
#export SDT_LOG_LEVEL="LOG_STOP"
#export SDT_LOG_LEVEL="LOG_ERROR"
#export SDT_LOG_LEVEL="LOG_WARN"
#export SDT_LOG_LEVEL="LOG_INFO"
#export SDT_LOG_LEVEL="LOG_DEBUG"
export SDT_LOG_LEVEL="LOG_TRACE"

export LD_LIBRARY_PATH=.:$LD_LIBRARY_PATH
export LD_LIBRARY_PATH=../lib:./lib64:$LD_LIBRARY_PATH

sysctl -w fs.file-max=150000
ulimit -n 150000
ulimit -s 102400

while [[ $# -gt 0 ]]
do
    case $1 in
    gdb)
        CMD_LINE="gdb --args ${CMD_LINE}"
        ;;
    --)
        shift
        break
        ;;
    --help)
        echo "$0 [gdb] [-- <解析程序附加参数>]"
        exit 1
        ;;
    *)
        echo "未知参数 $1"
        exit 1
        ;;
    esac
    shift
done

# exec cmd.
echo "CMD_LINE is $CMD_LINE $@"
eval $CMD_LINE $@
