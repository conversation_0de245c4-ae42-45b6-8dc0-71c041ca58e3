-- X509Cer.lua
local mapping = {
  from_proto_name = "X509Cer",
  to_proto_name = "cert",
  rule_proto_name = "cert",
  common_flag = true,
  field = {
{from_name = "version"            ,to_name = "cert_version"                      ,rule_name = "version"                  ,tll = 1},
{from_name = "sequence"           ,to_name = "cert_sn"                           ,rule_name = "sn"                       ,tll = 1},
{from_name = "signature_alg"      ,to_name = "cert_algorithm_id"                 ,rule_name = "algorithm_id"             ,tll = 1},
{from_name = "issuer"             ,to_name = "cert_issuer"                       ,rule_name = "issuer"                   ,tll = 1},
{from_name = "subject"            ,to_name = "cert_subject"                      ,rule_name = "subject"                  ,tll = 1},
{from_name = "issuer_firstname"   ,to_name = "cert_issuer_common_name"           ,rule_name = "issuer_common_name"       ,tll = 1},
{from_name = "subject_firstname"  ,to_name = "cert_subject_common_name"          ,rule_name = "subject_common_name"      ,tll = 1},
{from_name = "issuer_party"       ,to_name = "cert_issuer_organization"          ,rule_name = "issuer_organization"      ,tll = 1},
{from_name = "subject_party"      ,to_name = "cert_subject_organization"         ,rule_name = "subject_organization"               ,tll = 1},
{from_name = "begintime"          ,to_name = "cert_validity_notbefore"           ,rule_name = "validity_notbefore"       ,tll = 1},
{from_name = "endtime"            ,to_name = "cert_validity_notafter"            ,rule_name = "validity_notafter"        ,tll = 1},
{from_name = "lasttime"           ,to_name = "cert_validity_remaining"           ,rule_name = "validity_remaining"       ,tll = 1},
{from_name = "totaltime"          ,to_name = "cert_validity_total"               ,rule_name = "validity_total"       ,tll = 1},
{from_name = "subj_pubkey"        ,to_name = "cert_public_key"                   ,rule_name = "public_key"               ,tll = 1},
{from_name = "key_usage"          ,to_name = "cert_key_usage"                    ,rule_name = "key_usage"                ,tll = 1},
{from_name = "auth_key_id"        ,to_name = "cert_auth_key_id"                  ,rule_name = "auth_key_id"              ,tll = 1},
{from_name = "subj_key_id"        ,to_name = "cert_subject_key_id"               ,rule_name = "subject_key_id"           ,tll = 1},
{from_name = "subj_dns"           ,to_name = "cert_alternative_domain"           ,rule_name = "alternative_domain"       ,tll = 1},
{from_name = "dns_num"            ,to_name = "cert_alternative_domain_count"     ,rule_name = "alternative_domain_count"       ,tll = 1},
{from_name = "subj_ip"            ,to_name = "cert_alternative_ip"               ,rule_name = "alternative_ip"           ,tll = 1},
{from_name = "subj_ip"            ,to_name = "cert_alternative_ip_count"         ,rule_name = "alternative_ip_count"           ,tll = 1},
{from_name = "key_ext"            ,to_name = "cert_key_purpose_ids"              ,rule_name = "key_purpose_ids"           ,tll = 1},
{from_name = "crl_dist_points"    ,to_name = "cert_crl_distribute_points"        ,rule_name = "crl_distribute_points"           ,tll = 1},
{from_name = "policy"             ,to_name = "cert_policies"                     ,rule_name = "policies"           ,tll = 1},
{from_name = "authAccess"         ,to_name = "cert_auth_info"                    ,rule_name = "auth_info"           ,tll = 1},
{from_name = "basicConsCA"        ,to_name = "cert_basic_ca"                     ,rule_name = "basic_ca"           ,tll = 1},
{from_name = "basicConsPathLen"   ,to_name = "cert_basic_path_length"            ,rule_name = "basic_path_length"           ,tll = 1},
{from_name = "print"              ,to_name = "cert_fingerprint"                  ,rule_name = "fingerprint"           ,tll = 1},
{from_name = "print_alg"          ,to_name = "cert_fingerprint_algorithm"        ,rule_name = "fingerprint_algorithm"           ,tll = 1},
{from_name = "extension"          ,to_name = "cert_extend_id_list"               ,rule_name = "extend_id_list"           ,tll = 1},
{from_name = "ext_sums"           ,to_name = "cert_extend_count"                 ,rule_name = "extend_count"           ,tll = 1},
{from_name = "cert_len"           ,to_name = "cert_raw"                          ,rule_name = "raw"           ,tll = 1},
{from_name = "cert_num"           ,to_name = "cert_source"                       ,rule_name = "source"           ,tll = 1},
{from_name = "filename"           ,to_name = "cert_filename"                     ,rule_name = "filename"           ,tll = 1},

  }
}
yalua_register_proto(mapping)
