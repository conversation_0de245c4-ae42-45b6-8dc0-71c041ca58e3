local g_alias_rule_map = {}

function file_exists(name)
  local f = io.open(name, "r")
  if f ~= nil then
    io.close(f);
    return true
  else
    return false
  end
end

local g_custom_exist = file_exists("adapt_sdt.lua")

--[[
]]
function yalua_register_proto_layer(to_schema, from_schema, layer_map)
  for k, f in pairs(layer_map.field)
  do
    -- 如果存在 from 则使用与 from_name 相同的类型
    local field_type = YA_FT_STRING -- field 类型默认为 YA_FT_STRING
    if f.from_name and f.from_name ~= "" then
      field_type = pschema.get_field_type(from_schema, f.from_name)
    end

    -- 注册字段
    pschema.register_field(to_schema, f.to_name, field_type, "adapted_proto, no desc")

    -- 当存在 rule_name 时，为 from_schema 中的 from_name 字段设置一个 'rule_name' 属性
    if f.rule_name and f.rule_name ~= "" and
        f.from_name and f.from_name ~= "" then
      pschema.set_field_attribute(from_schema, f.from_name, "rule_name", f.rule_name)
    end
  end
end

function yalua_register_proto_adapter_layer(to, from, layer_map)
  for k, f in pairs(layer_map.field)
  do
    if f.trans_fun then
      to[f.to_name] = f.trans_fun(from)
    else
      -- print(string.format("adapt field %s %s %s", layer_map.from_proto_name, f.to_name, f.from_name))
      if f.from_name ~= "" then
        -- print(string.format("adapt field %s %s %s", layer_map.from_proto_name, f.to_name, f.from_name))
        to[f.to_name] = from[f.from_name]
      else
        to[f.to_name] = ""
      end
    end
  end
end

function yalua_register_proto(m)
  if g_custom_exist then
    g_custom_adapt = require("adapt_sdt")
  -- 如果没有一个存在 rule_name
    rule_flag = 0
    for k, f in pairs(m.field) do
        if f.rule_name and f.rule_name ~= "" then
          rule_flag= 1
        end
    end
    if rule_flag == 0 and m.from_proto_name ~= "common" then
      return
    end
  end
  local adapter = function(to, from)
    yalua_register_proto_adapter_layer(to, from, m)
  end

  local init = function(to_schema, from_schema)
    -- if g_custom_exist then
    --   g_custom_adapt.print_sdt()
    -- end
    -- 为 from_schema 设置属性 'rule_proto_name'
    if m.rule_proto_name then
      pschema.set_attribute(from_schema, "rule_proto_name", m.rule_proto_name)
    end

    --print("aaaaaaaaaaaaaaa",m.rule_proto_name)
    if m.rule_name_flag == false then
      pschema.set_attribute(from_schema, "alias", "true")
      -- find map from g_alias_rule_map
      for k, v in pairs(g_alias_rule_map) do
        --print("bbbbbbbbbbbb", m.rule_proto_name, k)
        if m.rule_proto_name == k then
          for k, f in pairs(v.field) do
            if f.rule_name and f.rule_name ~= "" and
                f.from_name and f.from_name ~= "" then
              pschema.set_field_attribute(from_schema, f.from_name, "rule_name", f.rule_name)
            end
          end
        end
      end
    end

    yalua_register_proto_layer(to_schema, from_schema, m)
  end

  yalua_register(m.from_proto_name, m.to_proto_name, adapter, init)
end

function yalua_register_init()
  if g_custom_exist then
    g_alias = require("./adapt_lua/alias")
    g_alias_rule_map["common"] = g_alias.mapping_common
    g_alias_rule_map["trans"] = g_alias.mapping_link
    g_alias_rule_map["ip"] = g_alias.mapping_ip
  end
  -- foreach g_alias_rule_map and print
  -- for k, v in pairs(g_alias_rule_map) do
  --   print(k, v)
  -- end
end

yalua_register_init()
