-- smb.lua
local mapping = {
  from_proto_name = "smb",
  to_proto_name = "smb",
  rule_proto_name = "smb",
  common_flag = true,
  field = {
    {from_name = "Serversb_version"    ,to_name = "smb_version"                ,rule_name = "version"   ,tll = 1},
    {from_name = "Clientsb_username"   ,to_name = "smb_user"                   ,rule_name = "user"      ,tll = 1},
    {from_name = "Clientpassword"      ,to_name = "smb_pwd"                    ,rule_name = "pwd"      ,tll = 1},
    {from_name = "Clientpath"          ,to_name = "smb_path"                   ,rule_name = "path"      ,tll = 1},
    {from_name = ""                    ,to_name = "smb_path_count"             ,rule_name = "path_count"      ,tll = 1},
    {from_name = "Servernativeos"      ,to_name = "smb_os"                     ,rule_name = "os"        ,tll = 1},
    {from_name = "Serveraction"        ,to_name = "smb_action"                 ,rule_name = "action"        ,tll = 1},
    {from_name = "Filename"            ,to_name = "smb_file"                   ,rule_name = "file"        ,tll = 1},
    {from_name = ""                    ,to_name = "smb_file_count"             ,rule_name = "file_count"    ,tll = 1},
    {from_name = "authType"            ,to_name = "smb_auth_type"              ,rule_name = "auth_type" ,tll = 1},
    {from_name = ""                    ,to_name = "smb_auth_type_count"        ,rule_name = "auth_type_count" ,tll = 1},
    {from_name = "auth_directory"      ,to_name = "smb_directory"              ,rule_name = "directory" ,tll = 1},
    {from_name = ""                    ,to_name = "smb_directory_count"        ,rule_name = "directory_count" ,tll = 1},
    {from_name = "FileSize"            ,to_name = "smb_filesize"               ,rule_name = "filesize"  ,tll = 1},
    {from_name = ""                    ,to_name = "smb_filesize_count"         ,rule_name = "filesize_count"  ,tll = 1},
    {from_name = "localFilename"       ,to_name = "smb_file_path"              ,rule_name = "file_path"        ,tll = 1},

  }
}
yalua_register_proto(mapping)
