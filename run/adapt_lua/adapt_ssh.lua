-- ssh.lua
local mapping = {
  from_proto_name = "ssh",
  to_proto_name = "ssh",
  rule_proto_name = "ssh",
  common_flag = true,
  field = {
    {from_name = "ServerKexInit_HASSH"         ,to_name = "ssh_hassh_server"           ,rule_name = "hassh_server"           ,tll = 1},
    {from_name = "ClientKexInit_HASSH"         ,to_name = "ssh_hassh_client"           ,rule_name = "hassh_client"           ,tll = 1},
    {from_name = ""                            ,to_name = "ssh_host_sign"              ,rule_name = "host_sign"},
    {from_name = "ServerKexGEX_GROUP_y"        ,to_name = "ssh_dsakey_y"               ,rule_name = "dsakey_y"},
    {from_name = "ServerKexGEX_GROUP_g"        ,to_name = "ssh_dsakey_g"               ,rule_name = "dsakey_g"},
    {from_name = "ServerKexGEX_GROUP_q"        ,to_name = "ssh_dsakey_q"               ,rule_name = "dsakey_q"},
    {from_name = "ServerKexGEX_GROUP_p"        ,to_name = "ssh_dsakey_p"               ,rule_name = "dsakey_p"},
    {from_name = "Server_RSA_ms"               ,to_name = "ssh_rsakey_mod"             ,rule_name = "rsakey_mod"             ,tll = 1},
    {from_name = "Server_RSA_ds"               ,to_name = "ssh_rsakey_exp"             ,rule_name = "rsakey_exp"             ,tll = 1},
    {from_name = ""                            ,to_name = "ssh_key_fingerprint_md5_server"   ,rule_name = ""},
    {from_name = "Server_Key"                  ,to_name = "ssh_key_fingerprint_sha256_server",rule_name = "key_fingerprint_sha256_server",tll = 1},
    {from_name = "Server_keyex_ms"             ,to_name = "ssh_dhkey_mod"              ,rule_name = "dhkey_mod"},
    {from_name = "Server_keyex_scy"            ,to_name = "ssh_dhkey_g"                ,rule_name = "dhkey_g"},
    {from_name = "ServerKexDHReply_PubKey"     ,to_name = "ssh_dhkey_server"           ,rule_name = "dhkey_server"},
    {from_name = "ClientKexInit_DHPubKey"      ,to_name = "ssh_dhkey_client"           ,rule_name = "dhkey_client"},
    {from_name = "SvrKexInit_compressAlgC2S"   ,to_name = "ssh_algorithm_compress_server" ,rule_name = "algorithm_compress_server" ,tll = 1},
    {from_name = "CltKexInit_CompAlgC2S"       ,to_name = "ssh_algorithm_compress_client" ,rule_name = "algorithm_compress_client" ,tll = 1},
    {from_name = "SvrKexInit_macAlgS2C"        ,to_name = "ssh_algorithm_check_server"    ,rule_name = "algorithm_check_server"    ,tll = 1},
    {from_name = "CltKexInit_MacAlgC2S"        ,to_name = "ssh_algorithm_check_client"    ,rule_name = "algorithm_check_client"    ,tll = 1},
    {from_name = "SvrKexInit_encryptAlgS2C"    ,to_name = "ssh_algorithm_encrypt_server"  ,rule_name = "algorithm_encrypt_server"  ,tll = 1},
    {from_name = "CltKexInit_EncryptAlgC2S"    ,to_name = "ssh_algorithm_encrypt_client"  ,rule_name = "algorithm_encrypt_client"  ,tll = 1},
    {from_name = "SvrKexInit_svr_HostKeyAlg"   ,to_name = "ssh_algorithm_hostkey_server"  ,rule_name = "algorithm_hostkey_server"  ,tll = 1},
    {from_name = "CltKexInit_svrhostkey_alg"   ,to_name = "ssh_algorithm_hostkey_client"  ,rule_name = "algorithm_hostkey_client"  ,tll = 1},
    {from_name = "ServerKexInit_kex_algorithms",to_name = "ssh_key_exchange_algorithm_server" ,rule_name = "key_exchange_algorithm_server" ,tll = 1},
    {from_name = "ClientKexInit_kex_algorithms",to_name = "ssh_key_exchange_algorithm_client" ,rule_name = "key_exchange_algorithm_client" ,tll = 1},
    {from_name = "ServerKexInit_Cookie"        ,to_name = "ssh_cookie_server"           ,rule_name = "cookie_server"           ,tll = 1},
    {from_name = "ClientKexInit_Cookie"        ,to_name = "ssh_cookie_client"           ,rule_name = "cookie_client"           ,tll = 1},
    {from_name = "SSHServerVersion"            ,to_name = "ssh_version_server"          ,rule_name = "version_server"          ,tll = 1},
    {from_name = "SSHClientVersion"            ,to_name = "ssh_version_client"          ,rule_name = "version_client"          ,tll = 1},
  }
}

yalua_register_proto(mapping)
