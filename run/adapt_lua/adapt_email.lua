--email.lua
local mapping = {
  from_proto_name = "email",
  to_proto_name = "email",
  rule_proto_name = "email",
  common_flag = true,
  field = {
    {from_name = "emaProtType"                  ,to_name = "email_proto_type"                                ,rule_name = "proto_type"     ,tll = 1},
    {from_name = "host"                         ,to_name = "email_host"                                      ,rule_name = "host"           ,tll = 1},
    {from_name = "banner"                       ,to_name = "email_banner"                                    ,rule_name = "banner" },
    {from_name = "login"                        ,to_name = "email_login"                                     ,rule_name = "login"          ,tll = 1},
    {from_name = "pwd"                          ,to_name = "email_pwd"                                       ,rule_name = "pwd"            ,tll = 1},
    {from_name = "startTLS"                     ,to_name = "email_starttls"                                  ,rule_name = "starttls"       ,tll = 1},
    {from_name = "auth_result"                  ,to_name = "email_auth_result"                               ,rule_name = "auth_result"         },
    {from_name = "Command"                      ,to_name = "email_cmd"                                       ,rule_name = "cmd"            ,tll = 1},
    {from_name = "count"                        ,to_name = "email_count"                                     ,rule_name = "count"          ,tll = 1},
    {from_name = "name"                         ,to_name = "email_imap_name"                                 ,rule_name = "imap_name"      ,tll = 1},
    {from_name = "vendor"                       ,to_name = "email_imap_vendor"                               ,rule_name = "imap_vendor"    ,tll = 1},
    {from_name = "ver"                          ,to_name = "email_imap_version"                              ,rule_name = "imap_version"   ,tll = 1},
    {from_name = "os"                           ,to_name = "email_imap_os"                                   ,rule_name = "imap_os"        ,tll = 1},
    {from_name = "osVer"                        ,to_name = "email_imap_os_version"                           ,rule_name = "imap_os_version" ,tll = 1},
    {from_name = "received"                     ,to_name = "email_received"                                  ,rule_name = "received"       ,tll = 1},
    {from_name = "spf"                          ,to_name = "email_spf"                                       ,rule_name = "spf"             },
    {from_name = "deliveredTo"                  ,to_name = "email_delivered_to"                              ,rule_name = "delivered_to"   ,tll = 1},
    {from_name = "xOriIP"                       ,to_name = "email_x_original_ip"                             ,rule_name = "x_original_ip"  ,tll = 1},
    {from_name = "senderEmail"                  ,to_name = "email_sender"                                    ,rule_name = "sender"         ,tll = 1},
    {from_name = "sender_alias"                 ,to_name = "email_sender_alias"                              ,rule_name = "sender_alias"   ,tll = 1},
    {from_name = "sender_domain"                ,to_name = "email_sender_domain"                             ,rule_name = "sender_domain"  ,tll = 1},
    {from_name = "sender_software"              ,to_name = "email_sender_software"                           ,rule_name = "sender_software" ,tll = 1},
    {from_name = "envelope_from"                ,to_name = "email_envelope_from"                             ,rule_name = "envelope_from"  ,tll = 1},
    {from_name = "envelope_from_domain"         ,to_name = "email_envelope_from_domain"                      ,rule_name = "envelope_from_domain"       ,tll = 1},
    {from_name = "envelope_from_domain_count"   ,to_name = "email_envelope_from_domain_count"                ,rule_name = "envelope_from_domain_count" ,tll = 1},
    {from_name = "FromIp"                       ,to_name = "email_from_ip"                                   ,rule_name = "from_ip"        ,tll = 1},
    {from_name = "from_ip_count"                ,to_name = "email_from_ip_count"                             ,rule_name = "from_ip_count" ,tll = 1},
    {from_name = "FromDom"                      ,to_name = "email_from_domain"                               ,rule_name = "from_domain"    ,tll = 1},
    {from_name = "from_domain_count"            ,to_name = "email_from_domain_count"                         ,rule_name = "from_domain_count"  ,tll = 1},
    {from_name = "FromAsn"                      ,to_name = "email_from_asn"                                  ,rule_name = "from_asn"       ,tll = 1},
    {from_name = "FromCountry"                  ,to_name = "email_from_country"                              ,rule_name = "from_country"   ,tll = 1},
    {from_name = "rcvrEmail"                    ,to_name = "email_receiver"                                  ,rule_name = "receiver"       ,tll = 1},
    {from_name = "rcvrEmail"                    ,to_name = "email_receiver_count"                            ,rule_name = "receiver_count" },
    {from_name = "receiver_alias"               ,to_name = "email_receiver_alias"                            ,rule_name = "receiver_alias" ,tll = 1},
    {from_name = "rcvrDom"                      ,to_name = "email_receiver_domain"                           ,rule_name = "receiver_domain" },
    {from_name = "rcptTo"                       ,to_name = "email_envelope_to"                               ,rule_name = "envelope_to"        ,tll = 1},
    {from_name = "rcptToDom"                    ,to_name = "email_envelope_to_domain"                        ,rule_name = "envelope_to_domain" ,tll = 1},
    {from_name = "rcptToDomCnt"                 ,to_name = "email_envelope_to_domain_count"                  ,rule_name = "envelope_to_domain_count" ,tll = 1},
    {from_name = "ByIP"                         ,to_name = "email_by_ip"                                     ,rule_name = "by_ip"          ,tll = 1},
    {from_name = "by_ip_count"                  ,to_name = "email_by_ip_count"                               ,rule_name = "by_ip_count"                                                      ,rule_name = "" },
    {from_name = "ByDom"                        ,to_name = "email_by_domain"                                 ,rule_name = "by_domain"      ,tll = 1},
    {from_name = "by_domain_count"              ,to_name = "email_by_domain_count"                           ,rule_name = "by_domain_count" },
    {from_name = "ByAsn"                        ,to_name = "email_by_asn"                                    ,rule_name = "by_asn"         ,tll = 1},
    {from_name = "ByCountry"                    ,to_name = "email_by_country"                                ,rule_name = "by_country"     ,tll = 1},
    {from_name = "user_agent"                   ,to_name = "email_user_agent"                                ,rule_name = "user_agent"     ,tll = 1},
    {from_name = "resentFrom"                   ,to_name = "email_resent_from"                               ,rule_name = "resent_from"    ,tll = 1},
    {from_name = "resentTo"                     ,to_name = "email_resent_to"                                 ,rule_name = "resent_to"      ,tll = 1},
    {from_name = "resentDate"                   ,to_name = "email_resent_date"                               ,rule_name = "resent_date"    ,tll = 1},
    {from_name = "resent_agent"                 ,to_name = "email_resent_agent"                              ,rule_name = "resent_agent"   ,tll = 1},
    {from_name = "CC"                           ,to_name = "email_cc"                                        ,rule_name = "cc"             ,tll = 1},
    {from_name = "cc_alias"                     ,to_name = "email_cc_alias"                                  ,rule_name = "cc_alias"       ,tll = 1},
    {from_name = "BCC"                          ,to_name = "email_bcc"                                       ,rule_name = "bcc"            ,tll = 1},
    {from_name = "reply"                        ,to_name = "email_reply"                                     ,rule_name = "reply"          ,tll = 1},
    {from_name = "date"                         ,to_name = "email_date"                                      ,rule_name = "date"           ,tll = 1},
    {from_name = "subj"                         ,to_name = "email_subject"                                   ,rule_name = "subject"        ,tll = 1},
    {from_name = "subject_count"                ,to_name = "email_subject_count"                             ,rule_name = "subject_count" },
    {from_name = "xMai"                         ,to_name = "email_x_mailer"                                  ,rule_name = "x_mailer"       ,tll = 1},
    {from_name = "x_mailer_count"               ,to_name = "email_x_mailer_count"                            ,rule_name = "x_mailer_count" },
    {from_name = "content"                      ,to_name = "email_content"                                   ,rule_name = "content"        ,tll = 1},
    {from_name = "conType"                      ,to_name = "email_content_type"                              ,rule_name = "content_type"   ,tll = 1},
    {from_name = ""                             ,to_name = "email_content_type_count"                        ,rule_name = "content_type_count"  },
    {from_name = "body"                         ,to_name = "email_body"                                      ,rule_name = "body"           ,tll = 1},
    {from_name = "body_md5"                     ,to_name = "email_body_md5"                                  ,rule_name = "body_md5"},
    {from_name = "body_length"                  ,to_name = "email_body_length"                               ,rule_name = "body_length"    ,tll = 1},
    {from_name = "bodyURL"                      ,to_name = "email_body_url"                                  ,rule_name = "body_url"       ,tll = 1},
    {from_name = "body_url_count"               ,to_name = "email_body_url_count"                            ,rule_name = "body_url_count" },
    {from_name = "bodyTraEnc"                   ,to_name = "email_body_encoding"                             ,rule_name = "body_encoding"  ,tll = 1},
    {from_name = "bodyTexCha"                   ,to_name = "email_body_charset"                              ,rule_name = "body_charset"   ,tll = 1},
    {from_name = "bodyType"                     ,to_name = "email_body_type"                                 ,rule_name = "body_type"      ,tll = 1},
    {from_name = "bodyTypeCnt"                  ,to_name = "email_body_type_count"                           ,rule_name = "body_type_count" },
    {from_name = "emaInd"                       ,to_name = "email_index"                                     ,rule_name = "index"          ,tll = 1},
    {from_name = "attFileName"                  ,to_name = "email_attachment_filename"                       ,rule_name = "attachment_filename"     ,tll = 1},
    {from_name = "attConSize"                   ,to_name = "email_attachment_length"                         ,rule_name = "attachment_length"       ,tll = 1},
    {from_name = "attFileNameCnt"               ,to_name = "email_attachment_count"                          ,rule_name = "attachment_count",tll = 1},
    {from_name = "attType"                      ,to_name = "email_attachment_content_type"                   ,rule_name = "attachment_content_type" ,tll = 1},
    {from_name = "attTypeCnt"                   ,to_name = "email_attachment_content_type_count"             ,rule_name = "attachment_content_type_count" ,tll = 1},
    {from_name = "attMD5"                       ,to_name = "email_attachment_md5"                            ,rule_name = "attachment_md5"          ,tll = 1},
    {from_name = "attMD5Cnt"                    ,to_name = "email_attachment_md5_count"                      ,rule_name = "attachment_md5_count"  ,tll = 1},
    {from_name = "attachment_md5_count"         ,to_name = "email_attachment_path"                           ,rule_name = "attachment_path" },
    {from_name = "headSet"                      ,to_name = "email_header_set"                                ,rule_name = "header_set"     ,tll = 1},
    {from_name = "header_set_count"             ,to_name = "email_header_set_count"                          ,rule_name = "header_set_count",tll = 1},
    {from_name = "msgID"                        ,to_name = "email_msg_id"                                    ,rule_name = "msg_id"         ,tll = 1},
    {from_name = "msgIDCnt"                     ,to_name = "email_msg_id_count"                              ,rule_name = "msg_id_count" ,tll = 1},
    {from_name = "mimeVer"                      ,to_name = "email_mime_version"                              ,rule_name = "mime_version"   ,tll = 1},
    {from_name = "mimeVerCnt"                   ,to_name = "email_mime_version_count"                        ,rule_name = "mime_version_count"    ,tll = 1},
    {from_name = "localFilename"                ,to_name = "email_file_path"                                 ,rule_name = "file_path" },

  }
}
yalua_register_proto(mapping)
