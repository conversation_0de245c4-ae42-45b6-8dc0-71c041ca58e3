-- -- common 映射文件，只要调用接口即可，map 需要放在 init 脚本中，因为其他脚本需要调用
-- -- adapt_init.lua
local mapping_common = {
  from_proto_name = "common",
  to_proto_name = "common",
  rule_proto_name = "common",
  rule_name_flag = false,       -- 如果 false ，去 init.lua 的静态表里面找 rule_name
  field = {
    {from_name = "tags_site"                ,to_name = "tags_site"               ,tll = 1},
    {from_name = "tags_unit"                ,to_name = "tags_unit"               ,tll = 1},
    {from_name = "tags_task"                ,to_name = "tags_task"               ,tll = 1},
    {from_name = "tags_rule"                ,to_name = "tags_rule"               ,tll = 1},
    {from_name = "tags_anomaly"             ,to_name = "tags_anomaly"            ,tll = 1},
    {from_name = "tags_threat"              ,to_name = "tags_threat"             ,tll = 1},
    {from_name = "tags_user"                ,to_name = "tags_user"               ,tll = 1},
    {from_name = "tags_attack"              ,to_name = "tags_attack"             ,tll = 1},
    {from_name = "time_capture"             ,to_name = "time_capture"            ,tll = 1},
    {from_name = "tags_isp"                 ,to_name = "tags_isp"                ,tll = 1},
    {from_name = "mobile_imsi"              ,to_name = "mobile_imsi"             ,tll = 1},
    {from_name = "mobile_imei"              ,to_name = "mobile_imei"             ,tll = 1},
    {from_name = "mobile_msisdn"            ,to_name = "mobile_msisdn"           ,tll = 1},
    {from_name = "mobile_interface"         ,to_name = "mobile_interface"        ,tll = 1},
    {from_name = "mobile_srcip"             ,to_name = "mobile_srcip"            ,tll = 1},
    {from_name = "mobile_srcport"           ,to_name = "mobile_srcport"          ,tll = 1},
    {from_name = "mobile_destip"            ,to_name = "mobile_destip"           ,tll = 1},
    {from_name = "mobile_destport"          ,to_name = "mobile_destport"         ,tll = 1},
    {from_name = "mobile_apn"               ,to_name = "mobile_apn"              ,tll = 1},
    {from_name = "mobile_ecgi"              ,to_name = "mobile_ecgi"             ,tll = 1},
    {from_name = "mobile_tai"               ,to_name = "mobile_tai"              ,tll = 1},
    {from_name = "mobile_teid"              ,to_name = "mobile_teid"             ,tll = 1},
    {from_name = "mobile_srcisp"            ,to_name = "mobile_srcisp"           ,tll = 1},
    {from_name = "mobile_destisp"           ,to_name = "mobile_destisp"          ,tll = 1},
    {from_name = "mobile_local_province"    ,to_name = "mobile_local_province"   ,tll = 1},
    {from_name = "mobile_local_city"        ,to_name = "mobile_local_city"       ,tll = 1},
    {from_name = "mobile_owner_province"    ,to_name = "mobile_owner_province"   ,tll = 1},
    {from_name = "mobile_owner_city"        ,to_name = "mobile_owner_city"       ,tll = 1},
    {from_name = "mobile_roaming_type"      ,to_name = "mobile_roaming_type"     ,tll = 1},
    {from_name = "mobile_rat"               ,to_name = "mobile_rat"              ,tll = 1},
    {from_name = "map_stream_id"            ,to_name = "map_stream_id"           ,tll = 1},
    {from_name = "ip_proto"                 ,to_name = "ip_proto"           ,tll = 1},
    {from_name = "proto_info"               ,to_name = "proto_info"           ,tll = 1},
  }
}
yalua_register_proto(mapping_common)
