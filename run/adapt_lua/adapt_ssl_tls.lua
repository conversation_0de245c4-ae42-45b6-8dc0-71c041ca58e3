-- ssl_tls.lua
local mapping = {
  from_proto_name = "ssl_n",
  to_proto_name = "tls",
  rule_proto_name = "tls",
  common_flag = true,
  field = {
    {from_name = "ClientProtocolVersion"        ,to_name = "tls_version_client"                    ,rule_name = "version_client"                 ,tll = 1},
    {from_name = "ServerProtocolVersion"        ,to_name = "tls_version_server"                    ,rule_name = "version_server"                 ,tll = 1},
    {from_name = "ClientHelloLength"            ,to_name = "tls_handshake_length_client"           ,rule_name = "handshake_length_client"        ,tll = 1},
    {from_name = "ServerHelloLength"            ,to_name = "tls_handshake_length_server"           ,rule_name = "handshake_length_server"        ,tll = 1},
    {from_name = "ClientGMTUnixTime"            ,to_name = "tls_handshake_random_time_client"      ,rule_name = "handshake_random_time_client"   ,tll = 1},
    {from_name = "ServerGMTUnixTime"            ,to_name = "tls_handshake_random_time_server"      ,rule_name = "handshake_random_time_server"   ,tll = 1},
    {from_name = "ClientRandomBytes"            ,to_name = "tls_handshake_random_bytes_client"     ,rule_name = "handshake_random_bytes_client"  ,tll = 1},
    {from_name = "ServerRandomBytes"            ,to_name = "tls_handshake_random_bytes_server"     ,rule_name = "handshake_random_bytes_server"  ,tll = 1},
    {from_name = "ClientSessionID"              ,to_name = "tls_session_id_client"                 ,rule_name = "session_id_client"              ,tll = 1},
    {from_name = "ServerSessionID"              ,to_name = "tls_session_id_server"                 ,rule_name = "session_id_server"              ,tll = 1},
    {from_name = "ClientCipherSuites"           ,to_name = "tls_ciphersuites_client"               ,rule_name = "ciphersuites_client"            ,tll = 1},
    {from_name = "ClientCipherSuiteCnt"         ,to_name = "tls_ciphersuites_client_count"         ,rule_name = "ciphersuites_client_count"      ,tll = 1},
    {from_name = "ServerCipherSuite"            ,to_name = "tls_ciphersuite_server"                ,rule_name = "ciphersuite_server"             ,tll = 1},
    {from_name = "ClientCompressionMethods"     ,to_name = "tls_compress_methods_client"           ,rule_name = "compress_methods_client"        ,tll = 1},
    {from_name = "ServerCompressionMethod"      ,to_name = "tls_compress_method_server"            ,rule_name = "compress_method_server"         ,tll = 1},
    {from_name = "ClientExtensions"             ,to_name = "tls_extend_types_client"               ,rule_name = "extend_types_client"            ,tll = 1},
    {from_name = "clientExtensionsCount"        ,to_name = "tls_extend_client_count"               ,rule_name = "extend_client_count"            ,tll = 1},
    {from_name = "ServerExtensions"             ,to_name = "tls_extend_types_server"               ,rule_name = "extend_types_server"            ,tll = 1},
    {from_name = "serverExtensionsCount"        ,to_name = "tls_extend_server_count"               ,rule_name = "extend_server_count"            ,tll = 1},
    {from_name = "extGrease"                    ,to_name = "tls_extend_grease"                     ,rule_name = "extend_grease"                  ,tll = 1},
    {from_name = "JA3C"                         ,to_name = "tls_ja3_client"                        ,rule_name = "ja3_client"                     ,tll = 1},
    {from_name = "JA3S"                         ,to_name = "tls_ja3s_server"                       ,rule_name = "ja3s_server"                    ,tll = 1},
    {from_name = "joyClient"                    ,to_name = "tls_joy_client"                        ,rule_name = "joy_client"                     ,tll = 1},
    {from_name = "joysServer"                   ,to_name = "tls_joys_server"                       ,rule_name = "joys_server"                    ,tll = 1},
    {from_name = "ClientSessionTick"            ,to_name = "tls_session_ticket_client"             ,rule_name = "session_ticket_client"          ,tll = 1},
    {from_name = "ServerSessionTick"            ,to_name = "tls_session_ticket_server"             ,rule_name = "session_ticket_server"          ,tll = 1},
    {from_name = "ServerName"                   ,to_name = "tls_sni"                               ,rule_name = "sni"                            ,tll = 1},
    {from_name = "ServerNameAttr"               ,to_name = "tls_sni_type"                          ,rule_name = "sni_type"                       ,tll = 1},
    {from_name = "ClientCertHashes"             ,to_name = "tls_cert_hashes_client"                ,rule_name = "cert_hashes_client"             ,tll = 1},
    {from_name = "ServerCertHashes"             ,to_name = "tls_cert_hashes_server"                ,rule_name = "cert_hashes_server"             ,tll = 1},
    {from_name = "ClientCertificatesNums"       ,to_name = "tls_cert_client_count"                 ,rule_name = "cert_client_count"              ,tll = 1},
    {from_name = "ServerCertificatesNums"       ,to_name = "tls_cert_server_count"                 ,rule_name = "cert_server_count"              ,tll = 1},
    {from_name = "ClientCertificatesLength"     ,to_name = "tls_cert_client_length"                ,rule_name = "cert_client_length"             ,tll = 1},
    {from_name = "ServerCertificatesLength"     ,to_name = "tls_cert_server_length"                ,rule_name = "cert_server_length"             ,tll = 1},
    {from_name = "ClientECDHCurveType"          ,to_name = "tls_extend_ec_format_client"           ,rule_name = "extend_ec_format_client"        ,tll = 1},
    {from_name = "srvExtECPoiFor"               ,to_name = "tls_extend_ec_format_server"           ,rule_name = "extend_ec_format_server"        ,tll = 1},
    {from_name = "ClientECDHNamedCurve"         ,to_name = "tls_extend_ec_groups_client"           ,rule_name = "extend_ec_groups_client"        ,tll = 1},
    {from_name = "srvExtECGroups"               ,to_name = "tls_extend_ec_groups_server"           ,rule_name = "extend_ec_groups_server"        ,tll = 1},
    {from_name = "certPath"                     ,to_name = "tls_cert_path"                         ,rule_name = "cert_path"                      ,tll = 1},
    {from_name = "tlsCertExist"                 ,to_name = "tls_cert_exist"                        ,rule_name = "cert_exist"                 ,tll = 1},
    {from_name = "tlsCertIntact"                ,to_name = "tls_cert_intact"                       ,rule_name = "cert_intact"                ,tll = 1},
    {from_name = "certDaysRemaining"            ,to_name = "tls_leaf_cert_days_remaining"          ,rule_name = "leaf_cert_days_remaining"   ,tll = 1},
    
  }
}
yalua_register_proto(mapping)
