-- rdp.lua
local mapping = {
  from_proto_name = "rdp",
  to_proto_name = "rdp",
  rule_proto_name = "rdp",
  common_flag = true,
  field = {
    {from_name = "ass_path"         ,to_name = "rdp_access_path"        ,rule_name = "access_path"        ,tll = 1},
    {from_name = "program"          ,to_name = "rdp_application"        ,rule_name = "application"        ,tll = 1},
    {from_name = "domain"           ,to_name = "rdp_domain"             ,rule_name = "domain"             ,tll = 1},
    {from_name = "clientname"       ,to_name = "rdp_host"               ,rule_name = "host"               ,tll = 1},
    {from_name = "username"         ,to_name = "rdp_user"               ,rule_name = "user"               ,tll = 1},
    {from_name = "msgchannelid"     ,to_name = "rdp_virtual_channel_id" ,rule_name = "virtual_channel_id" ,tll = 1},
    {from_name = "version"          ,to_name = "rdp_version"            ,rule_name = "version"            ,tll = 1},


  }
}
yalua_register_proto(mapping)