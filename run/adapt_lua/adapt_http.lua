-- http.lua
local mapping = {
  from_proto_name = "http",
  to_proto_name = "http",
  rule_proto_name = "http",
  common_flag = true,
  field = {
    {from_name = "Version"            ,to_name = "http_version_client"                        ,rule_name = "version_client"        ,tll = 1},
    {from_name = ""                   ,to_name = "http_version_client_count"                  ,rule_name = "version_client_count" ,tll = 1},
    {from_name = "Method"             ,to_name = "http_method"                                ,rule_name = "method"           ,tll = 1},
    {from_name = ""                   ,to_name = "http_method_count"                          ,rule_name = "method_count" ,tll = 1},
    {from_name = "Referer"            ,to_name = "http_referer"                               ,rule_name = "referer"              ,tll = 1},
    {from_name = "reqHeadFields"      ,to_name = "http_header_client"                         ,rule_name = "header_client"         ,tll = 1},
    {from_name = "reqHeadFieldsMD5"   ,to_name = "http_header_client_md5"                     ,rule_name = "header_client_md5" ,tll = 1},
    {from_name = "reqHeadFieldsCount" ,to_name = "http_header_client_md5_count"               ,rule_name = "header_client_md5_count" ,tll = 1},
    {from_name = "conDispUp"          ,to_name = "http_content_disposition_client"            ,rule_name = "content_disp_client"   ,tll = 1},
    {from_name = "Accept"             ,to_name = "http_accept"                                ,rule_name = "accept"         ,tll = 1},
    {from_name = "Accept-Language"    ,to_name = "http_accept_language"                       ,rule_name = "accept_language",tll = 1},
    {from_name = "Accept-Encoding"    ,to_name = "http_accept_encoding"                       ,rule_name = "accept_encoding",tll = 1},
    {from_name = "Authorization"      ,to_name = "http_auth"                                  ,rule_name = "auth"             ,tll = 1},
    {from_name = ""                   ,to_name = "http_auth_count"                            ,rule_name = "auth_count" ,tll = 1},
    {from_name = "Username"           ,to_name = "http_auth_user"                             ,rule_name = "user"             ,tll = 1},
    {from_name = ""                   ,to_name = "http_auth_user_count"                       ,rule_name = "auth_user_count" ,tll = 1},
    {from_name = "From"               ,to_name = "http_from"                                  ,rule_name = "from"             ,tll = 1},
    {from_name = "Host"               ,to_name = "http_host"                                  ,rule_name = "host"             ,tll = 1},
    {from_name = ""                   ,to_name = "http_host_count"                            ,rule_name = "host_count" ,tll = 1},
    {from_name = "URI"                ,to_name = "http_uri"                                   ,rule_name = "uri"              ,tll = 1},
    {from_name = ""                   ,to_name = "http_uri_count"                             ,rule_name = "uri_count" ,tll = 1},
    {from_name = "URI-Path"           ,to_name = "http_uri_path"                              ,rule_name = "uri_path"         ,tll = 1},
    {from_name = ""                   ,to_name = "http_uri_path_count"                        ,rule_name = "uri_path_count" ,tll = 1},
    {from_name = ""                   ,to_name = "http_uri_key"                               ,rule_name = "uri_key" ,tll = 1},
    {from_name = ""                   ,to_name = "http_uri_key_count"                         ,rule_name = "uri_key_count" ,tll = 1},
    {from_name = "uriSearch"          ,to_name = "http_uri_search"                            ,rule_name = "uri_search"       ,tll = 1},
    {from_name = "Range"              ,to_name = "http_range"                                 ,rule_name = "range"},
    {from_name = "User-Agent"         ,to_name = "http_user_agent"                            ,rule_name = "user_agent"       ,tll = 1},
    {from_name = ""                   ,to_name = "http_user_agent_count"                      ,rule_name = "user_agent_count" ,tll = 1},
    {from_name = "reqBody"            ,to_name = "http_body_client"                           ,rule_name = "body_client"           ,tll = 1},
    {from_name = "Content-MD5"        ,to_name = "http_body_client_md5"                       ,rule_name = "body_client_md5"},
    {from_name = "Cookie"             ,to_name = "http_cookie"                                ,rule_name = "cookie"           ,tll = 1},
    {from_name = "CookieKeys"         ,to_name = "http_cookie_key"                            ,rule_name = "cookie_key"       ,tll = 1},
    {from_name = ""                   ,to_name = "http_cookie_key_count"                      ,rule_name = "cookie_key_count" ,tll = 1},
    {from_name = "imei"               ,to_name = "http_imei"                                  ,rule_name = "imei"           ,tll = 1},
    {from_name = "imsi"               ,to_name = "http_imsi"                                  ,rule_name = "imsi"           ,tll = 1},
    {from_name = "Content-Encoding-c" ,to_name = "http_content_encoding_client"               ,rule_name = "content_encoding_client"    ,tll = 1},
    {from_name = "reqContent-Length"  ,to_name = "http_content_length_client"                 ,rule_name = "content_length_client"    ,tll = 1},
    {from_name = "X-Forwarded-For"    ,to_name = "http_xforwarded_for"                        ,rule_name = "xforwarded_for"           ,tll = 1},
    {from_name = ""                   ,to_name = "http_xforwarded_for_count"                  ,rule_name = "xforwarded_for_count" ,tll = 1},
    {from_name = "rspVersion"         ,to_name = "http_version_server"                        ,rule_name = "version_server"        ,tll = 1},
    {from_name = ""                   ,to_name = "http_version_server_count"                  ,rule_name = "version_server_count" ,tll = 1},
    {from_name = "rspHeadFields"      ,to_name = "http_header_server"                         ,rule_name = "header_server"         ,tll = 1},
    {from_name = "rspHeadFieldsMD5"   ,to_name = "http_header_server_md5"                     ,rule_name = "header_server_md5" ,tll = 1},
    {from_name = "reqHeadFieldsCount" ,to_name = "http_header_server_count"                   ,rule_name = "header_server_count" ,tll = 1},
    {from_name = "rspBody"            ,to_name = "http_body_server"                           ,rule_name = "body_server"           ,tll = 1},
    {from_name = "Content-MD5"        ,to_name = "http_body_server_md5"                       ,rule_name = "body_server_md5" ,tll = 1},
    {from_name = ""                   ,to_name = "http_body_server_md5_count"                 ,rule_name = "body_server_md5_count" ,tll = 1},
    {from_name = "conDispDown"        ,to_name = "http_content_disposition_server"            ,rule_name = "content_disp_server"   },
    {from_name = "Content-Encoding-s" ,to_name = "http_content_encoding_server"               ,rule_name = "content_encoding_server"    ,tll = 1},
    {from_name = ""                   ,to_name = "http_content_encoding_server_count"         ,rule_name = "content_encoding_server_count" ,tll = 1},
    {from_name = "Entity_Title_Head"  ,to_name = "http_title"                                 ,rule_name = "title"},
    {from_name = "Url"                ,to_name = "http_loc"                                   ,rule_name = "loc"},
    {from_name = "Vary"               ,to_name = "http_vary"                                  ,rule_name = "vary"             ,tll = 1},
    {from_name = "Content-Language"   ,to_name = "http_content_language"                      ,rule_name = "content_language"      ,tll = 1},
    {from_name = "Server"             ,to_name = "http_server"                                ,rule_name = "server"           ,tll = 1},
    {from_name = ""                   ,to_name = "http_server_count"                          ,rule_name = "server_count" ,tll = 1},
    {from_name = "Set-Cookie"         ,to_name = "http_set_cookie_key"                        ,rule_name = "set_cookie_key"   ,tll = 1},
    {from_name = "setCookieValue"     ,to_name = "http_set_cookie_value"                      ,rule_name = "set_cookie_value" ,tll = 1},
    {from_name = "rspContent-Length"  ,to_name = "http_content_length_server"                 ,rule_name = "content_length_server"    ,tll = 1},
    {from_name = "X-Powered-By"       ,to_name = "http_x_powered_by"                          ,rule_name = "x_powered_by"           ,tll = 1},
    {from_name = "Date"               ,to_name = "http_date"                                  ,rule_name = "date"             ,tll = 1},
    {from_name = "Transfer-Encoding"  ,to_name = "http_transfer_encoding"                     ,rule_name = "transfer_encoding"        ,tll = 1},
    {from_name = "X_Sinkhole"         ,to_name = "http_xsinkhole"                             ,rule_name = "xsinkhole"        ,tll = 1},
    {from_name = "Via"                ,to_name = "http_via"                                   ,rule_name = "via"              ,tll = 1},
    {from_name = "Via-Count"          ,to_name = "http_via_count"                             ,rule_name = "statuscode" ,tll = 1},
    {from_name = "Status"             ,to_name = "http_statuscode"                            ,rule_name = "statuscode"       ,tll = 1},
    {from_name = ""                   ,to_name = "http_statuscode_count"                      ,rule_name = "statuscode_count" ,tll = 1},
    {from_name = "localFilename"      ,to_name = "http_attachment_path"                       ,rule_name = "attachment_path"      ,tll = 1},
  }
}
yalua_register_proto(mapping)
