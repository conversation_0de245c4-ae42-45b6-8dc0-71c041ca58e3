-- ftp.lua
local mapping = {
  from_proto_name = "ftp",
  to_proto_name = "ftp",
  rule_proto_name = "ftp",
  common_flag = true,
  field = {
    {from_name = "DataPort"               ,to_name = "ftp_port"                    ,rule_name = "port" },
    {from_name = "Server_IP"              ,to_name = "ftp_server_ip"               ,rule_name = "server_ip"  ,tll = 1},
    {from_name = "hostName"               ,to_name = "ftp_server_name"             ,rule_name = "server_name"  ,tll = 1},
    {from_name = "Filename"               ,to_name = "ftp_file_name"               ,rule_name = "file_name"  ,tll = 1},
    {from_name = "Data_reallen"           ,to_name = "ftp_file_size"               ,rule_name = "file_size"  ,tll = 1},
    {from_name = "localFilename"          ,to_name = "ftp_file_path"               ,rule_name = "file_path"  ,tll = 1 },
    {from_name = "ftp_file_content"       ,to_name = "ftp_file_content"            ,rule_name = "file_content" },
    {from_name = "Content_type"           ,to_name = "ftp_content_type"            ,rule_name = "content_type"  ,tll = 1},
    {from_name = "ftp_operations"         ,to_name = "ftp_operations"              ,rule_name = "operations" },
    {from_name = "Username"               ,to_name = "ftp_user"                    ,rule_name = "user"      ,tll = 1},
    {from_name = "Password"               ,to_name = "ftp_pwd"                     ,rule_name = "pwd"       ,tll = 1},
    {from_name = "Control_cmd"            ,to_name = "ftp_request_cmd"             ,rule_name = "request_cmd"   ,tll = 1},
    {from_name = "Control_args"           ,to_name = "ftp_request_param"           ,rule_name = "request_param"   ,tll = 1},
    {from_name = "Return_code"            ,to_name = "ftp_response_code"           ,rule_name = "response_code" ,tll = 1},
    {from_name = "Return_content"         ,to_name = "ftp_response_argument"       ,rule_name = "response_argument"  ,tll = 1},
    {from_name = "LoginStatus"            ,to_name = "ftp_login_flag"              ,rule_name = "login_flag",tll = 1},

  }
}
yalua_register_proto(mapping)