-- ip.lua
local mapping = {
  from_proto_name = "ip",
  to_proto_name = "ip",
  rule_proto_name = "ip",
  rule_name_flag = false,
  field = {
    {from_name = "ipVer"             ,to_name = "ip_version"                   ,rule_name = "version"                      },
    {from_name = "srcAddr"           ,to_name = "ip_source"                    ,rule_name = "source"                       },
    {from_name = "srcAddrV6"         ,to_name = "ipv6_source"                  },
    {from_name = "dstAddr"           ,to_name = "ip_destination"               ,rule_name = "destination"                  },
    {from_name = "dstAddrV6"         ,to_name = "ipv6_destination"             },
    {from_name = "srcPort"           ,to_name = "port_source"                  },
    {from_name = "dstPort"           ,to_name = "port_destination"             },
    {from_name = "protNum"           ,to_name = "ip_proto"                     },
    {from_name = "srcCountry"        ,to_name = "ip_country_source"            ,rule_name = "country_source"               },
    {from_name = "srcState"          ,to_name = "ip_state_source"              ,rule_name = "state_source"                 },
    {from_name = "srcCity"           ,to_name = "ip_city_source"               ,rule_name = "city_source"                  },
    {from_name = "srcLongitude"      ,to_name = "ip_longitude_source"          ,rule_name = "longitude_source"             },
    {from_name = "srcLatitude"       ,to_name = "ip_latitude_source"           ,rule_name = "latitude_source"              },
    {from_name = "srcISP"            ,to_name = "ip_isp_source"                ,rule_name = "isp_source"                   },
    {from_name = "srcASN"            ,to_name = "ip_asn_source"                ,rule_name = "asn_source"                   },
    {from_name = "dstCountry"        ,to_name = "ip_country_destination"       ,rule_name = "country_destination"          },
    {from_name = "dstState"          ,to_name = "ip_state_destination"         ,rule_name = "state_destination"            },
    {from_name = "dstCity"           ,to_name = "ip_city_destination"          ,rule_name = "city_destination"             },
    {from_name = "dstLongitude"      ,to_name = "ip_longitude_destination"     ,rule_name = "longitude_destination"        },
    {from_name = "dstLatitude"       ,to_name = "ip_latitude_destination"      ,rule_name = "latitude_destination"         },
    {from_name = "dstISP"            ,to_name = "ip_isp_destination"           ,rule_name = "isp_destination"              },
    {from_name = "dstASN"            ,to_name = "ip_asn_destination"           ,rule_name = "asn_destination"              },

    {from_name = "protInfo"          ,to_name = "ip_proto_path"                     },
    {from_name = "protType"          ,to_name = "ip_base_proto"                     },
    {from_name = "protName"          ,to_name = "ip_upper_proto"               ,rule_name = "upper_proto"                  },
    {from_name = "appDirec"          ,to_name = "ip_direction"                 ,rule_name = "direction"                    },
    {from_name = "begTime"           ,to_name = "ip_begin_time"                ,rule_name = "begin_time"                   },
    {from_name = "endTime"           ,to_name = "ip_end_time"                  ,rule_name = "end_time"                     },
    {from_name = "comDur"            ,to_name = "ip_duration"                  ,rule_name = "duration"                     },
    {from_name = "firTtlByCli"       ,to_name = "ip_ttl_source"                ,rule_name = "ttl_source"                   },
    {from_name = "firTtlBySrv"       ,to_name = "ip_ttl_destination"           ,rule_name = "ttl_destination"              },
    {from_name = "pktNum"            ,to_name = "ip_packets"                   ,rule_name = "packets"                      },
    {from_name = "upLinkPktNum"      ,to_name = "ip_packets_source"            ,rule_name = "packets_source"               },
    {from_name = "downLinkPktNum"    ,to_name = "ip_packets_destination"       ,rule_name = "packets_destination"          },
    {from_name = "sesBytes"          ,to_name = "ip_bytes"                     ,rule_name = "bytes"                        },
    {from_name = "upSesBytes"        ,to_name = "ip_bytes_source"              ,rule_name = "bytes_source"                 },
    {from_name = "downSesBytes"      ,to_name = "ip_bytes_destination"         ,rule_name = "bytes_destination"            },
    {from_name = "sesBytesRatio"     ,to_name = "ip_bytes_ratio"               ,rule_name = "bytes_ratio"                  },
    {from_name = "payLen"            ,to_name = "ip_data_bytes"                ,rule_name = "data_bytes"                   },
    {from_name = "upPayLen"          ,to_name = "ip_data_bytes_source"         ,rule_name = "data_bytes_source"            },
    {from_name = "downPayLen"        ,to_name = "ip_data_bytes_destination"    ,rule_name = "data_bytes_destination"       },
    {from_name = "payLenRatio"       ,to_name = "ip_data_bytes_ratio"          ,rule_name = "data_bytes_ratio"             },
    {from_name = "desBytes"          ,to_name = "ip_desired_bytes"             ,rule_name = "desired_bytes"                },
    {from_name = "upLinkDesBytes"    ,to_name = "ip_desired_bytes_source"      ,rule_name = "desired_bytes_source"         },
    {from_name = "downLinkDesBytes"  ,to_name = "ip_desired_bytes_destination" ,rule_name = "desired_bytes_destination"    },
    {from_name = "stream"            ,to_name = "ip_stream"                    ,rule_name = "stream"                       },
    {from_name = "upLinkStream"      ,to_name = "ip_stream_source"             ,rule_name = "stream_source"                },
    {from_name = "downLinkStream"    ,to_name = "ip_stream_destination"        ,rule_name = "stream_destination"           },

  }
}
yalua_register_proto(mapping)
