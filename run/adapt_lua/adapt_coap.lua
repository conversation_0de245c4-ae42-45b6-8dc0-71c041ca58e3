-- dns.lua
local mapping = {
  from_proto_name = "coap",
  to_proto_name = "coap",
  rule_proto_name = "coap",
  common_flag = true,
  field = {
    {from_name = "coap_req_type"                      ,to_name = "coap_req_type"                             ,rule_name = "req_type"                           ,tll = 1},
    {from_name = "coap_req_code"                      ,to_name = "coap_req_code"                             ,rule_name = "req_code"                           ,tll = 1},
    {from_name = "coap_req_option_host"               ,to_name = "coap_req_option_host"                      ,rule_name = "req_option_host"                    ,tll = 1},
    {from_name = "coap_req_option_port"               ,to_name = "coap_req_option_port"                      ,rule_name = "req_option_port"                    ,tll = 1},
    {from_name = "coap_req_path"                      ,to_name = "coap_req_path"                             ,rule_name = "req_path"                           ,tll = 1},
    {from_name = "coap_req_uri"                       ,to_name = "coap_req_uri"                              ,rule_name = "req_uri"                            ,tll = 1},
    {from_name = "coap_req_option_proxy_uri"          ,to_name = "coap_req_option_proxy_uri"                 ,rule_name = "req_option_proxy_uri"               ,tll = 1},
    {from_name = "coap_req_option_proxy_scheme"       ,to_name = "coap_req_option_proxy_scheme"              ,rule_name = "req_option_proxy_scheme"            ,tll = 1},
    {from_name = "coap_req_option_content_format"     ,to_name = "coap_req_option_content_format"            ,rule_name = "req_option_content_format"          ,tll = 1},
    {from_name = "coap_resp_type"                     ,to_name = "coap_resp_type"                            ,rule_name = "resp_type"                          ,tll = 1},
    {from_name = "coap_resp_code"                     ,to_name = "coap_resp_code"                            ,rule_name = "resp_code"                          ,tll = 1},
    {from_name = "coap_resp_option_location_path"     ,to_name = "coap_resp_option_location_path"            ,rule_name = "resp_option_location_path"          ,tll = 1},
    {from_name = "coap_resp_option_location_query"    ,to_name = "coap_resp_option_location_query"           ,rule_name = "resp_option_location_query"         ,tll = 1},
    {from_name = "coap_resp_option_content_format"    ,to_name = "coap_resp_option_content_format"           ,rule_name = "resp_option_content_format"         ,tll = 1},
  }
}
yalua_register_proto(mapping)
