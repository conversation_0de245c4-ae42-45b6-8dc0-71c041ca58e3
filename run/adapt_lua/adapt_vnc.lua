-- vnc.lua
local mapping = {
  from_proto_name = "vnc",
  to_proto_name = "vnc",
  rule_proto_name = "vnc",
  common_flag = true,
  field = {
    {from_name = "MessageType"    ,to_name = "vnc_action"         ,rule_name = "action"         ,tll = 1},
    {from_name = "DesktopName"    ,to_name = "vnc_desktop"        ,rule_name = "desktop"        ,tll = 1},
    {from_name = "File"           ,to_name = "vnc_file"           ,rule_name = "file"           ,tll = 1},
    {from_name = "Host"           ,to_name = "vnc_host"           ,rule_name = "host"           ,tll = 1},
    {from_name = "ClientResponse" ,to_name = "vnc_response"       ,rule_name = "response"       ,tll = 1},
    {from_name = "ServerChanllege",to_name = "vnc_challenge"      ,rule_name = "challenge"      ,tll = 1},
    {from_name = "CliVersion"     ,to_name = "vnc_version_client" ,rule_name = "version_client" ,tll = 1},
    {from_name = "SerVersion"     ,to_name = "vnc_version_server" ,rule_name = "version_server" ,tll = 1},
  }
}
yalua_register_proto(mapping)
