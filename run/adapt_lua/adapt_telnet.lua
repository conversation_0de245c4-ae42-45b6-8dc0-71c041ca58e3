-- telnet.lua
local mapping = {
  from_proto_name = "telnet",
  to_proto_name = "telnet",
  rule_proto_name = "telnet",
  common_flag = true,
  field = {
    {from_name = "Terminaltype"  ,to_name = "telnet_terminal"        ,rule_name = "terminal"  ,tll = 1},
    {from_name = "TelnetData"    ,to_name = "telnet_data"            ,rule_name = "data"  ,tll = 1},
    {from_name = "Username"      ,to_name = "telnet_user"            ,rule_name = "user"      ,tll = 1},
    {from_name = "Passwd"        ,to_name = "telnet_pwd"             ,rule_name = "pwd"       ,tll = 1},
    {from_name = "Negotiations_c",to_name = "telnet_cmd_client"      ,rule_name = "cmd_client"     ,tll = 1},
    {from_name = "Negotiations_s",to_name = "telnet_cmd_server"      ,rule_name = "cmd_server"     ,tll = 1},
    {from_name = "Banner"        ,to_name = "telnet_banner"          ,rule_name = "banner"  ,tll = 1},
    {from_name = "LoginStatus"   ,to_name = "telnet_login_flag"      ,rule_name = "login_flag",tll = 1},
    {from_name = "FilePath"      ,to_name = "telnet_filename"        ,rule_name = "filename",tll = 1},

  }
}
yalua_register_proto(mapping)