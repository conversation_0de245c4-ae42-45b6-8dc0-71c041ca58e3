﻿## [1.3.4.3] - 2025-09-05
    DPISDT:
    7d8a07f0 wuby:修复流表模式下的入队问题
    f10c25eb wuby:修复flow_log模式中流表未被释放的问题
    a82711f1 黄知伟：修复插件获取负载或原始数据时产生崩溃；
    a52f0156 wuby:修复x509崩溃
    b6c577c0 wuby:修复未命中x509实体文件未被删除
    c3b44742 wuby:删除json映射表对lua的输出影响
    546d8c1c wuby:修复gcc4.8上无法编译的问题
    d5872430 wuby:修复x509重命名失败
    716767be 张庆学: 增加命令行参数 sdx_rule_path,sdx_plugin_dir, 配合插件离线调试工具使用
    079c4df8 黄知伟：恢复sdtAppProtoRecord_getPkts和sdtAppProtoRecord_getStream；
    74aa8b42 黄知伟：ynao ip协议中原始数据相关字段输出十六进制字符串；
    f2943d53 黄知伟：修复ynao 中ip和trans未正确输出协议名标签；
    4028696c 黄知伟：新增coap协议；
    d4ccf314 wuby:修复x509崩溃
    e5b761e6 wuby:邮件修复md5字段
    59a0fd61 wuby:修复类型问题导致输出错误
    1c221016 wuby:添加vtysh show flow hash打印
    ec010905 wuby:修复实体文件路径问题
    a7204c77 wuby:修复流表模式编译错误
    d66e21d4 wuby:修复编译警告
    ebc30153 wuby:提交配置文件
    67fb5381 wuby:添加smb还原解析
    49f8d232 wuby:添加dcerpc解析
    e9ec6695 wuby:为tbl目录长度增加变量提升性能
    0f0ddb27 wuby:x509添加证书去重
    c2341f9b wuby:修复x509字段
    cc526f3e wuby:补全email字段
    88c56818 张庆学: 修改 libsdt 插件内置函数库 为动态链接的方式
    61a2af83 wuby:1.修复回显问题.2.添加banner解析.3.添加cmd_s与cmd_c区分
    21e79fcf wuby:修复ftp文件对应关系与字段
    72b0f3c9 张庆学: 修复 tbl record 从 flow 取得无效的pkt, 在dpi_tbl_free时造成的崩溃
    4dcfe500 黄知伟：ynao ip协议增加平均包长、平均包间隔时间字段;
    1eb3aaf0 张庆学: 调整 终端输出内容, 增加宏开关DPI_PRINT_DETAILS
    fa1e1e2f 张庆学: 重新实现 sdtAppProtoRecord_getIpTuple
    3499e781 黄知伟：字段tcpEstablish、tcpFinished填值;
    a85dcb53 黄知伟：修复协议栈未正确输出的bug；
    558420b7 张庆学: 修复 snmp log 字段写入错位
    7dedba8f 张庆学: 调整 tcp_dissect_options()函数中 length为有符号类型
    554523ac 张庆学: 临时使用读写锁保护变量ghash_link_action,pkt_stream_head,防止崩溃
    bb58eba9 黄知伟：ynao调整ip、link用于ip和trans协议输出；
    4a152a77 wuby:修复rspHeadFields
    a3ccc26d wuby:修复content-len,content-body字段
    f193157e wuby:修复url字段
    eab579ef wuby:修复set-cookie与cookie字段
    1693ebec wuby:修复uriSearch字段
    1f6f4904 wuby:condisp字段与手机号字段修复
    93f4b853 wuby:修复ftp文件数量对应不上的问题
    cf394083 wuby:修改flow表字段
    dfb59915 张庆学: 增加 sdtAppProtoRecord_getPkts/getStream函数.(没有实现, 但是libsdt插件需要)
    a45d228a 张庆学: 调整 注册协议字典的协议层级
    97d921ad 李春利： net_vhost && virtio_user 模式的接收端 自适应RSS
    099d3963 黄知伟：同步修改用于匹配的包间隔时间；
    4f9784c9 黄知伟：修复包间隔时间未正确输出的问题；
    0194e128 黄知伟：tags_task和tags_rule字段填值；
    7343b5a1 “黄知伟：重新计算ssl协议中ja3指纹值；”
    ff47c5d4 wuby:修复pop输出文件路径错误
    dc4d912e wuby:为匹配失败的文件删除增加绝对路径
    eb806602 wuby:修复流表模式下程序退出pcap索引没更新的问题
    fa7a9beb wuby:修复流表输出错误
    5bdd7a1f wuby:修复ftp_data无法进入解析的问题
    1bd3c672 黄知伟：修复lua适配时协议名未按适配输出的问题；
    7771140f wuby:修复流表模式下无法停止程序的问题
    148f227d wuby:修复输出streamid位数问题
    f1ec583e wuby:为流表添加stream_id
    ce9f04bb wuby:恢复cwmp编译
    0bcd973c 黄知伟：ynao增加共性字段map_stream_id用于流关联；
    bad983cd wuby:http适配到最新的重组框架
    15d17a33 wuby:提交eml实体文件名字段
    494ab495 黄知伟：ynao补充ssl协议字段；
    52cc7113 黄知伟：ynao补充dns协议字段；
    d26c5966 wuby:修复y脑与327的编译冲突
    c861f0ea wuby:telnet适配到最新的重组框架
    88a0bc00 wuby:1.pop适配到最新的重组框架;2.修复邮件用户名无差别base64解码导致的输出错误
    c147c3f4 wuby:imap适配到最新的重组框架
    4d006886 wuby:smtp适配到最新的重组框架
    746d1136 黄知伟：适配ynao共性字段；
    0b7d1405 wuby:修改字段注册方式
    4f959d96 wuby:修复ftp_u协议号错误
    4d6ba386 wuby:为流表输出增加文件数组
    b16bc4ae wuby:ftp补全字段
    bdf3e263 wuby:为http添加一些辅助函数
    b9391053 黄知伟：新增ynao条件编译选项；
    20c96940 wuby:提交组包ftp
    ea9f05a4 wuby:移除libsdt中删除的变量
    4a9f8171 wuby:提供移动网通用字段注册接口
    36a1faed wuby:为流表模式增加不经过匹配线程的输出模块(使用元数据中的输出模块)
    04faa8ed wuby:添加flow_i;flow_m流表接口
    f2953d68 wuby:为arkime的pcap索引添加离线读取功能，保证不重复
    8f1f1e59 wuby:全量pcap索引保存在实体文件中
    6908890b 张庆学: 调整部分协议字段注册类型
    f7f2743b 张庆学: 修复 统计bps出现的天文数字
    e56e5cac wuby:增加http中mac的设备系统标识
    0f013611 wuby:修复http中长度计算错误导致的越界问题
    ce98d712 张庆学: 修正 部分字段注册类型与赋值类型不一致的问题
    135b9d12 李春利: 把CPU让出来，不要频繁做无用功。解决 匹配性能 被rte_ring_sc_dequeue_burst抢占现象
    03360701 张庆学: 对于命中的ipff,link规则, 在流表中分开记录. ipff不能按照会话处理后续报文
    28256a8d 李春利：解决 ip_in_ip 隧道 在 IPFF 模式下不能参与匹配的问题
    acc27447 李春利：字段类型注册 更新
    7fb81ee8 张庆学: 修改 link模式下命中包数,字节数为累加统计
    7a471da4 李春利：flow 超时 更新 命中包数
    0688427a 李春利：http uri 字段不填充导致规则不命中，解决 HTTP 开启 uri 双向输出导致的崩溃问题
    c5dde793 张庆学: 在匹配阶段给pRec->flow赋值, 否则 在求值阶段部分依赖flow的字段 求值结果为NULL
    d32a6d22 张庆学: 修复 解析隧道协议的 direction 参数传参错误
    981e1e05 李春利:按照字段注册要求 将 实际的[数值型] 转换成 注册要求的[字符型]
    1cfa5ec2 李春利：根据字段类型定义  在precord_fvalue_put_by_index前 将 STRINNG 转换为按照定义要求的 INT
    50faa0ad Revert "李春利：纠正 HTTP code、resp_code 的赋值类型"
    630343ea 李春利: 代码巡检 STRING、BYTES类型 长度为零时，索引自增即返回
    39bff414 李春利：解决vtysh ： show rules  match 显示不准问题 [IPFF/LINK 已测试]
    0379f3fd 李春利：完善 BYTES 数据类型赋值
    a71fce57 李春利：precord_fvalue_put_by_index() 字段的[注册类型]与[赋值类型]一致性检测 .纠正协议的 YA_FT_BYTES 全部更改为 YA_FT_STRING， 仅保留 STREAM 类字段为 YA_FT_BYTES
## [*******] - 2025-07-11
    DPISDT:
    016b8f9 张庆学: 修复 mbuf没有释放
    d7085c8 张庆学: 修复 程序结束时, precord_destroy发生的SIGSEGV问题
    5ba6ccb 张庆学: 增加 在隧道协议中解析内层ip时, 传递外层的acl match hash
    a663779 李春利：规则分为8个类别，删除接口sdtEngine_RuleDelete、sdtEngine_TaskDelete、sdtEngine_RuleClear、sdtEngine_RuleEnable、sdtEngine_RuleDisable
    a98b4f5 张庆学: 调整 web_config.ini配置, 设置输出.json/.pcap文件的轮换的时间60s,大小50MB
    45945a2 张庆学: 增加单包 arp匹配后的命中结果统计
    e65bf47 李春利: flow 初始化的时候-执行一次 timer_reset 避免flow少于4帧不输出
    bea0467 李春利： padapt_engine_record_check_and_adapt 因lua程序错误导致的JSON输出的字段内容全空
    b43ff3e 李春利：纠正 HTTP code、resp_code 的赋值类型
    006fbae 李春利：恢复 LINK 规则输出 报文
    5b1a076 张庆学: [自动测试] 优化自动测试脚本, 支持路径通配符
    dda3220 张庆学: 解析->匹配 传递 mbuf, payload
    e67ce83 张庆学: 修复 部分协议没有注册的,导致规则编译失败的问题
    c33b765 李春利： update adapt_lua
    106a422 张庆学: [自动测试] 修复 管道满导致的 dpi被write函数阻塞
    2225153 李春利：支持全通配 规则在 ip content 匹配关键字
    c4b810e 李春利： 解决 physical_id 超过 MAX_NUMA_NODE 导致的崩溃
    c22c2f6 张庆学: 测试脚本中发生错误时, 在脚本中停止dpi程序,以免下次测试运行失败
    8754fae 张庆学: [自动测试] 调整 只打印测试未通过的规则
    e7811f3 张庆学: 增加 打印初始化完成信息.初始化完成后,测试脚本才能使用RESTful接口
    12e1e41 张庆学: 将./test目录添加到打包命令
    f338eb2 张庆学: 增加自动测试模式下自定义tbl,pcap输出方法
    d10dc30 张庆学: 增加中断信号控制离线读包前的阻塞
    dd1f7e0 张庆学: [自动测试] 规则增加should_hit属性, 可以添加不会被命中的规则作为对照组, 没有命中为通过
    5d8d601 张庆学: 增加 离线收包报文数量统计
    3e1e0b0 李春利：设置 pkt 指针 payload
    76a147c 张庆学: 解决离线读包在退出时 队列中有未消费的数据而产生的丢数据的问题.
    4350b1d 张庆学: 添加测试目录test/, 添加自动测试脚本 test_main.py. 测试步骤参考test/README
    407c2be 张庆学: 改造解析程序, 适配自动测试脚本
    1f0ef91 李春利：link 协议完成gtest
    bc11aaa 李春利： link 协议 gtest 单元测试 第一阶段
    3ed12da 李春利: 完成test_common_callback 的 单元测试
    fcef272 张庆学: 在解析->匹配->输出过程中, 使用mbuf引用替换 packet的拷贝
    ec2c55b 李春利: gtest 支持 线路号
    70a9df0 李春利: yadpisdt 整体编译为一个库， 供 gtest 测试使用
    c479b29 李春利: 提交 tcp 的gtest测试用例
    cef3024 李春利: UDP 协调 适配 Gtest
    7dd63e9 李春利: 完善 ip 字段的 gtest 测试用例
    093d5eb 李春利: dpisdt 融合 gtest 测试工具
    a17de7a 李春利: tcp_windows 改为 本机字节序
    c2e21ba 李春利: IPFF 规则中带有 链路层|传输层 关键字,  要下移到 查flow 之后.  应该改成不依赖 flow.
    06bb3e6 李春利: 在ProtoRecord中复用pkt_info
    06f84d9 李春利: ip/tcp/udp/sctp/common/link/ 采用匹配时 实时计算
    afd05c0 李春利: 完善 规则体的 正则/浮动/完全/ 3种匹配 算法支持
    db9e1b8 张庆学: 利用rte_mbuf内置的refcnt, 使用mbuf引用替换g_memdup
    c997868 李春利: 实现 AC 编译/匹配 引擎
    f2c60e5 张庆学: 全面调整 packet_strea对象获取方式为 从内存池中获取
    aa7f6f3 张庆学: 去除 mac转发中对报文内存的malloc, 调整为直接从对象池中获取mbuf
    a4ab9f2 张庆学: 调整 sdt_out 线程中使用对象池的方式获取packet_stream对象, 去除molloc
    4127bf2 张庆学: 更新 src/CMakeLists.txt, 增加 libxml-2.0 依赖
    85bccb8 张庆学: 增加文件 scripts/yaDpiSdt_rule_agent.py, 监测ftp规则是否更新, 以及拉取最新的规则到本地
    760a394 李春利:adapt_lua 同步

    LIBSDT:
    85ec96a 李春利：规则分为8个类别，删除接口sdtEngine_RuleDelete、sdtEngine_TaskDelete、sdtEngine_RuleClear、sdtEngine_RuleEnable、sdtEngine_RuleDisable
    78551a3 李春利： 字段的定义 与 赋值类型 不一致 允许通过，屏幕会有一条警告
    317fc48 李春利: 1、关键字len=数字 实现方式为 ACL协议号对应的协议层载荷长度。2、纠正测试用例 "\r\n"
    71ca490 李春利：关键字 支持 否定 运算
    7e0cde8 李春利：将 关键字中的 len=数字 元素 改造为 唯一标识。 解决节点相同但分散在不同的树枝上， AC关键字匹配 仅返回1次，导致相似节点不能命中问题。
    c62fff6 李春利： 纠正 十六进制 转换问题
    b68cb05 李春利： 解决 Only关键字 匹配 失败跳转 偏移过大
    d1aa1c6 张庆学: [unittest] 添加测试用例 ComplexRuleMatch.bug001
    47f27ef 李春利：支持 ACL协议号为空  关键字改为 ip content 中匹配
    55d2ba4 张庆学: 调整 sdtEngine_fini在析构最后一个pEngine后, 再停止日志线程
    eaf6d5a 张庆学: 新增 控制监控规则文件开关的环境变量SDT_RULEFILE_MONITOR
    49ca296 李春利：AC 支持 关键字的长度限定标识 匹配
    78297f3 张庆学: 加入测试组提供的复合测试用例
    5cb0942 张庆学: [unitTest]增加关键字'len=<num>'和16进制模式的测试用例
    4ca971b 张庆学: 添加环境变量NO_PRINTF=1, 减少标准输出的内容
    de200b3 李春利:解决引入的测试用例导致编译报错 /root/source/libsdt/test/unitTest/rule_match.hpp:237:5: error: ‘map’ in namespace ‘std’ does not name a type /root/source/libsdt/test/unitTest/rule_match.hpp:178:33: error: ‘find_if’ is not a member of ‘std’
    4b73d91 张庆学: 新建单元测试目录test/unitTest, 添加acl,keyword,body rule测试用例
    2568733 李春利: 消除编译警告
    7bd7115 李春利: 将关键字的 固定位置 纳入 节点的HASH计算中, 解决 GET 与0:GET 混在一起
    102f226 李春利: 解决十六进制 转义 导致的 模式转换 错乱
    359d682 李春利: 解决空BODY导致的误命中
    f1bf355 李春利: 恢复传统字段求值
    bde6b87 李春利: SDT 规则关键 中的十六进制 格式 由HS模式 转换为 AC 模式
    e63fec8 李春利: 完善 规则体的 正则/浮动/完全/ 3种匹配 算法支持
    6f460f1 李春利: 关键字规则 调整为 Bytes 对象存储. 为承接 16进制表达的关键字做准备
    2e28192 李春利: 扩展AC匹配算法支持 浮动匹配/完全匹配/位置匹配/
    967746e 李春利: 命中测试输出
    1844e78 张庆学: 修复 http_server 线程在程序退出时访问已销毁的static变量的问题
    7b4c914 李春利:补充规则的unitID
    139f862 李春利: 调整规则文件路径 "SDT_Rule_Update.txt"
    dd2cb5a 李春利:  移除 clock() 计时功能
    42ec391 李春利: 解决 规则体字符串 模式转换导致的 崩溃
    b43e372 张庆学: 优化xml任务参数提取和错误校验
    58f7de1 李春利: 调整 包长限度标志
    67f83d6 李春利: 解决 Double导致的崩溃
    72016f8 李春利: 解决 xmlSAXHandler 对象未初始化导致的 XML解析崩溃 (gdb) bt (gdb) f 1 (gdb) p ctxt->sax->setDocumentLocator $2 = (setDocumentLocatorSAXFunc) 0x1 (gdb)
    1a461d9 李春利: 实现 AC 编译/匹配 引擎
    1110edf 张庆学: 调整mongoose日志等级; 去除编译警告
    6d4d6ec 张庆学: 调整 监测文件目录
    ea45055 张庆学: 增加 本地规则文件更新监测功能
    1e3e51d 李春利: keyword 编译代码 阶段1 实现
    170a3b1 李春利: AC 第一阶段
    e0173fd 李春利: 原子计数 rule_hit
    93250ab 李春利: delete hitOnCntTotal
    4183945 李春利: 仅存在Keyword且不存在BODY时, 不再需要 已运算的 bitmap, 以加快运算速度
    8569436 李春利: 降低内存的访问次数
    0c7cae6 李春利: 解决 bitmap 未初始化 导致的 误命中
    e031aed 李春利: 删除属性 ruleHashCode
    b62c653 李春利: 统一 sdt_types.h , 并调整受影响的代码
    aa19872 李春利: 新的 求值API sdtAppProtoRecord_getFieldByIDv3, 兼顾rec求值 和 直接关键字求值
    6aafe89 李春利: 解决 ACL 命中 结果没有 及时 输出导致的 不命中问题
    0f2abe1 李春利: 清除 is_ipff 标记
    3280429 李春利:没有关键字-没有规则体 直接命中
    13ecf84 李春利: 调整 规则分类, 解决分类 不正确
    4ac362e 李春利: 新增接口 sdtEngine_getRuleType_By_aclHash
    4c94775 李春利: 调整 API 参数 sdtEngine_matchSdtRules_v2
    ff8eeb3 李春利: 解决求值 过程 rule_kind 导致的崩溃
    afe2512 李春利: 解决规则匹配 因规则类型 不存在的 trie对象导致的崩溃
    158bf8f 李春利: 适配 alias 表 遗漏的 "."转"_",如 规则[cable_linename1] Alias表-> [common_cable_linename1] 协议字段表-> [common][lineName1]
    8d0eef7 李春利:适配 trie  为空 导致的崩溃
    d2d90d7 李春利: 调整 sdt_rule_type
    8008a9c 李春利: 适配新的 API接口 sdtEngine_matchSdtRules_v2
    0fcb233 李春利: sdtRuleVec, trie 改为 8类结构
    cbb169d 李春利: 规则分类第1阶段
    8af0c3b 李春利: 删除SdtPrematchResult
    c4b793b 李春利:删除预匹配代码.  关键字匹配 转换为 规则体
    23dad16 李春利: 去除C文件的可执行属性
    3db6ec7 李春利:删除规则反射代码2. (过度设计 变 累赘)
    fbafc7c 李春利:删除规则反射代码. (过度设计 变 累赘)
    2498e60 李春利: 去除C文件的 可执行属性
    8f58daf 李春利: 规则分类
    934fbfe 李春利: 仅仅控制日志. release 也要带上符号
    e47d1d4 李春利: sdtMatchResArray 改为存储类型改为 指针
    a0255b6 李春利: 完善 字段 编译错误 原因
    4521e7a 李春利： 解决 编译 警告
    8119000 李春利: 解决 expressionAdd  导致的崩溃
    04e1084 李春利: 解决 关键字类型 转换问题
    327cfa3 李春利： FastSDT 首次代码
    282da48 李春利: 删除 transaction_mdj_xml
    81470da 李春利: MDJ 功能代码 合并

## [*******] - 2025-03-19
李春利:
    - 实现 SDT FAST 匹配算法
    - IPFF_ACL_KEYWORD0_RULEBODY0 命中输出
    - MAC 帧 转发均衡基于 FLOW_ID(五元组)
    - MDJ现场 sdt_out_ring 分发不均衡
    - PCAP 索引文件 添加 RULE_ID, INFO_TYPE 元素, 完善 Global_LineNO 的解析
    - RecIPRule 生成到本地，sdt_out_thfunc 优化
    - rule_mode, task_mode, mode_param, task_sub_type 传递 XML-->Action-->packet_stream
    - sdtMatchResArray 改为存储类型改为 指针
    - SMTP 支持非标端口
    - SSH 支持非标
    - VTYSH  显示 MAC帧转发计数
    - VTY新增 规则更新时间
    - 不预设ProtoRecord值空间,调整sdtAppProtoRecord_getFieldByID返回类型,规则运算求值过程取出字段值.针对IPFF类协议快速匹配命中输出
    - 仅存在Keyword且不存在BODY时, 不再需要 已运算的 bitmap, 以加快运算速度
    - 传递XML MAC池，读取默认MAC池， 转发帧，配置task_sub_type，rule_mode与method 决策[存包|转发]
    - 修改 API接口, 分离 IPFF/LINK  匹配代码
    - 删除PCAP的51字节头， 删除默认action 中的 report
    - 删除 prematch 代码. 关键字匹配改为 规则体
    - 删除冗余属性, 支持LINK规则匹配输出
    - 删除 冗余配置项tran_match
    - 删除部分 precord_clone 防止崩溃
    - 去除 编译警告
    - 增强程序 运行稳性
    - 实现 flow 关联 命中的 LINK规则
    - 恢复检测. 牡丹江现场出现 sdt_out_thead_num 参数为空导致除0崩溃
    - 扩展 task_ID 字符长度， 解决taskid被截断导致在同一个 task中
    - 提交 CWMP 修复代码. 解决 CWMP 不命中不输出问题.演示规则:[IPFF tcp@*:*<>*:*; cwmp.oui~"."]
    - 提交 FadtSDT
    - 新增接口 sdtEngine_getRuleType_By_aclHash
    - 新的帧结构 14字节头+51字节头+报文帧+98字节Direct_IP_Custom_Tail
    - 新的 求值API sdtAppProtoRecord_getFieldByIDv3, 兼顾rec求值 和 直接关键字求值
    - 日志按需启用
    - 添加ACL 命中计数， 解决 flow->pkt 无效引用导致的崩溃
    - 添加新文件 update_sdt
    - 清晰变量命名
    - 清除 is_ipff 标记
    - 牡丹江现场 初始化
    - 竖线分隔的TBL结构 已经彻底不存在了. 那就清理干净
    - 统一 sdt_types.h , 并调整受影响的代码
    - 解决 1565(1514+51) 的报文无法输出问题
    - 解决 内有规则体 -- 会话但是没有超时的问题
    - 解决 队列 index  为负值导致的 分发 MAC错乱问题
    - 调整 dpi_forward 系列文件布局
    - 调整 sdt_in_pcap 参数
    - 调整显示效果
    - 适配 alias 表 遗漏的 "."转"_",如 规则[cable_linename1] Alias表-> [common_cable_linename1] 协议字段表-> [common][lineName1]
    - 适配新的 API接口 sdtEngine_matchSdtRules_v2
    - 限制 "unknown proto type" 显示
张庆学:
    - packet_dump()输出的pcap.json索引文件 增加.tmp临时文件后缀
    - pcap.json 增加 ruleID 字段
    - .pcap文件名 增加本机ip地址
    - <schema index 转换 proto id> 未完成， 暂放开所有协议的解析开关
    - tcp 单包匹配, 允许 common协议通过
    - 优化 sdx mac_hdr 参数传递, 防止出现空线路号的情况
    - 修复 email.attConSize 输出类型为整数类型
    - 修复 pcap 文件长度计算错误
    - 修复 rule_mode 未赋值, 后续处理流程错误
    - 修复 tcp match中 tbl 没有释放
    - 修复 tcp match 中的 record内存 没有回收
    - 增加 sdx 线路名转换库
    - 增加 测试线路号判定和填充
    - 增加配置参数 DUMP_PCAP_WITH_SDX_MAC, 控制输出pcap报文是否带51字节sdx mac头
    - 支持 KS_ZC, KS-ZC
    - 放宽 ssh 输出tbl的条件，解析出 版本号即可输出tbl进行后续匹配
    - 新增 公共字段输出
    - 更新 pcap 存盘路径
    - 更新 自定义测试线路号
    - 调整 SDT_RULE_KEY_SIZE=64
    - 调整输出目录
    - 针对 IPFF 6@*:*<>*:*;cable_linename1 ^ "1-24-E-R-S64", 放开 tcp 匹配的严格限制
黄知伟
    - mdj修改带特殊mac头输出pcap文件名；
    - 增加decode 非标端口协议识别开关；
    - 应对mdj非标端口测试；

## [1.2.3] - 2024-11-05
### Fix
  - 张庆学: 完善 收包线程内mbuf free
  - 张庆学: 在收包线程 51字接头，未知类型数据包跳过不丢弃

v1.2.2 : 2024/10/30
李春利：
  - 适配 MDJ 项目 不经过协议解析输出 packet_dump()
  - 突出 packet_dump 的参数配置说明
  - RDP的端口3389, 若客户端的端口偏小, 则复现无法识别解析. 如 1685:3389
  - 解决VNC协议无法 进入识别的问题
陈政权：
  - 51字接头，未知类型数据包跳过不丢弃
  - 修复 http 异常 Content-Length 导致写 record 崩溃
张庆学：
  - 在dpdk收包线程 增加sdx特殊mac头解析
武柄屹：
  - 修复错误的proto_layer

v1.2.1
张庆学：
  - 更新 bgp,x509 字段输出类型与定义一致
  - 在 tds,telnet,tns 也增加对 match_data_len的赋值, 用于后续的匹配统计,告警日志
  - 修复 告警日志 长时间不生效的问题

v1.2.0 : 2024/10/16
李春利：
  - 喜春发现未知功能开关项
  - 预匹配阶段 如果报文不是 [TCP|UDP] 才走 IP 参与匹配
  - 新增HTTP BODY解析开关. 默认不解析 HTTP BODY
  - 更正 HTTP 变量名
  - 消除编译警告
  - 调整 HASH_INDEX 调试输出
  - 可配置 UNKNOWN会话报缓存个数, 默认禁止对 UNKNOWN会话 报文缓存
  - 默认关闭 单包匹配, 开放 pPrematchResult 规则通过
  - 一种基于ipv4/ipv6网络会话 [正向/反向] 均衡入队的实现方法
  - 解决 ACL 中即有空Body, 又有http|TLS|EMAIL|SQL 规则导致 空Body不输出问题
  - 删除config.ini无用配置项
  - 修复 vtysh unknown 计数缺陷
  - 删除Recoder 中的 Alloc 对象, 避免性能消耗
  - 去除编译警告
  - Flow 老化入队列失败 计数, flow_hash 插入失败检测 与计数
  - 报文放大器 增强 支持不丢包读取 PCAP目录
  - vtysh show flow detail 命令只输出有效值
  - 采用XOR 解决解析队列分配 不均衡问题
  - 删除无用函数 do_idle_flow_free
  - flow_timer_cb 入队失败附加警告日志
  -  将 prev_tsc 变量 集成在 ctx 中
  - 记下Rule 的全部 proto_ID. 应用场景: 如果没有IP/TCP/UDP的规则, 那就可以 避免构造REC 节省时
  - 调整对 mem_alloc的引用, 解决ID2780
  - 解除 会话的 禁用.
  - IP 阶段 预匹配 采用 ip.tot_len, 而不用payload_len. 例如 len=401,而实际的paylaod_len并没有401
  - SDT规则Action类型改为第一范式, 删除复合类型 SAE_report_and_packetDump,SAE_event_and_packetDump
  - 关闭 输出
  - get_parameters_keys_values 添加长度的有效检测, 防止 HTTP解析memcpy 遇到负值触发崩溃
陈政权：
  - 修复 tbl clone 顺序致的 flow 中 record 释放崩溃
  - 暂时屏蔽 flow 中的 record 复用
  - 修复 327 udp 匹配崩溃问题
  - 补充配置文件说明
  - 程序线程支持动态配置，支持参数列表以及配置文件两种方式
  - 单核多线程，禁止同时绑定同一个物理核的逻辑核
  - 优化线程绑定核心
  - 为每个线程命名以优化火焰图展示
  - 使用新版的内存分配器，以及优化 record 创建方式
  - 1、添加 mbuf 预取 2、优化 mbuf 分发
  - 修复 zdy record 推送 字段值类型失败问题
  - 调整 tcp 超时的精度为 ms
  - 添加 327 固定头
  - 添加 ip tcp udp 匹配开关
  - flow中添加 record 对象，对record 的部分layer 进行复用
  - 修复静态变量复用的 bug
  - 1、添加输出开关 2、修复 tbl 内存池不回收问题
  - 修复 udp record 不构建
  - 添加 libsdt 回调预处理，预防报错产生的 I/O 影响性能
  - 1、减少定时器的调用频率，降低精度 2、修复 变量未初始化导致的崩溃
  - tbl log 内存添加引用计数，修复 acl 多模匹配重复引用导致的崩溃
  - 修复部分内存泄漏问题
  - 移除 fievalue ，全面引用 record
张庆学：
  - 更新 bgp,x509 字段输出类型与定义一致
  - 在 tds,telnet,tns 也增加对 match_data_len的赋值, 用于后续的匹配统计,告警日志
  - 修复 告警日志 长时间不生效的问题
  - 1. 优化 flow老化线程ring 分配方法, 避免cpuid不连续导致计算出的ring id相同.
  - 优化 cpu 核心分配, 保证所有线程在同一 NUMA上分配 cpu lcore
  - 完善 packet分发中 对gtp扩展头的正确识别
  - 修复 智能网卡收包 入解析队列是被没有释放的错误;
  - 修复bug 2806, http uriSearch 写越界
  - 修复 当action为'event();packet_dump(0,0)'时 以下问题:
  - 通联日志帧 在查找流表阶段只查表, 无论是否找到都return, 不修改flow内容
  - 修复 单包匹配过程中的内存泄露问题
  - 修复 asan 报告的堆内存越界访问
  - 存在 空body规则情况下, 也要恢复 通联日志开关的原始配置
  - 增加 通联日志输出
  - 去除 sdt_in_event中 init_log_ptr_data()调用, 直接创建precord.
  - 1.将 dpi_tbl_log.c 中的函数 app_match_rule, app_fields_sdt_match_func,
  - 合并 remote/new_ftype, 在冲突中删除本地修改
  - 修复 部分内存泄露问题
  - 修复 部分内存泄露问题
  - 使线程 stx_out_thread 有序退出, 防止使用已销毁的变量
  - cdp precord put 'cdp' layer
  - 修复 在正则匹配时 目标字符串非null结尾产生的异常. 改为调用g_regex_match_full
  - 修复bug 2382; 增加配置 CONVERSATION_IDENTIFY_PKT_NUM
  - 修复bug 2777
  - 增加 智能网卡初始化默认 action 配置
  - 增加智能网卡收包模式
  - 修复 ./yaDpiSdt -v 不能正常显示版本号的问题
  - 复 flow_info 重复 free的问题; 使用 dec_and_test 一步原子操作替代 dec 和 read
  - syslog 增加判空条件
  - 在 get_protoinfo 函数中增加判空条件
  - 修复 workflow->layers[] 写越界
  - 修复 g_proto_layer在 skip_mpls函数中写越界
  - 修复 写pcap时, pkt_data 访问越界
  - 解决 arp 使用局部变量flow, 在调用dpi_flow_free时崩溃的问题
武柄屹：
  - 修复l4协议没有protoinfo的问题
  - 修复cmake脚本打印
  - 修改cmake使不开启智能网卡可以编译
  - 合并dpi中流超时部分码
  - 修复http越界
  - 修复内存越界访问
  - http增加精细解析开关，用于优化在327中的性能
  - ssl修复内存泄漏

1.1.7 : 2023/07/31
陈政权：
  - 重做 std_interface 部分接口
  - 优化流表超时逻辑，先进行数据处理，真正超时的时候再做内存回收
  - 流表线程线共享添加引用计数，添加流表 clone 接口，哪个线程使用哪个线程释放
张庆学：
  - 修复bug 2770, bgp字段类型与测绘大纲不一致
1.1.5 :  2023/07/16
陈政权：
  - 新增 测试功能代码 报文会话放大器
  - IP/TCP/UDP/SCTP 真正的单包匹配
黄知伟：
  - http可还原实体文件大小改成可配置；
  - 修复chunked内容未能成功申请堆空间的问题；
陈政权：
  - 修复 hash 表删除崩溃，修复 流表重复超时
  - 修复邮件筛选 bug
1.1.4 ： 2023/07/05
陈政权：
  - cwmp 协议调用 xml 添加 error 回调，警告信息不再输出到控制台
  - 优化日志输出
武柄屹：
  - 优化处理http_boundary逻辑，并且修复越界
  - 同步元数据中的protoinfo添加操作
v1.1.2 : 2023/01/04
李春利：
  - 解决 PCIE地址配置 无效时, 转发 提示
  - 解决 长时间运行, 规则下发显示: "等待解析线程超时 XX" 的问题
  - 更新 DPDK ACL 库, 解决result空间未清理干净导致的误命中
  - 解决 strtok多线程竞争导致 MAC地址解析错误的问题  --> strtok_r
  - 解决 错包时flow 为空 导致的崩溃
  - 解决 匆忙为 if无括号语句 添加语句 导致的 SDT 无法输出的BUG
武柄屹：
  - 修复dpdk读包模式直接退出的问题，需要更新到最新的ya_dpdk分支
陈政权：
  - 修改邮件 prottype 错误
v1.1.1 : 2024/01/03
李春利：
  - 消除邮件类文件 的编译警告
  - 解决 flow->pkt 在超时还在被引用 导致的崩溃
  - 解决 从收包到存包, 因mbuf周期太长导致的性能低下-大量MISS问题. 解放手动配置DPDK内存a参数的困惑.  修复 write_hex_to_string 返回无界字符串 导致strlen崩溃
  - 解决 内存耗尽问题
v1.1.0: 2023/12/22
陈政权：
  - 邮件添加端口识别,防止误识别
  - 修复 imap 崩溃的bug
  - 修复 pop 旧代码导致的崩溃
  - 修复邮件 command 越界
张庆学：
  - 修复 字段映射表 to_proto_name配置未生效
  - 更新内存管理器, 减少flow工作量, 转由ProtoRecord负责收集和释放
李春利：
  - 解决 匹配阶段 读取报文的ETH头 heap use after free 问题

v1.0.27 : 2023/12/14
陈政权：
  - 修复 sdt 邮件相关崩溃问题
张庆学：
  - 修复规则下发崩溃
  - 修复 ssh 返回值错误导致的死循环; 修复teamviewer split NG
v1.0.26 : 2023/12/04
张庆学:
  - 修复 http memchr越界; 修复ip_position_switch被固定为1
  - 修复 http 解析cookie, head lines内存越界
  - 修复 协议重组cache 末位补0越界
  - 修复 x509 证书总数量 超出数组最大容量
  - 修复 多log线程获取的file seq相同, 导致的文件内容有覆盖
武柄屹：
  - 修复http中uri_keys数组越界

v1.0.25 : 2023/11/15
  - 解决 2640 yaDpiSdt_1.0.23版本输出pcap-json，产生了空的文件。
  - 解决 HTTP header 栈空间过大, 导致的运行时崩溃

v1.0.24: 2023/11/06
李春利：
  - 检测空文件. BUGID: 2640 yaDpiSdt_1.0.23版本输出pcap-json，产生了空的文件。
  - 添加字符边界,防止越界. BUGID 2645
张庆学：
  - 修复bug 2635, telnet username 和 passwd未输出的问题
  - 更新文件 run/GeoLite2-City.mmdb
武柄屹：
  - bug2644 修复在切换日期时文件计数更新的问题

v1.0.23 : 2023/10/30
李春利：
  - 解决 SSL pst_sslinfo->ECDHPubkey_len 为0 触发的断言
  - 解决 2628 yaDpiSdt_1.0.22版本批量发测试包，程序崩溃
张庆学：
  - 修复 会话内存管理器的double-free, heap-use-after-free错误.
  - 调整 rdp exit_func 在flow_timeout时执行, 确保在exit_func中不产生新的tbl
v1.0.22 2023/10/23
李春利:
  - 解决 1835 sdt：telnet协议，ter_type字段 暂不支持解析
  - 解决 BUG 2609
  - 解决 BUG 2258
  - 解决 Telnet 字段"Username" "Passwd"不能下发的BUG
  - snmp 添加检测 防止越界
  - 补充 json report 中的 乱码
  - 解决 漏填 unitID导致HASH表查找失败 触发的崩溃
张庆学：
  - 修复modbus内存泄露
  - 修复bug 2613, 空指针崩溃
  - 修复bug 2608, ip地理位置 省份填写错误
  - 规则命中统计 只上报有命中增量的规则
  - 1)修复 多用户规则, taskID groupID相同时, 只有一个用户有命中记录.         2)修复 流表中记录规则数超过128, 导致后续规则没有命中记录.
  - 输出report json文件名增加-report-, 与pcap json对应
  - 更新 field/X509Cer.json
武柄屹：
  - 更新x509映射表
v1.0.21: 2023/10/13
李春利:
  - 调整为 用户Unit 包含 多任务Task 包含 多群组Group 包含 多规则Rule.
  - 对 rte_pktmbuf_clone 添加内存不足的检测
  - PCAP 输出为原始帧内容
  - 解决 规则下发 触发 线程在 g_sdt_hash_db_clear 卡死的情况
  - 使用 rte_pktmbuf_clone 替代 PKT_CACHE
v1.1.20: 2023/09/20
李春利：
Data_SRC.Global_LineNO 输出为HEX字符
  - 解决 CPU L1 cache 不同步导致的自旋死锁问题
  - 解决 HTTP 解析 cookie 内存越界
  - 为 数据越界 添加零结尾
  - 更新libmicroxml,解决cwmp协议奔溃
  - 解决 SSL use heap after free 导致的崩溃
陈政权：
  - 修复挂载上报问题
张庆学：
  - 取消上报按协议统计命中包数
v1.1.19: 2023/09/11
李春利：
  - 预匹配结果 在IP/TCP/UDP 层共用
  - BUG 2536 解决 PCAP文件tmp后缀不超时问题
  - 更新 DPDK ACL库, 弥补IPV6不能匹配的疏漏
  - 解决 带不带应用层 的 mac.src, mac.dst 匹配冲突问题
  - BUGID:2545 完善规则要素的处理
  - 实现 emax/elimit/efreq 动作 (report可用, packet_dump不可用)
  - 以十六进制对应字符串存储元数据字段时，默认采用小写
  - 解决规则下发, WEB点击清空规则导致的崩溃
  - 解决 ACL匹配为空 继续往后走导致 预匹配 map查找 的崩溃
  - 解决 NFS 挂载的遗留问题
  - 解决 ACL 多模在 IP/TCP 阶段的遗留问题
  - 解决 IP规则 common字段匹配崩溃问题
  - 解决 HTTP_info 超时时还在引用_info 中的数据导致的崩溃
  - 解决 HTTP键值对空Value导致的崩溃, 解决超时还在引用PKT中的Method导致的崩溃
  - 解决输出payload为HEX无0结尾, 导致strlen的内存越界崩溃
  - 解决flow_mem 释放过早导致的数据引用崩溃
  - 解决 ACL匹配为空 继续往后走导致 预匹配 map查找 的崩溃
张庆学:
  - 修复 bug2513, 补充ssl部分字段
  - 新增 落包.pcap 同时生成一条json格式 RecIPRule信息(测试大纲B.6.3)
  - 取消全局变量 file_name_seq 的 __thread 类型限定
  - 修复bug2417, ip协议protType填充UNKNOWN
  - 实现 按协议统计命中上报
陈政权：
  - 邮件引用邮件解析库
v1.0.18: 2023/08/22
  李春利：
    - 去除调试插桩代码
    - NFS挂载目录, json/pcap/event 都迁过去
    - 解决 event 文件名中存在 NULL 问题
    - 实现 ACL 多模
    - 去除调试插桩代码
    - 更新 libsdt-acl.a
    - 解决 event 文件名中存在 NULL 问题
    - NFS挂载目录, json/pcap/event 都迁过去
    - 解决json tbl 多线程 争用 hash_DB 导致 json 文件不超时
    - XML 文件中的 method 允许连字符(-), 下划线(_), 如ML_ZC, ML-ZC
v1.0.17: 2023/08/14
李春利：
添加哨兵 解决 内存访问越界
  - 解决 cnt 为0时 用-1索引buff导致的崩溃
  - 解决 sdt_rule_hash_db_clean 线程安全问题 导致的崩溃
  - 解决equeue_app_fields 入队列 log->out_elem是NULL 导致的出队列崩溃
  - 解决 sdt_rule_hash_db_clean 两个业务线程共同 clean 导致的 double free 问题
武柄屹：
  - 移除了重复宏定义
v1.0.16: 2023/08/07
武柄屹：
  - 增加链路层的protoinfo
  - 修复协议字段表拼写错误
  - 删除频繁获取网卡ip的接口，用初始化时的变量替代
李春利：
  - 提交 RARP 代码
  - 解决 X722 VF虚拟网卡 启动崩溃问题
  - 移除旧版本的 libsdt-acl 静态库
  - 解决 会话内存管理器 错误free导致malloc的崩溃

v1.0.15: 2023/07/31
武柄屹：
  - arp协议"hardw"改为int类型
  - 修复cdp协议字段表数量错误
李春利：
  - 解决UDP不能解析的BUG. -- 删除应用层的预匹配功能之后的 疏漏.
  - 解決event() 中沒有method()报错
  - ARP 适配 COMM字段
  - 去除 SDT_HEX_IN_TEXT, 根据表达式左值自动判断是否需要将HEX转为整型
v1.0.14: 2023/07/24
张庆学:
  - 更新 libmongoose.a, 修复 socket recv 极其慢的问题
李春利:
  - 解决 TCP/IP阶段的fieldvalue阶段 sdtAppProtoRecord_getFieldByID common.tcp_ttl=128导致的崩溃
  - 解决多线程JSON文件写入竞争问题
  - 删除无意义的开关 SDX_SHARE_HEADER_SWITCH
  - 解决规则数 超过1万条 空指针导致的崩溃
  - 解决 ICMP 不能进入解析问题
  - 添加库文件 libsdt-acl.a.rcp.20230712
  - 修复 mac.src, mac.dst 不能参与匹配运算的问题
陈政权：
  - 添加挂载脚本

v1.0.13: 2023/07/17
李春利：
  - 标书中要求 PCAP的线程号 默认从1开始
  - 修复 sock模式pkt too long 问题- authored 1 week ago
  - 调整输出PCAP文件的路径 形式
  - 适配 XML 文件中的单位ID unitID, 删除TaskName
武柄屹：
  - 补全sdx公共字段
  - 增加挂载路径判断接口。
  - arp协议mac地址输出为u64的hex

v1.0.12: 2023/07/10
李春利：
  - 支持XML规则中的 Method=[ML_ZC|ML_MAC] TopicName=[MAC_ADDRESS_HEX] 语法
  - 将命中规则的相关信息 填充到report()的JSON内容中, 便于检索核验
  - 去除JSON文件找不到提示, topicName的MAC支持分隔符为连字符,解决UDP/TCP/IP 写tbl_log崩溃, 解决ARP hyperscan错误
  - JSON字段表 适配 report()
  - HTTP_N 调整为 HTTP, 解决dpi_sdt 内部是用协议名找json文件的模式,找不到
  - 为了避免元数据与规则筛选TCP字段冲突, 将规则筛选的TCP payloadlen 统一起来
张庆学：
  - 修改sdx 命中转发的报文src mac最后一个字节 为本地设备ip的最后一个字节
武柄屹：
  - 输出tbl目录修改
  - 配置中SDT_OUT_PRODUCE_DATA_DEV_IP替换为SDX_IP_INTERFACE
v1.0.11: 2023/07/03
李春利：
  - 适配字符形式的taskid
张庆学：
  - 用taskID 和 taskName共同标识唯一task 本次更新依赖 libsdt, yaSdxWatch
  - 修复bug2229
武柄屹：
  - 修复 bug 2424
v1.0.10: 2023/06/25
  李春利：
    - 解决会话内存管理器的BUG
    - IP/TCP/UDP 字段匹配 支持link字段
    - 支持多层 ETH/VALN/MPLS/MPLS/PW/ETH/VLAN/MPLS/MPLS/ETH/...
    - 解决 GRE 在 IP/TCP/UDP 填充Common 阶段发生的崩溃
    - 解决 HTTP PIPELINE 的最后一个 URI 无法输出 TBL导致URI求值无效的问题
  张庆学：
    - 修复 modbus 解析崩溃
    - 修复 流超时 过程中的崩溃
  武柄屹：
    - 按照筛选大纲修改数据输出格式

v1.0.9: 2023/05/31
  李春利：
    - 预匹配只在 IP/TCP/UDP/SCTP 的载荷中参与 见附录B.1 解决 无应用层/协议跨层 问题
    - 调整 ARP 协议适配 新的策略
    - IP/TCP/UDP 协议支持 common 字段匹配
v1.0.8: 2023/05/14
  李春利:
    - ARP协议 适配 2023.04.10 的规则要求
    - 添加开关 : 强制 将规则文本中的十六进制转换为HS模式

v1.0.7: 2023/05/04
  张庆学：
    - 修复bug 2250、2233、2229
  李春利：
    - 规则语法 IP地址是否强制 带上 前缀掩码, 由配置项决定
    - 从配置文件 载入默认 Action
    - 支持 嵌套 VLAN 解析
    - 恢复配置项 SDT_RELOAD_RULE_SWITCH 是否拉取历史规则
v1.0.6： 2023/04/17
  张庆学：
    - 重构 X509 扩展属性 解析代码
    - 修复bug 2202, 2203，2042

  李春利：
    - DPDK底层MBUF不丢包, DPI main入队不丢包
    - 解决程序在 GDB 中, 重新编译 file busy 的问题
    - HTTP 解析 C2S/S2C 的其他状态 及时输出
    - 解决 ARP IN VLAN 报文不解析问题

  武柄义:
    - 增加公共字段guid，以及zid配置項

v1.0.4: 2023/03/28
  张庆学：
    - 适配tftp bgp 协议
  李春利：
    - SSL 协议写TBL次序矫正
v1.0.3 : 2023/03/21
  李春利:
    - SSL协议适配 TCP重组, 适配协议字段筛选
    - 支持 ip.stream.src, ip.stream.dst 匹配

  张庆学:
    - 修复bug 2075
    - 增加共性字段protName 高层应用层协议识别
    - 移除部分不需要的协议
    - ssh 去除解析结果中 尾部多余的空白字符

v1.0.2 : 2023/03/14
  李春利：
    - 支持 ipff 单包落包
    - 解决BUG 2003
  张庆学：
    - 修复bug 2072、2074、1955、1936
    - 修改GRE.Protocol_type类型为string
  黄知伟：
    - 修复l2tp协议中承载的协议无法解析匹配的问题
    - event规则行为输出tbl的后缀统一为txt
    - 修复snmp协议部分字段未正确解析；

v1.0.1 2023/03/06
  李春利
    1) LLC,LLDP,华为交换机私协议,这类报文,不再打日志
    2) 解决 TCP 握手报文 进入 IP报文匹配功能中. TCP 空包也应该进入TCP的匹配模块才对

  张庆学
    1) 增加SDX别名映射表：field_alias_map.json
    2) 去除写完 protInfo 字段后的置空操作,会导致有嵌套关系的其它协议的该字段输出为空
    3) 修复bug 2169,2170; 适配X509Cer 部分字段.


1.0.0
  李春利
    1) 屏蔽编译警告
    2) 纠正payload 预匹配次序, PKT 不是 TCP/UDP, 才匹配 IP_PAYLOAD
    3) 删除本地 libsdt库&头文件,  采用系统目录内的 libsdt.h libsdt.a
    4) 解决 GOTO语法导致的无差别缓存 无法释放. UDP中的HTTP
  张庆学
    1) 修复 含隧道协议的内层ip层未解析
    2) 修复bug1939, proto_info栈溢出导致的崩溃
    3) 修复bug2158, 预匹配 死循环
    4) 修复bug 2060, HTTP 字段适配conLenByCli; 增加xPowBy字段解析
    5) 修复bug 1935，修改sctp字段别名与客户要求一致
    6) 修复bug 1910, 配置规则时, 隧道协议保持开启状态
    7) 修正数据类型错误
    8) 修复bug 2008, 1992
  黄知伟 2023/02/27
    1) 将report行为输出的文件后缀改为txt
    2) 对于无body体的规则输出pcap包由IPP改成IPP、TCP、UDP
    3) 修改共性字段begtime、endtime输出位数；
    4) 补充g_strsplit返回结果判断，避免崩溃；

v0.0.8
  李春利 2023/02/02：
    1) 修正 tcp->source 数据类型
    2) 错误主动 出屏
    3) 支持 预匹配
    4) 兼容 append_proto_info
    5) 新版识别解析 支持 端口注册
  张庆学 2023/02/02：
    1) 修改x509注册名称为 X509Cer; 更新 X509Cer 字段表
    3) 修复bug 1939，缺少配置项
    4) 修复tcp options字段解析错位
    5) 修复bug 1849,1975, 由于x509 与ssl有依赖关系导致x509未解析。
    6) 修复bgp写tbl 栈溢出
    7) 修复 socket没有关闭超出限制 mongoose报错

v0.0.7
  张庆学 2023/01/11:
    1) 修复field.fType = YV_FT_INT64时未求值的错误
    2) 修复 rdp 主次version解析错误
    3) 修改字段表 field_rdp.json, version 对应 clientversion
    4) 统计上报新增网卡利用率信息
    5) 新增流量易读性单位转换
  黄知伟 2023/01/11
    1) 修复ssh协议客户端和服务端协议版本字段解析错误

v0.0.6
  黄知伟 2023/01/06:
    · 修复 alarm 日志出现导致匹配规则之后无法继续输出报文的问题
    · 修复dns中存在访问越界导致的崩溃问题
    · 去除name_srv_host字段存在的重复值
    · 修复http协议命中输出流数据包不全的问题
    · 修复out_pkt()命中无pcap包输出的问题(不适用存在tcp重组协议)
    · 修复ip、tcp、udp相关规则action中不含report时，输出pcap文件名中groupName为NULL的问题
    · 修复ip、tcp、udp以及匹配数大于20未正确按多用户规则匹配输出pcap的问题

  张庆学 2023/01/06：
    · 修复 状态统计模块 规则命中数增量为0
v0.0.5.14
陈政权 2022/12/16
  1) 更新字段映射表，修复 ftp 协议匹配问题
  2) 修复 tcp  payload 输出内容包含 tcp 头问题

李春利 2022/12/15
  1) 解决 getRunningRuleTaskManager返回为NULL时, 导致的系列崩溃


v0.0.5.14 新增功能
洪乐乐： 2022/12/13
  1) 新增GTP-U,L2TP协议的tunnelID字段，并填充至共性字段
  2) 共性字段映射表增加tunnelID;
  3) 增加L2TP字段表;

v0.0.5.14 新增功能
陈政权： 2022/12/9
  1) 更新配置文件下发

yaDpiSdt_v0.0.5.14_20221209
1）洪乐乐：
   a. 转发时syslog恢复落包；

yaDpiSdt_v0.0.5.14_20221208
1）洪乐乐：
   a. 修复特殊MAC帧命中转发功能；
   b. 支持正常MAC帧命中转发;
   c. 增加自定义trailer；
   d. syslog,event的action不转发，针对pcap，report;

yaDpiSdt_v0.0.5.14_20221207
1）黄知伟：
   a.修复在处理tds协议存在崩溃的情况；
   b.json和pcap文件按web_config中的配置进行切换;
   c.区分用户和规则组进行匹配输出；
   d.将规则匹配输出的event和pcap相关文件的根目录与TBL_OUT_DIR一致；
2）洪乐乐：
   a.修复命中转发功能;
   b.更新通联日志解析问题；
   c.优化特殊MAC头之后的链路层解析;
   d.删除通联日志的json字段表的空格;
3）张庆学:
   a.修复公共字段src mac与dst mac取值颠倒的错误;
   b.删除struct rule_stat_t, 由libyaSdxWatch管理;
4) 李春利:
   a.修复 GRE 访问全部变量越界 崩溃;
   b.纠正 init_protocol_reflect_fields 期间的文件载入错误;
   c.解决HTTP拼装越界;
   d.当为空 Body 时, 开启所有协议解析器;
   e.完善 sdt_types ipff/link 枚举;
   f.libsdt 规则命中后, 在Action 结构中可获取 ipff/link 标记;
5)陈政权：规则要素上报数据类型修改；
6）刘光海：
   a.更改ip/udp/tcp头二进制转为字符串进行规则匹配,ip/udp/tcp payload 部分二进制转换成字符串;
   b.共性字段strDirec 修改s2c为1，c2s为2;添加common字段的srcMacOui、dstMacOui查询;
   c.icmp 支持resTime计算，不准确，粗放处理，统计部分添加规则是否单包模式还是流模式;
   d.添加telnet terminal type输出;
   e.修复mpls大小端问题;


yaDpiSdt_v0.0.5.13_20221201
   洪乐乐：
   1) 修复命中转发功能;

yaDpiSdt_v0.0.5.13_20221201
   洪乐乐：
   1) 更新通联日志解析问题；
   2) 优化特殊MAC头之后的链路层解析;
   3) 删除通联日志的json字段表的空格;


yaDpiSdt_v0.0.5.13_2021121
1）黄知伟：
   a.将dns字段中数据补全；2.将自治系统组织改名为asn；dns中mailSrvHostcnt字段类型；
   b.修复tns协议在丢包情况流数据解析中断的bug；修改tns中Dbname、OracleHost字段数据对应关系；修复tns超时解析预匹配被置为空导
   c.修改pgsql、tns中Password字段输出数据类型；
   d.修复非标准数据RT规则tag取值为外层srcMac；
2）洪乐乐：更新common字段表，更新modbus字段表，清除asn；snmp字段表更新；
3）张庆学：修改json结果文件格式，清除收尾“{}”；
4）李春利：默认匹配算法调整为前缀树；
5）刘光海：
   a.modbus 针对exc_code和ref_num增加标志，标识当前报文是否解析此字段，没解析写空;
   b.增加icmp ttl/qurType/qurIP6/qurIP4/next_hop_mtu/exc_pointer/mul_cast_addr解析；
   c.添加统计信息单元测试，通过vtysh命令打印输出，修正其他相关发现问题；（dpi test module 1/2）
   d.共性字段添加ip地址的ISP，latitud，longitude，ASN输出;
   e.link添加tcp flag ece、cwr、ns的统计输出;
   f.修复vlan赋值错误问题;
   g.修复link最大最小问题和前n报文规则匹配；

yaDpiSdt_v0.0.5.12_20221111
洪乐乐：
1) 更新common, bgp, cwmp, gre, socks, ssh, ssl, syslog字段表;

yaDpiSdt_v0.0.5.12_20221111
洪乐乐：
1) 更新modbus字段表;
2) 清除asn缓存;

yaDpiSdt_v0.0.5.12_20221110
洪乐乐：
1) 修改commom为最新的字段表;
2) email协议更新sender,sender.alias,from.dom字段;

yaDpiSdt_v0.0.5.12_20221110
洪乐乐：
1) web参数源mac，目的mac地址应用到sdt转发地址;

yaDpiSdt_v0.0.5.11_20221109
洪乐乐：
1) smtp,pop,imap补充字段，并整合为email协议输出；
2) 增加ASN自治号获取;

yaDpiSdt_v0.0.5.11_20221101
洪乐乐：
1）优化cwmp协议download,upload请求与响应；

yaDpiSdt_v0.0.5.11_20221026
刘光海：
1）添加arp协议，适配规则匹配功能；
2）ip、udp、tcp协议测试添加规则，可以自动开启该协议开关；
3）icmp、rdp、s7comm适配协议字段类型；

洪乐乐：
1）ssl补充一个字段 服务端证书链: ServerCertHashes
2）syslog字段类型更新(facility, level为整数);
3）snmp, bgp, ssh协议字段补充，适配，规则命中;
4）cwmp oui字段长度更新；
5）删除gre, syslog, ssl, x509字段表前缀；

黄知伟：
1）packet-dump 执行时对应输出"数据采集文件”消息到 kafka；
2）增加datafrom、sigtype配置项，配置如下：
   SDX_DATA_FROM              = ML       # 数据来源：ML，KS，ZW
   SDX_SIGTYPE                = 0x1      # 0x1 : 信号处理节点机
                                         # 0x2: IP统计设备
                                         # 0x3: 业务解承载设备
                                         # 0x4:整体信号分流器
                                         # 0x5:通道化信号分流器（高阶通道化）
                                         # 0x6:高阶虚级联处理设备（通道化分流器）
                                         # 0x7:低阶虚级联处理设备0x8:ATM处理设备
                                         # 0x9:DDN处理设备
                                         # 0xa－0xf:保留


3）FTP支持配置固定端口解析control，配置如下：
   SDX_FTP_PORT = 21
4）元数据输出json文件适配规范-包含report、event、alarm；
5）适配dns和dbbasic协议；


20221025 v0.0.5.10
     洪乐乐
     1) ssh补充字段并调试规则匹配;

20221025 v0.0.5.10
     洪乐乐
     1) 更新syslog字段类型facility,level为整型;
     2) 适配bgp, 优化cdp;

20220930 v0.0.5.8
     洪乐乐
     1) 修复http获取via_num， uri_keys时崩溃;

20220928 v0.0.5.7
     洪乐乐
     1) 使http的字段与元数据解析保持一致，并更新json映射;
     2) 适配ntlmssp;

20220923 v0.0.5.6
     1) 洪乐乐: 流预销毁机制增加cwmp协议;

20220922 v0.0.5.6
     1) 洪乐乐: 移植并适配cwmp, syslog, socks协议;
     2) 洪乐乐: 通联日志优化;

20220909 v0.0.5.0
     1) 洪乐乐: 适配ldp,radius协议;

20220812 v0.0.5.0
    1) 洪乐乐：增加数据采集开关， 开启时转发命中的包至目的MAC地址，不生成pcap;

20220726 v0.0.4.22
    1) 洪乐乐: 新增通联日志协议(暂定TLL)，支持规则命中并输出；
    2) 洪乐乐：增加原始mac帧输出, 支持sdt包转发，目的mac地址可配置;

20220122 v0.0.4.22
    1) 解决无body体的out_pkt模式输出包文数超出预期的问题；
    2) 解决SSL协议在link中写tbl时app_proto写了unknown;

20220121 v0.0.4.21
    1) 增加vtysh命令 show rules match 显示命中规则展示；
    2）新增功能，对应流表是否要记录命中规则；
    3）修复http tbl输出多写一个竖线问题；


20220111 v0.0.4.20
    1) 优化http response body规则匹配，减少memset；
    2) http report增加response body输出，增加X_http_token.X_cookie_token字段；


20220107 v0.0.4.19
    1) 黄知伟：调整vnc/rdb/ftp/email相关协议字段，并适配smtp元数据输出；
    2) 陈政权：sdt添加report的json输出格式；
    3) 释放http response body内容检测8192字节大小 ，最大支持1M；

20211228 v0.0.4.18
    1) 刘光海：增加http response body数据chunk解码，并用户规则匹配，增加Server_body字段；

20211221 v0.0.4.17
    1) 刘光海：解决imaps、pops、smtps被识别为ssl协议后，link中不能按端口识别为上述三个协议；

20211215 v0.0.4.16
    1) 刘光海：修复不能统计out_pkt()命中报文数和字节数问题；
    2）刘光海：修复不能统计action为纯report()的报文数据和字节数据问题；

20211210 v0.0.4.15
    1) 王春辉：添加ssl扫描判断；
    2）李春利：解决规则错误，导致后续head body action 识别错误的问题；

20211209 v0.0.4.14
    1)刘光海：修正解析离线pcap读完即退出模式下不能输出数据问题.


20211207 v0.0.4.13
更新内容：
    1）张庆学：更新libmongoose.a,解决调整MG_BUF_SIZE到30M；
    2）黄知伟：修复gre/icmp协议输出元数据会崩溃问题，并补充适配协议字段；
    3）王春辉：添加SSL双向证书监测，x509添加方向标志；
    4) 刘光海：新增配置参数SDT_RULES_THRESHOLD_HITS = 0，命中次数告警阈值，
               超过此配置阈值，输出一条日志到log.txt中；


20211203 v0.0.4.12
更新内容：
    1) 张庆学：实现规则启用、禁用、清空api接口；
    2）张庆学：调整MG_BUF_SIZE到30M，忽略应用规则时不关心的字段；
    3）陈政权：解决写tbl误用编号导致崩溃问题；
    4）陈政权：gtp_u协议添加ip头长度判断，解决崩溃问题；
    5）刘光海：ftp control方向判断沿用原来方式，对应乱序导致混乱，统一采用端口判断。


20211202 v0.0.4.11
新增功能：
    1）刘光海：优化离线解析完成退出机制；
    2) 陈政权：解决gtp_u崩溃问题；


20211202 v0.0.4.10
新增功能：
    1）新增协议rip；
    2) 输出SSL不合规原因，不合规种类，添加SSL弱摘要算法识别；


20211201 v0.0.4.9
    1) 修复http host规则配置不能使能问题；
    2）修复ssl report输出tbl字段错位问题；
    3）http和ssl的field_http_n.json,field_ssl_n.json
    4) DT_INNER_PLUGIN_SWITCH =0 开关是否关闭360毒霸等工具检测，
                                 该内置插件会严重影响性能，默认关闭；
    5）POPS协议名改为POP3S；
    6）解决360毒霸等内置插件崩溃问题；
    7）优化边打流量边下发规则过慢问题；


20211130 v0.0.4.8
    1)  解决崩溃问题
    2） 修改gtp_u剥离方式；
    3） 添加ssl不合规检测；


20211130 v0.0.4.7
新增功能：
    1）支持映射客户字段3个点4个分段模式 即 [protocol].a.b.c
    2) 输出映射字段表添加功能字段，可以用tbl_trad.sh 查看字段表和对应tbl是否一致；
    3）dns新增一个字段作为answer rdata00；
    4）新增配置SDT_RULES_THRESHOLD_BYTES 可以测试*******.6 默认为0关闭，满足流量条件会输出日志到log.txt;
    5) libyasdt.a sdtEngine_startManageThread增加userId可通过SDT_USER_ID配置；
    6) mysql输出用户名密码。


20211125 v0.0.4.6
修正功能：
    1）ipp的ipv6问题，对应bug1295；
    2）link app_proto字段重复，多写一个；
    3) dns识别从以前限制端口改为不限端口号，用于测试协议误用识别能力；
新增功能：
    1）NMAP、DDOS、PCANYWHERE、360、毒霸数据检测插件接口适配，
       该功能需要开发好插件，放入到对应配置目录中,本文件中inner_plugin中；
       检测结果会在link common字段中RuleEtags中体现；
       测试数据见

    2)配置文件说明：
    FIELDS_JSON_DIR      = ./fields_json # 此项必须配置，
                                         # 初始化时输出字段表和report是否能有数据输出和此有关，
		                                 # 如果配置默认使用./fields_json
    SDT_RULES_FIELD      = ./fields_json     # 配置此项则映射客户端字段，不配置则使用自有字段
    SDT_INNER_PLUGIN_DIR = ./inner_plugin    # NMAP、DDOS、PCANYWHERE、360、毒霸数据检测插件配置目录.

    3)ssl.server.cert.cnt 支持，客户字段映射自有字段SSL.cert.cnt.s目前还不支持3个点映射会失败，可以
      配置自有字段验证ssl.ServerCertificatesNums;

    4）新增ipip、gtp-u、tererdo协议，补全field_dnp3.json、field_s7comm.json字段映射表


20211123 v0.0.4.5
修正功能：
    1)解决event和out_pkt无规则体不能输出问题；
    2)离线读pcap对应规则输出的pcap文件时间与原离线pcap时间保持一致；
    3)离线读pcap对应规则输出命中输出数据之后在退出；
    4)支持ipp.tcp_flags_ns功能


20211123  v0.0.4.4
新增功能：
    1）离线解析pcap，先离线启动命令，下发配置规则成功后，进入vtysh， 执行：start  rcv_pkts ；
    2）link支持
       packets_cnt功能，流报文总量；
       stream0_len，c2s方向报文负载总长度；
       stream1_len，s2c方向报文负载总长度；
       app_proto功能，该流识别到应用协议名（如："HTTP","DNS"）
    3）ipp和link自有字段名更改，以下划线模式配置字段名；
