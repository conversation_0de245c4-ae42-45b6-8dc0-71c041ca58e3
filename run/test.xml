<?xml version="1.0" encoding="utf-8"?>
<taskparams create_time="2024-11-24 10:58:46" type="13">
	<!--<task task_id="yn_01" method="KS-ZC" task_sub_type="11" unit="57302" topic_name="57302">
        <rule rule_id="910" rule_group="001" rule_groupid="710" rule_mode="0"><![CDATA[link 6@*:*<>*:*;  !http_attachment_path=="bin"&&http_statuscode=="200"; report();]]></rule>
        <rule rule_id="910" rule_group="001" rule_groupid="710" rule_mode="0"><![CDATA[link 6@*:*<>*:*;  http_method=="GET"; report();]]></rule>
        <rule rule_id="911" rule_group="001" rule_groupid="710" rule_mode="0"><![CDATA[link 17@*:*<>*:*; dns_cname~"."; report();]]></rule>
        <rule rule_id="912" rule_group="001" rule_groupid="710" rule_mode="0"><![CDATA[link 6@*:*<>*:*;  tls_cert_path ~ "\w"; report();]]></rule>
        <rule rule_id="913" rule_group="001" rule_groupid="710" rule_mode="0"><![CDATA[link 6@*:*<>*:*;  ftp_file_name~ "\w"; report();]]></rule>
        <rule rule_id="914" rule_group="001" rule_groupid="710" rule_mode="0"><![CDATA[link 6@*:*<>*:*;  email_sender~"\w"; report();]]></rule>
        <rule rule_id="915" rule_group="001" rule_groupid="710" rule_mode="0"><![CDATA[link 6@*:*<>*:*;  telnet_cmd_client ~ "\w"; report();]]></rule>
	<rule rule_id="916" rule_group="001" rule_groupid="710" rule_mode="0"><![CDATA[link 6@*:*<>*:*;  tcp_flags_fin_count>0; report();]]></rule>
	<rule rule_id="917" rule_group="001" rule_groupid="710" rule_mode="0"><![CDATA[link 6@*:*<>*:*;  cert_filename~"."; report();]]></rule>
	<rule rule_id="919" rule_group="001" rule_groupid="710" rule_mode="0"><![CDATA[link 6@*:*<>*:*;  rdp_version ~ "."; report();]]></rule>
	<rule rule_id="920" rule_group="001" rule_groupid="710" rule_mode="0"><![CDATA[link 6@*:*<>*:*;  vnc_version_server~ "."; report();]]></rule>
	<rule rule_id="917" rule_group="001" rule_groupid="710" rule_mode="0"><![CDATA[link 6@*:*<>*:*;  dcerpc_version ~ "\w"; report();]]></rule>
	<rule rule_id="921" rule_group="001" rule_groupid="710" rule_mode="0"><![CDATA[link 6@*:*<>*:*;  ssh_hassh_server~ "."; report();]]></rule>
	<rule rule_id="922" rule_group="001" rule_groupid="710" rule_mode="0"><![CDATA[link 6@*:*<>*:*;  tcp_flags_fin_count>1; report();]]></rule>
	<rule rule_id="923" rule_group="001" rule_groupid="710" rule_mode="0"><![CDATA[link 6@*:*<>*:*;  ip_packets>=15; report();]]></rule>
	<rule rule_id="916" rule_group="001" rule_groupid="710" rule_mode="0"><![CDATA[link 6@*:*<>*:*;  smb_file_path ~ "\w"; report();]]></rule>
	<rule rule_id="916" rule_group="001" rule_groupid="710" rule_mode="0"><![CDATA[link 17@*:*<>*:*;  coap_req_code=="POST"; report();]]></rule>
</task>-->
	<task task_id="yn_02" method="KS-YSJ" task_sub_type="11" unit="57302" topic_name="57302">

	<task rule_id="344" rule_group="test344" rule_mode="0" rule_groupid="344"><![CDATA[link 6@*:*<>*:*;rdp_version~"\w";]]></task>
	<task rule_id="345" rule_group="test345" rule_mode="0" rule_groupid="345"><![CDATA[link 6@*:*<>*:*;rdp_host~"\w";]]></task>
	<task rule_id="346" rule_group="test346" rule_mode="0" rule_groupid="346"><![CDATA[link 6@*:*<>*:*;rdp_virtual_channel_id~"\w";]]></task>
	<task rule_id="347" rule_group="test347" rule_mode="0" rule_groupid="347"><![CDATA[link 6@*:*<>*:*;rdp_user~"\w";]]></task>
	<task rule_id="348" rule_group="test348" rule_mode="0" rule_groupid="348"><![CDATA[link 6@*:*<>*:*;rdp_domain~"\w";]]></task>
	<task rule_id="349" rule_group="test349" rule_mode="0" rule_groupid="349"><![CDATA[link 6@*:*<>*:*;rdp_application~"\w";]]></task>
	<task rule_id="350" rule_group="test350" rule_mode="0" rule_groupid="350"><![CDATA[link 6@*:*<>*:*;rdp_access_path~"\w";]]></task>

	<task rule_id="359" rule_group="test359" rule_mode="0" rule_groupid="359"><![CDATA[link 6@*:*<>*:*;vnc_version_server~"\w";]]></task>
	<task rule_id="360" rule_group="test360" rule_mode="0" rule_groupid="360"><![CDATA[link 6@*:*<>*:*;vnc_version_client~"\w";]]></task>
	<task rule_id="361" rule_group="test361" rule_mode="0" rule_groupid="361"><![CDATA[link 6@*:*<>*:*;vnc_host~"\w";]]></task>
	<task rule_id="362" rule_group="test362" rule_mode="0" rule_groupid="362"><![CDATA[link 6@*:*<>*:*;vnc_file~"\w";]]></task>
	<task rule_id="363" rule_group="test363" rule_mode="0" rule_groupid="363"><![CDATA[link 6@*:*<>*:*;vnc_action~"1";]]></task>
	<task rule_id="364" rule_group="test364" rule_mode="0" rule_groupid="364"><![CDATA[link 6@*:*<>*:*;vnc_desktop~"\w";]]></task>
	<task rule_id="3164" rule_group="test364" rule_mode="0" rule_groupid="364"><![CDATA[link 6@*:*<>*:*;vnc_challenge~"\w";]]></task>
	<task rule_id="3165" rule_group="test364" rule_mode="0" rule_groupid="364"><![CDATA[link 6@*:*<>*:*;vnc_response~"\w";]]></task>

	<task rule_id="351" rule_group="test351" rule_mode="0" rule_groupid="351"><![CDATA[link 6@*:*<>*:*;telnet_user~"\w";]]></task>
	<task rule_id="352" rule_group="test352" rule_mode="0" rule_groupid="352"><![CDATA[link 6@*:*<>*:*;telnet_pwd~"\w";]]></task>
	<task rule_id="353" rule_group="test353" rule_mode="0" rule_groupid="353"><![CDATA[link 6@*:*<>*:*;telnet_terminal~"\w";]]></task>
	<task rule_id="354" rule_group="test354" rule_mode="0" rule_groupid="354"><![CDATA[link 6@*:*<>*:*;telnet_data~"\w";]]></task>
	<task rule_id="355" rule_group="test355" rule_mode="0" rule_groupid="355"><![CDATA[link 6@*:*<>*:*;telnet_cmd_client~"\w";]]></task>
	<task rule_id="356" rule_group="test356" rule_mode="0" rule_groupid="356"><![CDATA[link 6@*:*<>*:*;telnet_cmd_server~"\w";]]></task>
	<task rule_id="357" rule_group="test357" rule_mode="0" rule_groupid="357"><![CDATA[link 6@*:*<>*:*;telnet_login_flag~"\w";]]></task>
	<task rule_id="358" rule_group="test358" rule_mode="0" rule_groupid="358"><![CDATA[link 6@*:*<>*:*;telnet_banner~"\w";]]></task>

	<task rule_id="229" rule_group="test229" rule_mode="0" rule_groupid="229"><![CDATA[link 6@*:*<>*:*;ssh_version_client~"\w";]]></task>
	<task rule_id="230" rule_group="test230" rule_mode="0" rule_groupid="230"><![CDATA[link 6@*:*<>*:*;ssh_key_exchange_algorithm_client~"\w";]]></task>
	<task rule_id="231" rule_group="test231" rule_mode="0" rule_groupid="231"><![CDATA[link 6@*:*<>*:*;ssh_algorithm_hostkey_client~"\w";]]></task>
	<task rule_id="232" rule_group="test232" rule_mode="0" rule_groupid="232"><![CDATA[link 6@*:*<>*:*;ssh_algorithm_encrypt_client~"\w";]]></task>
	<task rule_id="233" rule_group="test233" rule_mode="0" rule_groupid="233"><![CDATA[link 6@*:*<>*:*;ssh_algorithm_check_client~"\w";]]></task>
	<task rule_id="234" rule_group="test234" rule_mode="0" rule_groupid="234"><![CDATA[link 6@*:*<>*:*;ssh_algorithm_compress_client~"\w";]]></task>
	<task rule_id="235" rule_group="test235" rule_mode="0" rule_groupid="235"><![CDATA[link 6@*:*<>*:*;ssh_version_server~"\w";]]></task>
	<task rule_id="236" rule_group="test236" rule_mode="0" rule_groupid="236"><![CDATA[link 6@*:*<>*:*;ssh_key_exchange_algorithm_server~"\w";]]></task>
	<task rule_id="237" rule_group="test237" rule_mode="0" rule_groupid="237"><![CDATA[link 6@*:*<>*:*;ssh_algorithm_hostkey_server~"\w";]]></task>
	<task rule_id="238" rule_group="test238" rule_mode="0" rule_groupid="238"><![CDATA[link 6@*:*<>*:*;ssh_algorithm_encrypt_server~"\w";]]></task>
	<task rule_id="239" rule_group="test239" rule_mode="0" rule_groupid="239"><![CDATA[link 6@*:*<>*:*;ssh_algorithm_check_server~"\w";]]></task>
	<task rule_id="240" rule_group="test240" rule_mode="0" rule_groupid="240"><![CDATA[link 6@*:*<>*:*;ssh_algorithm_compress_server~"\w";]]></task>
	<task rule_id="241" rule_group="test241" rule_mode="0" rule_groupid="241"><![CDATA[link 6@*:*<>*:*;ssh_rsakey_exp~"\w";]]></task>
	<task rule_id="242" rule_group="test242" rule_mode="0" rule_groupid="242"><![CDATA[link 6@*:*<>*:*;ssh_rsakey_mod~"\w";]]></task>

	<task rule_id="313" rule_group="test313" rule_mode="0" rule_groupid="313"><![CDATA[link 6@*:*<>*:*;ftp_user~"\w";]]></task>
	<task rule_id="314" rule_group="test314" rule_mode="0" rule_groupid="314"><![CDATA[link 6@*:*<>*:*;ftp_pwd~"\w";]]></task>
	<task rule_id="315" rule_group="test315" rule_mode="0" rule_groupid="315"><![CDATA[link 6@*:*<>*:*;ftp_port>=1;]]></task>
	<task rule_id="316" rule_group="test316" rule_mode="0" rule_groupid="316"><![CDATA[link 6@*:*<>*:*;ftp_server_ip~"\w";]]></task>
	<task rule_id="317" rule_group="test317" rule_mode="0" rule_groupid="317"><![CDATA[link 6@*:*<>*:*;ftp_file_name~"\w";]]></task>
	<task rule_id="318" rule_group="test318" rule_mode="0" rule_groupid="318"><![CDATA[link 6@*:*<>*:*;ftp_file_size>10;]]></task>
	<task rule_id="319" rule_group="test319" rule_mode="0" rule_groupid="319"><![CDATA[link 6@*:*<>*:*;ftp_content_type~"\w";]]></task>
	<task rule_id="320" rule_group="test320" rule_mode="0" rule_groupid="320"><![CDATA[link 6@*:*<>*:*;ftp_server_name~"\w";]]></task>
	<task rule_id="321" rule_group="test321" rule_mode="0" rule_groupid="321"><![CDATA[link 6@*:*<>*:*;ftp_request_cmd~"\w";]]></task>
	<task rule_id="322" rule_group="test322" rule_mode="0" rule_groupid="322"><![CDATA[link 6@*:*<>*:*;ftp_request_param~"\w";]]></task>
	<task rule_id="323" rule_group="test323" rule_mode="0" rule_groupid="323"><![CDATA[link 6@*:*<>*:*;ftp_response_code>1;]]></task>
	<task rule_id="324" rule_group="test324" rule_mode="0" rule_groupid="324"><![CDATA[link 6@*:*<>*:*;ftp_response_argument~"\w";]]></task>
	<task rule_id="325" rule_group="test325" rule_mode="0" rule_groupid="325"><![CDATA[link 6@*:*<>*:*;ftp_login_flag<222;]]></task>
	<task rule_id="326" rule_group="test326" rule_mode="0" rule_groupid="326"><![CDATA[link 6@*:*<>*:*;ftp_operations~"\w";]]></task>
	<task rule_id="1326" rule_group="test326" rule_mode="0" rule_groupid="326"><![CDATA[link 6@*:*<>*:*;ftp_file_path~"\w";]]></task>
	<task rule_id="1327" rule_group="test326" rule_mode="0" rule_groupid="326"><![CDATA[link 6@*:*<>*:*;ftp_file_content~"\w";]]></task>
       
	<task rule_id="327" rule_group="test327" rule_mode="0" rule_groupid="327"><![CDATA[link 6@*:*<>*:*;smb_directory~"\w";]]></task>
	<task rule_id="1325" rule_group="test327" rule_mode="0" rule_groupid="327"><![CDATA[link 6@*:*<>*:*;smb_directory_count~"\w";]]></task>
	<task rule_id="1327" rule_group="test327" rule_mode="0" rule_groupid="327"><![CDATA[link 6@*:*<>*:*;smb_filesize_count~"\w";]]></task>
	<task rule_id="1329" rule_group="test328" rule_mode="0" rule_groupid="328"><![CDATA[link 6@*:*<>*:*;smb_file_path~"\w";]]></task>
	<task rule_id="328" rule_group="test328" rule_mode="0" rule_groupid="328"><![CDATA[link 6@*:*<>*:*;smb_path~"\w";]]></task>
	<task rule_id="1328" rule_group="test328" rule_mode="0" rule_groupid="328"><![CDATA[link 6@*:*<>*:*;smb_path_count~"\w";]]></task>
	<task rule_id="331" rule_group="test331" rule_mode="0" rule_groupid="331"><![CDATA[link 6@*:*<>*:*;smb_os~"\w";]]></task>
	<task rule_id="1331" rule_group="test331" rule_mode="0" rule_groupid="331"><![CDATA[link 6@*:*<>*:*;smb_action~"\w";]]></task>
	<task rule_id="332" rule_group="test332" rule_mode="0" rule_groupid="332"><![CDATA[link 6@*:*<>*:*;smb_version~"\w";]]></task>
	<task rule_id="333" rule_group="test333" rule_mode="0" rule_groupid="333"><![CDATA[link 6@*:*<>*:*;smb_auth_type~"\w";]]></task>
	<task rule_id="1333" rule_group="test333" rule_mode="0" rule_groupid="333"><![CDATA[link 6@*:*<>*:*;smb_auth_type_count~"\w";]]></task>
	<task rule_id="334" rule_group="test334" rule_mode="0" rule_groupid="334"><![CDATA[link 6@*:*<>*:*;smb_file~"\w";]]></task>
	<task rule_id="1334" rule_group="test334" rule_mode="0" rule_groupid="334"><![CDATA[link 6@*:*<>*:*;smb_file_count~"\w";]]></task>
	<task rule_id="335" rule_group="test335" rule_mode="0" rule_groupid="335"><![CDATA[link 6@*:*<>*:*;smb_filesize>11;]]></task>
	<task rule_id="336" rule_group="test336" rule_mode="0" rule_groupid="336"><![CDATA[link 6@*:*<>*:*;smb_user~"\w";]]></task>
	<task rule_id="337" rule_group="test337" rule_mode="0" rule_groupid="337"><![CDATA[link 6@*:*<>*:*;smb_pwd~"\w";]]></task>
      
	<task rule_id="273" rule_group="test273" rule_mode="0" rule_groupid="273"><![CDATA[link 6@*:*<>*:*;cert_version>=1;]]></task>
	<task rule_id="274" rule_group="test274" rule_mode="0" rule_groupid="274"><![CDATA[link 6@*:*<>*:*;cert_sn~"\w";]]></task>
	<task rule_id="275" rule_group="test275" rule_mode="0" rule_groupid="275"><![CDATA[link 6@*:*<>*:*;cert_issuer_common_name~"\w";]]></task>
	<task rule_id="276" rule_group="test276" rule_mode="0" rule_groupid="276"><![CDATA[link 6@*:*<>*:*;cert_issuer_organization~"\w";]]></task>
	<task rule_id="277" rule_group="test277" rule_mode="0" rule_groupid="277"><![CDATA[link 6@*:*<>*:*;cert_subject_common_name~"\w";]]></task>
	<task rule_id="278" rule_group="test278" rule_mode="0" rule_groupid="278"><![CDATA[link 6@*:*<>*:*;cert_subject_organization~"\w";]]></task>
	<task rule_id="279" rule_group="test279" rule_mode="0" rule_groupid="279"><![CDATA[link 6@*:*<>*:*;cert_validity_notbefore<=100;]]></task>
	<task rule_id="280" rule_group="test280" rule_mode="0" rule_groupid="280"><![CDATA[link 6@*:*<>*:*;cert_validity_notafter<=100;]]></task>
	<task rule_id="281" rule_group="test281" rule_mode="0" rule_groupid="281"><![CDATA[link 6@*:*<>*:*;cert_algorithm_id~"\w";]]></task>
	<task rule_id="282" rule_group="test282" rule_mode="0" rule_groupid="282"><![CDATA[link 6@*:*<>*:*;cert_auth_key_id~"\w";]]></task>
	<task rule_id="283" rule_group="test283" rule_mode="0" rule_groupid="283"><![CDATA[link 6@*:*<>*:*;cert_subject_key_id~"\w";]]></task>
	<task rule_id="284" rule_group="test284" rule_mode="0" rule_groupid="284"><![CDATA[link 6@*:*<>*:*;cert_key_usage~"\w";]]></task>
	<task rule_id="285" rule_group="test285" rule_mode="0" rule_groupid="285"><![CDATA[link 6@*:*<>*:*;cert_policies~"\w";]]></task>
	<task rule_id="286" rule_group="test286" rule_mode="0" rule_groupid="286"><![CDATA[link 6@*:*<>*:*;cert_alternative_domain~"\w";]]></task>
	<task rule_id="287" rule_group="test287" rule_mode="0" rule_groupid="287"><![CDATA[link 6@*:*<>*:*;cert_alternative_ip~"\w";]]></task>
	<task rule_id="289" rule_group="test289" rule_mode="0" rule_groupid="289"><![CDATA[link 6@*:*<>*:*;cert_cert_issuer~"\w";]]></task>
	<task rule_id="290" rule_group="test290" rule_mode="0" rule_groupid="290"><![CDATA[link 6@*:*<>*:*;cert_subject~"\w";]]></task>
	<task rule_id="291" rule_group="test291" rule_mode="0" rule_groupid="291"><![CDATA[link 6@*:*<>*:*;cert_public_key~"\w";]]></task>
	<task rule_id="292" rule_group="test292" rule_mode="0" rule_groupid="292"><![CDATA[link 6@*:*<>*:*;cert_fp_fingerprint_algorithm~"\w";]]></task>
	<task rule_id="293" rule_group="test293" rule_mode="0" rule_groupid="293"><![CDATA[link 6@*:*<>*:*;cert_fingerprint~"\w";]]></task>
	<task rule_id="294" rule_group="test294" rule_mode="0" rule_groupid="294"><![CDATA[link 6@*:*<>*:*;cert_extend_id_list~"\w";]]></task>
	<task rule_id="295" rule_group="test295" rule_mode="0" rule_groupid="295"><![CDATA[link 6@*:*<>*:*;cert_auth_info~"\w";]]></task>
	<task rule_id="296" rule_group="test296" rule_mode="0" rule_groupid="296"><![CDATA[link 6@*:*<>*:*;cert_basic_ca>10;]]></task>
	<task rule_id="297" rule_group="test297" rule_mode="0" rule_groupid="297"><![CDATA[link 6@*:*<>*:*;cert_basic_path_length>0;]]></task>
	<task rule_id="459" rule_group="test297" rule_mode="0" rule_groupid="297"><![CDATA[link 6@*:*<>*:*;cert_issuer~".";]]></task>
	<task rule_id="460" rule_group="test297" rule_mode="0" rule_groupid="297"><![CDATA[link 6@*:*<>*:*;cert_validity_remaining>1;]]></task>
	<task rule_id="461" rule_group="test297" rule_mode="0" rule_groupid="297"><![CDATA[link 6@*:*<>*:*;cert_validity_total>0;]]></task>
	<task rule_id="462" rule_group="test297" rule_mode="0" rule_groupid="297"><![CDATA[link 6@*:*<>*:*;cert_alternative_domain_count>0;]]></task>
	<task rule_id="463" rule_group="test297" rule_mode="0" rule_groupid="297"><![CDATA[link 6@*:*<>*:*;cert_alternative_ip_count>0;]]></task>
	<task rule_id="464" rule_group="test297" rule_mode="0" rule_groupid="297"><![CDATA[link 6@*:*<>*:*;cert_key_purpose_ids~"\w";]]></task>
	<task rule_id="465" rule_group="test297" rule_mode="0" rule_groupid="297"><![CDATA[link 6@*:*<>*:*;cert_crl_distribute_points~"\w";]]></task>
	<task rule_id="466" rule_group="test297" rule_mode="0" rule_groupid="297"><![CDATA[link 6@*:*<>*:*;cert_fingerprint_algorithm~"\w";]]></task>
	<task rule_id="467" rule_group="test297" rule_mode="0" rule_groupid="297"><![CDATA[link 6@*:*<>*:*;cert_extend_count>0;]]></task>
        <task rule_id="468" rule_group="test297" rule_mode="0" rule_groupid="297"><![CDATA[link 6@*:*<>*:*;cert_raw~".";]]></task> 
	<task rule_id="469" rule_group="test297" rule_mode="0" rule_groupid="297"><![CDATA[link 6@*:*<>*:*;cert_source>1;]]></task>
      
	<task rule_id="450" rule_group="test449" rule_mode="0" rule_groupid="449"><![CDATA[link 6@*:*<>*:*;dcerpc_version~"\w";]]></task>
	<task rule_id="451" rule_group="test449" rule_mode="0" rule_groupid="449"><![CDATA[link 6@*:*<>*:*;dcerpc_packet_types=="12";]]></task>
	<task rule_id="452" rule_group="test449" rule_mode="0" rule_groupid="449"><![CDATA[link 6@*:*<>*:*;dcerpc_auth_type==18;]]></task>
	<task rule_id="453" rule_group="test449" rule_mode="0" rule_groupid="449"><![CDATA[link 6@*:*<>*:*;decrpc_second_address~".";]]></task>
	<task rule_id="454" rule_group="test449" rule_mode="0" rule_groupid="449"><![CDATA[link 6@*:*<>*:*;decrpc_auth_level~".";]]></task>
	<task rule_id="455" rule_group="test449" rule_mode="0" rule_groupid="449"><![CDATA[link 6@*:*<>*:*;decrpc_object~"\w";]]></task>
	<task rule_id="456" rule_group="test449" rule_mode="0" rule_groupid="449"><![CDATA[link 6@*:*<>*:*;decrpc_interface~".";]]></task>
	<task rule_id="457" rule_group="test449" rule_mode="0" rule_groupid="449"><![CDATA[link 6@*:*<>*:*;decrpc_operation_number==8;]]></task>
	<task rule_id="458" rule_group="test449" rule_mode="0" rule_groupid="449"><![CDATA[link 6@*:*<>*:*;decrpc_endpoint~".";]]></task>
	<task rule_id="41" rule_group="test41" rule_mode="0" rule_groupid="41"><![CDATA[link 6@*:*<>*:*;email_sender~"\w";]]></task>
	<task rule_id="42" rule_group="test42" rule_mode="0" rule_groupid="42"><![CDATA[link 6@*:*<>*:*;email_sender_alias~"\w";]]></task>
	<task rule_id="43" rule_group="test43" rule_mode="0" rule_groupid="43"><![CDATA[link 6@*:*<>*:*;email_from_ip~"\w";]]></task>
	<task rule_id="44" rule_group="test44" rule_mode="0" rule_groupid="44"><![CDATA[link 6@*:*<>*:*;email_from_domain~"\w";]]></task>
	<task rule_id="45" rule_group="test45" rule_mode="0" rule_groupid="45"><![CDATA[link 6@*:*<>*:*;email_from_asn~"\w";]]></task>
	<task rule_id="46" rule_group="test46" rule_mode="0" rule_groupid="46"><![CDATA[link 6@*:*<>*:*;email_from_country~"\w";]]></task>
	<task rule_id="47" rule_group="test47" rule_mode="0" rule_groupid="47"><![CDATA[link 6@*:*<>*:*;email_sender_software~"\w";]]></task>
	<task rule_id="48" rule_group="test48" rule_mode="0" rule_groupid="48"><![CDATA[link 6@*:*<>*:*;email_receiver~"\w";]]></task>
	<task rule_id="49" rule_group="test49" rule_mode="0" rule_groupid="49"><![CDATA[link 6@*:*<>*:*;email_receiver_alias~"\w";]]></task>
	<task rule_id="50" rule_group="test50" rule_mode="0" rule_groupid="50"><![CDATA[link 6@*:*<>*:*;email_by_ip~"\w";]]></task>
	<task rule_id="51" rule_group="test51" rule_mode="0" rule_groupid="51"><![CDATA[link 6@*:*<>*:*;email_by_domain~"\w";]]></task>
	<task rule_id="52" rule_group="test52" rule_mode="0" rule_groupid="52"><![CDATA[link 6@*:*<>*:*;email_by_asn~"\w";]]></task>
	<task rule_id="53" rule_group="test53" rule_mode="0" rule_groupid="53"><![CDATA[link 6@*:*<>*:*;email_by_country~"\w";]]></task>
	<task rule_id="54" rule_group="test54" rule_mode="0" rule_groupid="54"><![CDATA[link 6@*:*<>*:*;email_user_agent~"\w";]]></task>
	<task rule_id="55" rule_group="test55" rule_mode="0" rule_groupid="55"><![CDATA[link 6@*:*<>*:*;email_cc~"\w";]]></task>
	<task rule_id="56" rule_group="test56" rule_mode="0" rule_groupid="56"><![CDATA[link 6@*:*<>*:*;email_cc_alias~"\w";]]></task>
	<task rule_id="57" rule_group="test57" rule_mode="0" rule_groupid="57"><![CDATA[link 6@*:*<>*:*;email_bcc~"\w";]]></task>
	<task rule_id="58" rule_group="test58" rule_mode="0" rule_groupid="58"><![CDATA[link 6@*:*<>*:*;email_reply~"\w";]]></task>
	<task rule_id="59" rule_group="test59" rule_mode="0" rule_groupid="59"><![CDATA[link 6@*:*<>*:*;email_date~"\w";]]></task>
	<task rule_id="60" rule_group="test60" rule_mode="0" rule_groupid="60"><![CDATA[link 6@*:*<>*:*;email_proto_type~"\w";]]></task>
	<task rule_id="61" rule_group="test61" rule_mode="0" rule_groupid="61"><![CDATA[link 6@*:*<>*:*;email_login_server~"\w";]]></task>
	<task rule_id="62" rule_group="test62" rule_mode="0" rule_groupid="62"><![CDATA[link 6@*:*<>*:*;email_smtp_server~"\w";]]></task>
	<task rule_id="63" rule_group="test63" rule_mode="0" rule_groupid="63"><![CDATA[link 6@*:*<>*:*;email_smtp_server_agent~"\w";]]></task>
	<task rule_id="64" rule_group="test64" rule_mode="0" rule_groupid="64"><![CDATA[link 6@*:*<>*:*;email_subject~"\w";]]></task>
	<task rule_id="65" rule_group="test65" rule_mode="0" rule_groupid="65"><![CDATA[link 6@*:*<>*:*;email_x_mailer~"\w";]]></task>
	<task rule_id="66" rule_group="test66" rule_mode="0" rule_groupid="66"><![CDATA[link 6@*:*<>*:*;email_body_type~"\w";]]></task>
	<task rule_id="67" rule_group="test67" rule_mode="0" rule_groupid="67"><![CDATA[link 6@*:*<>*:*;email_content_type~"\w";]]></task>
	<task rule_id="68" rule_group="test68" rule_mode="0" rule_groupid="68"><![CDATA[link 6@*:*<>*:*;email_index~"\w";]]></task>
	<task rule_id="69" rule_group="test69" rule_mode="0" rule_groupid="69"><![CDATA[link 6@*:*<>*:*;email_attachment_filename~"\w";]]></task>
	<task rule_id="70" rule_group="test70" rule_mode="0" rule_groupid="70"><![CDATA[link 6@*:*<>*:*;email_attachment_content_type~"\w";]]></task>
	<task rule_id="71" rule_group="test71" rule_mode="0" rule_groupid="71"><![CDATA[link 6@*:*<>*:*;email_attachment_md5~"\w";]]></task>
	<task rule_id="72" rule_group="test72" rule_mode="0" rule_groupid="72"><![CDATA[link 6@*:*<>*:*;email_attachment_length<=10;]]></task>
	<task rule_id="73" rule_group="test73" rule_mode="0" rule_groupid="73"><![CDATA[link 6@*:*<>*:*;email_header_set~"\w";]]></task>
	<task rule_id="74" rule_group="test74" rule_mode="0" rule_groupid="74"><![CDATA[link 6@*:*<>*:*;email_msg_id~"\w";]]></task>
	<task rule_id="75" rule_group="test75" rule_mode="0" rule_groupid="75"><![CDATA[link 6@*:*<>*:*;email_mime_version~"\w";]]></task>
	<task rule_id="76" rule_group="test76" rule_mode="0" rule_groupid="76"><![CDATA[link 6@*:*<>*:*;email_login~"\w";]]></task>
	<task rule_id="77" rule_group="test77" rule_mode="0" rule_groupid="77"><![CDATA[link 6@*:*<>*:*;email_pwd~"\w";]]></task>
	<task rule_id="78" rule_group="test78" rule_mode="0" rule_groupid="78"><![CDATA[link 6@*:*<>*:*;email_version~"\w";]]></task>
	<task rule_id="79" rule_group="test79" rule_mode="0" rule_groupid="79"><![CDATA[link 6@*:*<>*:*;email_sender_domain~"\w";]]></task>
	<task rule_id="80" rule_group="test80" rule_mode="0" rule_groupid="80"><![CDATA[link 6@*:*<>*:*;email_received~"\w";]]></task>
	<task rule_id="81" rule_group="test81" rule_mode="0" rule_groupid="81"><![CDATA[link 6@*:*<>*:*;email_content~"\w";]]></task>
	<task rule_id="82" rule_group="test82" rule_mode="0" rule_groupid="82"><![CDATA[link 6@*:*<>*:*;email_host~"\w";]]></task>
	<task rule_id="83" rule_group="test83" rule_mode="0" rule_groupid="83"><![CDATA[link 6@*:*<>*:*;email_delivered_to~"\w";]]></task>
	<task rule_id="84" rule_group="test84" rule_mode="0" rule_groupid="84"><![CDATA[link 6@*:*<>*:*;email_x_original_ip~"\w";]]></task>
	<task rule_id="85" rule_group="test85" rule_mode="0" rule_groupid="85"><![CDATA[link 6@*:*<>*:*;email_starttls>0;]]></task>
	<task rule_id="86" rule_group="test86" rule_mode="0" rule_groupid="86"><![CDATA[link 6@*:*<>*:*;email_cmd~"\w";]]></task>
	<task rule_id="87" rule_group="test87" rule_mode="0" rule_groupid="87"><![CDATA[link 6@*:*<>*:*;email_count>2;]]></task>
	<task rule_id="88" rule_group="test88" rule_mode="0" rule_groupid="88"><![CDATA[link 6@*:*<>*:*;email_envelope_from~"\w";]]></task>
	<task rule_id="89" rule_group="test89" rule_mode="0" rule_groupid="89"><![CDATA[link 6@*:*<>*:*;email_envelope_from_domain~"\w";]]></task>
	<task rule_id="90" rule_group="test90" rule_mode="0" rule_groupid="90"><![CDATA[link 6@*:*<>*:*;email_receiver_domain~"\w";]]></task>
	<task rule_id="91" rule_group="test91" rule_mode="0" rule_groupid="91"><![CDATA[link 6@*:*<>*:*;email_envelope_to~"\w";]]></task>
	<task rule_id="92" rule_group="test92" rule_mode="0" rule_groupid="92"><![CDATA[link 6@*:*<>*:*;email_envelope_to_domain~"\w";]]></task>
	<task rule_id="93" rule_group="test93" rule_mode="0" rule_groupid="93"><![CDATA[link 6@*:*<>*:*;email_resent_from~"\w";]]></task>
	<task rule_id="94" rule_group="test94" rule_mode="0" rule_groupid="94"><![CDATA[link 6@*:*<>*:*;email_resent_to~"\w";]]></task>
	<task rule_id="95" rule_group="test95" rule_mode="0" rule_groupid="95"><![CDATA[link 6@*:*<>*:*;email_resent_date~"\w";]]></task>
	<task rule_id="96" rule_group="test96" rule_mode="0" rule_groupid="96"><![CDATA[link 6@*:*<>*:*;email_resent_agent~"\w";]]></task>
	<task rule_id="97" rule_group="test97" rule_mode="0" rule_groupid="97"><![CDATA[link 6@*:*<>*:*;email_body~"\w";]]></task>
	<task rule_id="98" rule_group="test98" rule_mode="0" rule_groupid="98"><![CDATA[link 6@*:*<>*:*;email_body_length<300;]]></task>
	<task rule_id="99" rule_group="test99" rule_mode="0" rule_groupid="99"><![CDATA[link 6@*:*<>*:*;email_body_url~"\w";]]></task>
	<task rule_id="100" rule_group="test100" rule_mode="0" rule_groupid="100"><![CDATA[link 6@*:*<>*:*;email_body_encoding~"\w";]]></task>
	<task rule_id="101" rule_group="test101" rule_mode="0" rule_groupid="101"><![CDATA[link 6@*:*<>*:*;email_body_charset~"\w";]]></task>
	<task rule_id="102" rule_group="test102" rule_mode="0" rule_groupid="102"><![CDATA[link 6@*:*<>*:*;email_imap_name~"\w";]]></task>
	<task rule_id="103" rule_group="test103" rule_mode="0" rule_groupid="103"><![CDATA[link 6@*:*<>*:*;email_imap_vendor~"\w";]]></task>
	<task rule_id="104" rule_group="test104" rule_mode="0" rule_groupid="104"><![CDATA[link 6@*:*<>*:*;email_imap_version~"\w";]]></task>
	<task rule_id="105" rule_group="test105" rule_mode="0" rule_groupid="105"><![CDATA[link 6@*:*<>*:*;email_imap_os~"\w";]]></task>
	<task rule_id="106" rule_group="test106" rule_mode="0" rule_groupid="106"><![CDATA[link 6@*:*<>*:*;email_imap_os_version~"\w";]]></task>
	<task rule_id="470" rule_group="test106" rule_mode="0" rule_groupid="106"><![CDATA[link 6@*:*<>*:*;email_banner~"\w";]]></task>
	<task rule_id="471" rule_group="test106" rule_mode="0" rule_groupid="106"><![CDATA[link 6@*:*<>*:*;email_auth_result~"\w";]]></task>
	<task rule_id="472" rule_group="test106" rule_mode="0" rule_groupid="106"><![CDATA[link 6@*:*<>*:*;email_spf~"\w";]]></task>
	<task rule_id="473" rule_group="test106" rule_mode="0" rule_groupid="106"><![CDATA[link 6@*:*<>*:*;email_envelope_from_domain_count=="1";]]></task>
	<task rule_id="474" rule_group="test106" rule_mode="0" rule_groupid="106"><![CDATA[link 6@*:*<>*:*;email_from_ip_count>2;]]></task>
	<task rule_id="475" rule_group="test106" rule_mode="0" rule_groupid="106"><![CDATA[link 6@*:*<>*:*;email_from_domain_count>1;]]></task>
	<task rule_id="476" rule_group="test106" rule_mode="0" rule_groupid="106"><![CDATA[link 6@*:*<>*:*;email_receiver_count>1;]]></task>
	<task rule_id="477" rule_group="test106" rule_mode="0" rule_groupid="106"><![CDATA[link 6@*:*<>*:*;email_envelope_to_domain_count>1;]]></task>
	<task rule_id="478" rule_group="test106" rule_mode="0" rule_groupid="106"><![CDATA[link 6@*:*<>*:*;email_by_ip_count>1;]]></task>
	<task rule_id="479" rule_group="test106" rule_mode="0" rule_groupid="106"><![CDATA[link 6@*:*<>*:*;email_by_domain_count>1;]]></task>
	<task rule_id="480" rule_group="test106" rule_mode="0" rule_groupid="106"><![CDATA[link 6@*:*<>*:*;email_subject_count>1;]]></task>
	<task rule_id="481" rule_group="test106" rule_mode="0" rule_groupid="106"><![CDATA[link 6@*:*<>*:*;email_x_mailer_count>3;]]></task>
	<task rule_id="482" rule_group="test106" rule_mode="0" rule_groupid="106"><![CDATA[link 6@*:*<>*:*;email_content_type_count>3;]]></task>
	<task rule_id="483" rule_group="test106" rule_mode="0" rule_groupid="106"><![CDATA[link 6@*:*<>*:*;email_body_md5~"\w";]]></task>
	<task rule_id="484" rule_group="test106" rule_mode="0" rule_groupid="106"><![CDATA[link 6@*:*<>*:*;email_body_url_count>3;]]></task>
	<task rule_id="485" rule_group="test106" rule_mode="0" rule_groupid="106"><![CDATA[link 6@*:*<>*:*;email_body_type_count>3;]]></task>
	<task rule_id="486" rule_group="test106" rule_mode="0" rule_groupid="106"><![CDATA[link 6@*:*<>*:*;email_attachment_count>3;]]></task>
	<task rule_id="487" rule_group="test106" rule_mode="0" rule_groupid="106"><![CDATA[link 6@*:*<>*:*;email_attachment_type_count>3;]]></task>
	<task rule_id="488" rule_group="test106" rule_mode="0" rule_groupid="106"><![CDATA[link 6@*:*<>*:*;email_attachment_md5_count>3;]]></task>
	<task rule_id="489" rule_group="test106" rule_mode="0" rule_groupid="106"><![CDATA[link 6@*:*<>*:*;email_attachment_path>3;]]></task>
	<task rule_id="490" rule_group="test106" rule_mode="0" rule_groupid="106"><![CDATA[link 6@*:*<>*:*;email_header_set_count>3;]]></task>
	<task rule_id="491" rule_group="test106" rule_mode="0" rule_groupid="106"><![CDATA[link 6@*:*<>*:*;email_msg_id_count>3;]]></task>
	<task rule_id="492" rule_group="test106" rule_mode="0" rule_groupid="106"><![CDATA[link 6@*:*<>*:*;email_mime_version_count>3;]]></task>

     <task rule_id="493" rule_group="coap" rule_mode="0" rule_groupid="106"><![CDATA[link 17@*:*<>*:*;coap_req_type>0;]]></task>
     <task rule_id="494" rule_group="coap" rule_mode="0" rule_groupid="106"><![CDATA[link 17@*:*<>*:*;coap_req_code~".";]]></task>
     <task rule_id="495" rule_group="coap" rule_mode="0" rule_groupid="106"><![CDATA[link 17@*:*<>*:*;coap_req_option_host~".";]]></task>
     <task rule_id="496" rule_group="coap" rule_mode="0" rule_groupid="106"><![CDATA[link 17@*:*<>*:*;coap_req_option_port<1024;]]></task>
     <task rule_id="497" rule_group="coap" rule_mode="0" rule_groupid="106"><![CDATA[link 17@*:*<>*:*;coap_req_path~".";]]></task>
     <task rule_id="498" rule_group="coap" rule_mode="0" rule_groupid="106"><![CDATA[link 17@*:*<>*:*;coap_req_uri~".";]]></task>
     <task rule_id="499" rule_group="coap" rule_mode="0" rule_groupid="106"><![CDATA[link 17@*:*<>*:*;coap_req_option_proxy_uri~".";]]></task>
     <task rule_id="500" rule_group="coap" rule_mode="0" rule_groupid="106"><![CDATA[link 17@*:*<>*:*;coap_req_option_proxy_scheme~".";]]></task>
     <task rule_id="501" rule_group="coap" rule_mode="0" rule_groupid="106"><![CDATA[link 17@*:*<>*:*;coap_req_option_content_format~".";]]></task>
     <task rule_id="502" rule_group="coap" rule_mode="0" rule_groupid="106"><![CDATA[link 17@*:*<>*:*;coap_resp_type>10;]]></task>
     <task rule_id="503" rule_group="coap" rule_mode="0" rule_groupid="106"><![CDATA[link 17@*:*<>*:*;coap_resp_code~".";]]></task>
     <task rule_id="504" rule_group="coap" rule_mode="0" rule_groupid="106"><![CDATA[link 17@*:*<>*:*;coap_resp_option_location_path~".";]]></task>
     <task rule_id="505" rule_group="coap" rule_mode="0" rule_groupid="106"><![CDATA[link 17@*:*<>*:*;coap_resp_option_location_query~".";]]></task>
     <task rule_id="506" rule_group="coap" rule_mode="0" rule_groupid="106"><![CDATA[link 17@*:*<>*:*;coap_resp_option_content_format~".";]]></task>
     
     <task rule_id="506" rule_group="dns506" rule_groupid="134" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_answer_name~"\w";]]></task>
     <task rule_id="507" rule_group="dns507" rule_groupid="135" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_answer_type~"\w";]]></task>
     <task rule_id="508" rule_group="dns508" rule_groupid="136" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_answer_rrs>1;]]></task>
     <task rule_id="509" rule_group="dns509" rule_groupid="137" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_authority_rrs>2;]]></task>
     <task rule_id="510" rule_group="dns510" rule_groupid="138" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_additional_rrs>3;]]></task>
     <task rule_id="511" rule_group="dns511" rule_groupid="139" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_cname~"\w";]]></task>
     <task rule_id="512" rule_group="dns512" rule_groupid="140" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_cname_count>3;]]></task>
     <task rule_id="513" rule_group="dns513" rule_groupid="141" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_a_ipv4~"\w";]]></task>
     <task rule_id="514" rule_group="dns514" rule_groupid="142" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_a_ipv4_count>3;]]></task>
     <task rule_id="515" rule_group="dns515" rule_groupid="143" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_aaaa_ipv6~"\w";]]></task>
     <task rule_id="516" rule_group="dns516" rule_groupid="144" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_aaaa_ipv6_count>3;]]></task>
     <task rule_id="517" rule_group="dns517" rule_groupid="145" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_aip_asn~"\w";]]></task>
     <task rule_id="518" rule_group="dns518" rule_groupid="146" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_aip_country~"\w";]]></task>
     <task rule_id="519" rule_group="dns519" rule_groupid="147" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_mx_host~"\w";]]></task>
     <task rule_id="520" rule_group="dns520" rule_groupid="148" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_mx_host_count>3;]]></task>
     <task rule_id="521" rule_group="dns521" rule_groupid="149" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_mx_ipv4~"\w";]]></task>
     <task rule_id="522" rule_group="dns522" rule_groupid="150" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_mx_ipv4_count>3;]]></task>
     <task rule_id="523" rule_group="dns523" rule_groupid="151" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_mx_ipv6~"\w";]]></task>
     <task rule_id="524" rule_group="dns524" rule_groupid="152" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_mx_ipv6_count>3;]]></task>
     <task rule_id="525" rule_group="dns525" rule_groupid="153" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_mx_ip_asn~"\w";]]></task>
     <task rule_id="526" rule_group="dns526" rule_groupid="154" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_mx_ip_country>3;]]></task>
     <task rule_id="527" rule_group="dns527" rule_groupid="155" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_ns_host~"\w";]]></task>
     <task rule_id="528" rule_group="dns528" rule_groupid="156" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_ns_host_count>3;]]></task>
     <task rule_id="529" rule_group="dns529" rule_groupid="157" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_ns_ipv4~"\w";]]></task>
     <task rule_id="530" rule_group="dns530" rule_groupid="158" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_ns_ipv4_count>3;]]></task>
     <task rule_id="531" rule_group="dns531" rule_groupid="159" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_ns_ipv6~"\w";]]></task>
     <task rule_id="532" rule_group="dns532" rule_groupid="160" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_ns_ipv6_count>3;]]></task>
     <task rule_id="533" rule_group="dns533" rule_groupid="161" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_ns_ip_asn~"\w";]]></task>
     <task rule_id="534" rule_group="dns534" rule_groupid="162" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_ns_ip_country>3;]]></task>
     <task rule_id="535" rule_group="dns535" rule_groupid="163" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_spf~"\w";]]></task>
     <task rule_id="536" rule_group="dns536" rule_groupid="164" rule_mode="0"><![CDATA[link 17@*:*<>*:*;dns_txt~"\w";]]></task>

     <task rule_id="537" rule_group="tls537" rule_groupid="537" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_version_client>1;]]></task>
     <task rule_id="538" rule_group="tls538" rule_groupid="538" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_version_server>1;]]></task>
     <task rule_id="539" rule_group="tls539" rule_groupid="539" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_handshake_length_client>1;]]></task>
     <task rule_id="540" rule_group="tls540" rule_groupid="540" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_handshake_length_server>2;]]></task>
     <task rule_id="541" rule_group="tls541" rule_groupid="541" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_handshake_random_time_client~"\w";]]></task>
     <task rule_id="542" rule_group="tls542" rule_groupid="542" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_handshake_random_time_server~"\w";]]></task>
     <task rule_id="543" rule_group="tls543" rule_groupid="543" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_handshake_random_bytes_client~"\w";]]></task>
     <task rule_id="544" rule_group="tls544" rule_groupid="544" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_handshake_random_bytes_server~"\w";]]></task>
     <task rule_id="545" rule_group="tls545" rule_groupid="545" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_session_id_client~"\w";]]></task>
     <task rule_id="546" rule_group="tls546" rule_groupid="546" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_session_id_server~"\w";]]></task>
     <task rule_id="547" rule_group="tls547" rule_groupid="547" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_ciphersuites_client~"\w";]]></task>
     <task rule_id="548" rule_group="tls548" rule_groupid="548" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_ciphersuites_client_count>2;]]></task>
     <task rule_id="549" rule_group="tls549" rule_groupid="549" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_ciphersuite_server~"\w";]]></task>
     <task rule_id="550" rule_group="tls550" rule_groupid="550" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_compress_methods_client~"\w";]]></task>
     <task rule_id="551" rule_group="tls551" rule_groupid="551" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_compress_method_server~".";]]></task>
     <task rule_id="552" rule_group="tls552" rule_groupid="552" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_extend_types_client~"\w";]]></task>
     <task rule_id="553" rule_group="tls553" rule_groupid="553" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_extend_client_count>3;]]></task>
     <task rule_id="554" rule_group="tls554" rule_groupid="554" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_extend_types_server~"\w";]]></task>
     <task rule_id="555" rule_group="tls555" rule_groupid="555" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_extend_server_count>3;]]></task>
     <task rule_id="556" rule_group="tls556" rule_groupid="556" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_extend_grease>4;]]></task>
     <task rule_id="557" rule_group="tls557" rule_groupid="557" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_ja3_client~"\w";]]></task>
     <task rule_id="558" rule_group="tls558" rule_groupid="558" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_ja3s_server~"\w";]]></task>
     <task rule_id="559" rule_group="tls559" rule_groupid="559" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_joy_client~"\w";]]></task>
     <task rule_id="560" rule_group="tls560" rule_groupid="560" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_joys_server~"\w";]]></task>
     <task rule_id="561" rule_group="tls561" rule_groupid="561" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_session_ticket_client~"\w";]]></task>
     <task rule_id="562" rule_group="tls562" rule_groupid="562" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_session_ticket_server~"\w";]]></task>
     <task rule_id="563" rule_group="tls563" rule_groupid="563" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_sni~"\w";]]></task>
     <task rule_id="564" rule_group="tls564" rule_groupid="564" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_sni_type<1;]]></task>
     <task rule_id="565" rule_group="tls565" rule_groupid="565" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_cert_hashes_client~"\w";]]></task>
     <task rule_id="566" rule_group="tls566" rule_groupid="566" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_cert_hashes_server~"\w";]]></task>
     <task rule_id="567" rule_group="tls567" rule_groupid="567" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_cert_client_count>1;]]></task>
     <task rule_id="568" rule_group="tls568" rule_groupid="568" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_cert_server_count>2;]]></task>
     <task rule_id="569" rule_group="tls569" rule_groupid="569" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_cert_client_length>3;]]></task>
     <task rule_id="570" rule_group="tls570" rule_groupid="570" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_cert_server_length>4;]]></task>
     <task rule_id="571" rule_group="tls571" rule_groupid="571" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_extend_ec_format_client>1;]]></task>
     <task rule_id="572" rule_group="tls572" rule_groupid="572" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_extend_ec_format_server>1;]]></task>
     <task rule_id="573" rule_group="tls573" rule_groupid="573" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_extend_ec_groups_client~"\w";]]></task>
     <task rule_id="574" rule_group="tls574" rule_groupid="574" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_extend_ec_groups_server~"\w";]]></task>
     <task rule_id="575" rule_group="tls575" rule_groupid="575" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_cert_path~"\w";]]></task>
     <task rule_id="576" rule_group="tls576" rule_groupid="576" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_cert_exist>1;]]></task>
     <task rule_id="577" rule_group="tls577" rule_groupid="577" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_cert_intact>1;]]></task>
     <task rule_id="578" rule_group="tls578" rule_groupid="578" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tls_leaf_cert_days_remaining>1;]]></task>

     <task rule_id="579" rule_group="http579" rule_groupid="579" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_version_client~"\w";]]></task>
     <task rule_id="580" rule_group="http580" rule_groupid="580" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_version_client_count~"\w";]]></task>
     <task rule_id="581" rule_group="http581" rule_groupid="581" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_method~"\w";]]></task>
     <task rule_id="582" rule_group="http582" rule_groupid="582" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_method_count>2;]]></task>
     <task rule_id="583" rule_group="http583" rule_groupid="583" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_referer~"\w";]]></task>
     <task rule_id="584" rule_group="http584" rule_groupid="584" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_header_client~"\w";]]></task>
     <task rule_id="585" rule_group="http585" rule_groupid="585" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_header_client_md5~"\w";]]></task>
     <task rule_id="586" rule_group="http586" rule_groupid="586" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_header_client_count>2;]]></task>
     <task rule_id="587" rule_group="http587" rule_groupid="587" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_content_disposition_client~"\w";]]></task>
     <task rule_id="588" rule_group="http588" rule_groupid="588" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_accept~"\w";]]></task>
     <task rule_id="589" rule_group="http589" rule_groupid="589" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_accept_language~"\w";]]></task>
     <task rule_id="590" rule_group="http590" rule_groupid="590" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_accept_encoding~"\w";]]></task>
     <task rule_id="591" rule_group="http591" rule_groupid="591" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_auth~"\w";]]></task>
     <task rule_id="592" rule_group="http592" rule_groupid="592" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_auth_count~"\w";]]></task>
     <task rule_id="593" rule_group="http593" rule_groupid="593" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_auth_user~"\w";]]></task>
     <task rule_id="594" rule_group="http594" rule_groupid="594" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_auth_user_count>2;]]></task>
     <task rule_id="595" rule_group="http595" rule_groupid="595" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_from~"\w";]]></task>
     <task rule_id="596" rule_group="http596" rule_groupid="596" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_host~"\w";]]></task>
     <task rule_id="597" rule_group="http597" rule_groupid="597" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_host_count>3;]]></task>
     <task rule_id="598" rule_group="http598" rule_groupid="598" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_uri~"\w";]]></task>
     <task rule_id="599" rule_group="http599" rule_groupid="599" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_uri_count>3;]]></task>
     <task rule_id="600" rule_group="http600" rule_groupid="600" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_uri_path~"\w";]]></task>
     <task rule_id="601" rule_group="http601" rule_groupid="601" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_uri_path_count>3;]]></task>
     <task rule_id="602" rule_group="http602" rule_groupid="602" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_uri_key~"\w";]]></task>
     <task rule_id="603" rule_group="http603" rule_groupid="603" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_uri_key_count>3;]]></task>
     <task rule_id="604" rule_group="http604" rule_groupid="604" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_uri_search~"\w";]]></task>
     <task rule_id="605" rule_group="http605" rule_groupid="605" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_range~"\w";]]></task>
     <task rule_id="606" rule_group="http606" rule_groupid="606" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_user_agent~"\w";]]></task>
     <task rule_id="607" rule_group="http607" rule_groupid="607" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_user_ragent_count>3;]]></task>
     <task rule_id="608" rule_group="http608" rule_groupid="608" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_body_client~"\w";]]></task>
     <task rule_id="609" rule_group="http609" rule_groupid="609" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_body_client_md5~"\w";]]></task>
     <task rule_id="610" rule_group="http610" rule_groupid="610" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_cookie~"\w";]]></task>
     <task rule_id="611" rule_group="http611" rule_groupid="611" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_cookie_key~"\w";]]></task>
     <task rule_id="612" rule_group="http612" rule_groupid="612" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_cookie_key_count>4;]]></task>
     <task rule_id="613" rule_group="http613" rule_groupid="613" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_imei~"\w";]]></task>
     <task rule_id="614" rule_group="http614" rule_groupid="614" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_imsi~"\w";]]></task>
     <task rule_id="615" rule_group="http615" rule_groupid="615" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_content_encoding_client~"\w";]]></task>
     <task rule_id="616" rule_group="http616" rule_groupid="616" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_content_length_client~"\w";]]></task>
     <task rule_id="617" rule_group="http617" rule_groupid="617" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_xforwarded_for~"\w";]]></task>
     <task rule_id="618" rule_group="http618" rule_groupid="618" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_xforwarded_for_count>1;]]></task>
     <task rule_id="619" rule_group="http619" rule_groupid="619" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_version_server~"\w";]]></task>
     <task rule_id="620" rule_group="http620" rule_groupid="620" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_version_server_count>1;]]></task>
     <task rule_id="621" rule_group="http621" rule_groupid="621" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_header_server~"\w";]]></task>
     <task rule_id="622" rule_group="http622" rule_groupid="622" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_header_server_md5~"\w";]]></task>
     <task rule_id="623" rule_group="http623" rule_groupid="623" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_header_server_count>1;]]></task>
     <task rule_id="624" rule_group="http624" rule_groupid="624" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_body_server~"\w";]]></task>
     <task rule_id="625" rule_group="http625" rule_groupid="625" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_body_server_md5~"\w";]]></task>
     <task rule_id="626" rule_group="http626" rule_groupid="626" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_body_server_md5_count>1;]]></task>
     <task rule_id="627" rule_group="http627" rule_groupid="627" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_content_disposition_server~"\w";]]></task>
     <task rule_id="628" rule_group="http628" rule_groupid="628" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_content_encoding_server~"\w";]]></task>
     <task rule_id="629" rule_group="http629" rule_groupid="629" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_content_encoding_server_count>1;]]></task>
     <task rule_id="630" rule_group="http630" rule_groupid="630" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_title~"\w";]]></task>
     <task rule_id="631" rule_group="http631" rule_groupid="631" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_loc~"\w";]]></task>
     <task rule_id="632" rule_group="http632" rule_groupid="632" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_vary~"\w";]]></task>
     <task rule_id="633" rule_group="http633" rule_groupid="633" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_content_language~"\w";]]></task>
     <task rule_id="634" rule_group="http634" rule_groupid="634" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_server~"\w";]]></task>
     <task rule_id="635" rule_group="http635" rule_groupid="635" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_server_count>1;]]></task>
     <task rule_id="636" rule_group="http636" rule_groupid="636" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_set_cookie_key~"\w";]]></task>
     <task rule_id="637" rule_group="http637" rule_groupid="637" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_set_cookie_value~"\w";]]></task>
     <task rule_id="638" rule_group="http638" rule_groupid="638" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_content_length_server>1;]]></task>
     <task rule_id="639" rule_group="http639" rule_groupid="639" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_x_powered_by~"\w";]]></task>
     <task rule_id="640" rule_group="http640" rule_groupid="640" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_date~"\w";]]></task>
     <task rule_id="641" rule_group="http641" rule_groupid="641" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_transfer_encoding~"\w";]]></task>
     <task rule_id="642" rule_group="http642" rule_groupid="642" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_xsinkhole~"\w";]]></task>
     <task rule_id="643" rule_group="http643" rule_groupid="643" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_via~"\w";]]></task>
     <task rule_id="644" rule_group="http644" rule_groupid="644" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_via_count>1;]]></task>
     <task rule_id="645" rule_group="http645" rule_groupid="645" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_statuscode~"\w";]]></task>
     <task rule_id="646" rule_group="http646" rule_groupid="646" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_statuscode_count>1;]]></task>
     <task rule_id="647" rule_group="http647" rule_groupid="647" rule_mode="0"><![CDATA[link 6@*:*<>*:*;http_attachment_path~"\w";]]></task>
    
     <task rule_id="648" rule_group="trans648" rule_groupid="648" rule_mode="0"><![CDATA[link 6@*:*<>*:*;trans_payload_hex_source~"\w";]]></task>
     <task rule_id="649" rule_group="trans649" rule_groupid="649" rule_mode="0"><![CDATA[link 6@*:*<>*:*;trans_payload_hex_destination~"\w";]]></task>
     <task rule_id="650" rule_group="trans650" rule_groupid="650" rule_mode="0"><![CDATA[link 6@*:*<>*:*;trans_payload_length_seq_source~"\w";]]></task>
     <task rule_id="651" rule_group="trans651" rule_groupid="651" rule_mode="0"><![CDATA[link 6@*:*<>*:*;trans_payload_length_seq_destination~"\w";]]></task>
     <task rule_id="652" rule_group="trans652" rule_groupid="652" rule_mode="0"><![CDATA[link 6@*:*<>*:*;trans_packet_length_source_max>12;]]></task>
     <task rule_id="653" rule_group="trans653" rule_groupid="653" rule_mode="0"><![CDATA[link 6@*:*<>*:*;trans_packet_length_destination_max>13;]]></task>
     <task rule_id="654" rule_group="trans654" rule_groupid="654" rule_mode="0"><![CDATA[link 6@*:*<>*:*;trans_packet_length_source_min>14;]]></task>
     <task rule_id="655" rule_group="trans655" rule_groupid="655" rule_mode="0"><![CDATA[link 6@*:*<>*:*;trans_packet_length_destination_min>15;]]></task>
     <task rule_id="656" rule_group="trans656" rule_groupid="656" rule_mode="0"><![CDATA[link 6@*:*<>*:*;trans_packet_length_source_high_frequency>16;]]></task>
     <task rule_id="657" rule_group="trans657" rule_groupid="657" rule_mode="0"><![CDATA[link 6@*:*<>*:*;trans_packet_length_destination_high_frequency>17;]]></task>
     <task rule_id="658" rule_group="trans658" rule_groupid="658" rule_mode="0"><![CDATA[link 6@*:*<>*:*;trans_packet_interval_source_max>18;]]></task>
     <task rule_id="659" rule_group="trans659" rule_groupid="659" rule_mode="0"><![CDATA[link 6@*:*<>*:*;trans_packet_interval_destination_max>19;]]></task>
     <task rule_id="660" rule_group="trans660" rule_groupid="660" rule_mode="0"><![CDATA[link 6@*:*<>*:*;trans_packet_interval_source_min>20;]]></task>
     <task rule_id="661" rule_group="trans661" rule_groupid="661" rule_mode="0"><![CDATA[link 6@*:*<>*:*;trans_packet_interval_destination_min>21;]]></task>
     <task rule_id="662" rule_group="trans662" rule_groupid="662" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tcp_first_flag~"\w";]]></task>
     <task rule_id="663" rule_group="trans663" rule_groupid="663" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tcp_syn_seq_source>2;]]></task>
     <task rule_id="664" rule_group="trans664" rule_groupid="664" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tcp_syn_seq_destination>2;]]></task>
     <task rule_id="665" rule_group="trans665" rule_groupid="665" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tcp_window_size_source>2;]]></task>
     <task rule_id="666" rule_group="trans666" rule_groupid="666" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tcp_window_size_destination>2;]]></task>
     <task rule_id="667" rule_group="trans667" rule_groupid="667" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tcp_options_source~"\w";]]></task>
     <task rule_id="668" rule_group="trans668" rule_groupid="668" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tcp_options_destination~"\w";]]></task>
     <task rule_id="669" rule_group="trans669" rule_groupid="669" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tcp_flags_source~"\w";]]></task>
     <task rule_id="670" rule_group="trans670" rule_groupid="670" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tcp_flags_destination~"\w";]]></task>
     <task rule_id="671" rule_group="trans671" rule_groupid="671" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tcp_flags_fin_count>1;]]></task>
     <task rule_id="672" rule_group="trans672" rule_groupid="672" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tcp_flags_syn_count>1;]]></task>
     <task rule_id="673" rule_group="trans673" rule_groupid="673" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tcp_flags_rst_count>1;]]></task>
     <task rule_id="674" rule_group="trans674" rule_groupid="674" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tcp_flags_psh_count>1;]]></task>
     <task rule_id="675" rule_group="trans675" rule_groupid="675" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tcp_flags_ack_count>1;]]></task>
     <task rule_id="676" rule_group="trans676" rule_groupid="676" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tcp_flags_urg_count>1;]]></task>
     <task rule_id="677" rule_group="trans677" rule_groupid="677" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tcp_flags_ece_count>1;]]></task>
     <task rule_id="678" rule_group="trans678" rule_groupid="678" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tcp_flags_cwr_count>1;]]></task>
     <task rule_id="679" rule_group="trans679" rule_groupid="679" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tcp_flags_ns_count>1;]]></task>
     <task rule_id="680" rule_group="trans680" rule_groupid="680" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tcp_flags_synack_count>1;]]></task>
     <task rule_id="681" rule_group="trans681" rule_groupid="681" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tcp_established>1;]]></task>
     <task rule_id="682" rule_group="trans682" rule_groupid="682" rule_mode="0"><![CDATA[link 6@*:*<>*:*;tcp_finished>1;]]></task>
    
     <task rule_id="683" rule_group="ip683" rule_groupid="683" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_version>1;]]></task>
     <task rule_id="684" rule_group="ip684" rule_groupid="684" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_source~"\w";]]></task>
     <task rule_id="685" rule_group="ip685" rule_groupid="685" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ipv6_source~"\w";]]></task>
     <task rule_id="686" rule_group="ip686" rule_groupid="686" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_destination~"\w";]]></task>
     <task rule_id="687" rule_group="ip687" rule_groupid="687" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ipv6_destination~"\w";]]></task>
     <task rule_id="688" rule_group="ip688" rule_groupid="688" rule_mode="0"><![CDATA[link 6@*:*<>*:*;port_source>13;]]></task>
     <task rule_id="689" rule_group="ip689" rule_groupid="689" rule_mode="0"><![CDATA[link 6@*:*<>*:*;port_destination>14;]]></task>
     <task rule_id="690" rule_group="ip690" rule_groupid="690" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_proto>15;]]></task>
     <task rule_id="691" rule_group="ip691" rule_groupid="691" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_country_source~"\w";]]></task>
     <task rule_id="692" rule_group="ip692" rule_groupid="692" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_state_source~"\w";]]></task>
     <task rule_id="693" rule_group="ip693" rule_groupid="693" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_city_source~"\w";]]></task>
     <task rule_id="694" rule_group="ip694" rule_groupid="694" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_longitude_source~"\w";]]></task>
     <task rule_id="695" rule_group="ip695" rule_groupid="695" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_latitude_source~"\w";]]></task>
     <task rule_id="696" rule_group="ip696" rule_groupid="696" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_isp_source~"\w";]]></task>
     <task rule_id="697" rule_group="ip697" rule_groupid="697" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_asn_source~"\w";]]></task>
     <task rule_id="698" rule_group="ip698" rule_groupid="698" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_country_destination~"\w";]]></task>
     <task rule_id="699" rule_group="ip699" rule_groupid="699" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_state_destination~"\w";]]></task>
     <task rule_id="700" rule_group="ip700" rule_groupid="700" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_city_destination~"\w";]]></task>
     <task rule_id="701" rule_group="ip701" rule_groupid="701" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_longitude_destination~".";]]></task>
     <task rule_id="702" rule_group="ip702" rule_groupid="702" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_latitude_destination~".";]]></task>
     <task rule_id="703" rule_group="ip703" rule_groupid="703" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_isp_destination~"\w";]]></task>
     <task rule_id="704" rule_group="ip704" rule_groupid="704" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_asn_destination~"\w";]]></task>
     <task rule_id="705" rule_group="ip705" rule_groupid="705" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_proto_path~"\w";]]></task>
     <task rule_id="706" rule_group="ip706" rule_groupid="706" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_base_proto~"\w";]]></task>
     <task rule_id="707" rule_group="ip707" rule_groupid="707" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_upper_proto~"\w";]]></task>
     <task rule_id="708" rule_group="ip708" rule_groupid="708" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_direction>0;]]></task>
     <task rule_id="709" rule_group="ip709" rule_groupid="709" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_begin_time>10;]]></task>
     <task rule_id="710" rule_group="ip710" rule_groupid="710" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_end_time>11;]]></task>
     <task rule_id="711" rule_group="ip711" rule_groupid="711" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_duration>3;]]></task>
     <task rule_id="712" rule_group="ip712" rule_groupid="712" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_ttl_source>4;]]></task>
     <task rule_id="713" rule_group="ip713" rule_groupid="713" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_ttl_destination>5;]]></task>
     <task rule_id="714" rule_group="ip714" rule_groupid="714" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_packets>6;]]></task>
     <task rule_id="715" rule_group="ip715" rule_groupid="715" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_packets_source>7;]]></task>
     <task rule_id="716" rule_group="ip716" rule_groupid="716" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_packets_destination>8;]]></task>
     <task rule_id="717" rule_group="ip717" rule_groupid="717" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_bytes>9;]]></task>
     <task rule_id="718" rule_group="ip718" rule_groupid="718" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_bytes_source>10;]]></task>
     <task rule_id="719" rule_group="ip719" rule_groupid="719" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_bytes_destination>11;]]></task>
     <task rule_id="720" rule_group="ip720" rule_groupid="720" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_bytes_ratio>12;]]></task>
     <task rule_id="721" rule_group="ip721" rule_groupid="721" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_data_bytes>13;]]></task>
     <task rule_id="722" rule_group="ip722" rule_groupid="722" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_data_bytes_source>14;]]></task>
     <task rule_id="723" rule_group="ip723" rule_groupid="723" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_data_bytes_destination>15;]]></task>
     <task rule_id="724" rule_group="ip724" rule_groupid="724" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_data_bytes_ratio>16;]]></task>
     <task rule_id="725" rule_group="ip725" rule_groupid="725" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_desired_bytes>17;]]></task>
     <task rule_id="726" rule_group="ip726" rule_groupid="726" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_desired_bytes_source>18;]]></task>
     <task rule_id="727" rule_group="ip727" rule_groupid="727" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_desired_bytes_destination>19;]]></task>
     <task rule_id="728" rule_group="ip728" rule_groupid="728" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_stream~"\w";]]></task>
     <task rule_id="729" rule_group="ip729" rule_groupid="729" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_stream_source~"\w";]]></task>
     <task rule_id="730" rule_group="ip730" rule_groupid="730" rule_mode="0"><![CDATA[link 6@*:*<>*:*;ip_stream_destination~"\w";]]></task>

 </task>
   <task task_id="BGP" unit="test1" method="KS-YSJ" task_sub_type="11">
	   <!---<rule rule_id="201" rule_group="0" rule_groupid="28" rule_mode="0" hit="1"><![CDATA[link 6@*:*<>*:*; call_plugin("dport_8080.yar", "");packet_dump(0,0);report();]]></rule>
	<rule rule_id="202" rule_group="1" rule_groupid="29" rule_mode="0" hit="1"><![CDATA[link 6@*:*<>*:*; call_plugin("unittest_sip_test.yar", "");packet_dump(0,0);report();]]></rule>
	   <rule rule_id="203" rule_group="1" rule_groupid="29" rule_mode="0" hit="1"><![CDATA[link 6@*:*<>*:*; call_plugin("mystream.yar", "");packet_dump(0,0);report();]]></rule>-->
        <rule rule_id="204" rule_group="1" rule_groupid="29" rule_mode="0" hit="1"><![CDATA[link 6@*:*<>*:*; call_plugin("proto_field_http.yar", "");packet_dump(0,0);report();]]></rule>
</task>

</taskparams>
