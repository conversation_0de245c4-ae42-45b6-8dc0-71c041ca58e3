#include "libsdt/sdt_types.h"

#ifndef __SDT_RULE_DYNAMIC_HEAD__
#define __SDT_RULE_DYNAMIC_HEAD__

#define  DYNAMIC_ACL_VALUE_MAX 16

#ifdef __cplusplus
extern "C" {
#endif

void dynamic_show_sdt_acl(const struct sdt_acl_t *r, FILE *f);
struct rte_acl_ctx* dynamic_rule_build_ipv4_flex(struct sdt_acl_t *acls[], int num);
struct rte_acl_ctx* dynamic_rule_build_ipv6_flex(struct sdt_acl_t *acls[], int num);
int dynamic_rule_acl_match(const struct rte_acl_ctx *ctx, const struct pkt_tuple_t *pkt, uint32_t *match_result, int result_size);

struct SDTRuleAction* dynamic_rule_by_index(int index);

#ifdef __cplusplus
}
#endif

#endif


