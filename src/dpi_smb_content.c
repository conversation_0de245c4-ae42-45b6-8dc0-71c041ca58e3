//SMB Decode Design By chunli.
//难度 2颗星 (2/5)

#include <libgen.h>
#include <glib.h>
#include <netinet/in.h>
#include <rte_mbuf.h>
#include <glib.h>
#include <jhash.h>
#include <unistd.h>
#include <sys/stat.h>
#include <sys/types.h>

#include "utils/dpi_utils.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_dissector.h"
#include "charsets.h"
#include "tcp_rsm.h"
#include "dpi_file_rsm.h"
#include "utils/dpi_utils.h"

//#define CHUNLI_DEBUG  // 追踪信息 编译开关, 取消注释生效
#ifdef CHUNLI_DEBUG
#define DBG(...) (printf(__VA_ARGS__))
#define ABORT(...) {DBG(__VA_ARGS__);abort();}
#else
#define DBG(...) {}
#define ABORT(...) {}
#endif

#define SMB_MAX_CMD     256
#define SMB_CMD_REQ     0
#define SMB_CMD_RES     1
#define SMB_CMD_CREATE  0xa2
#define SMB_CMD_READ    0x2e
#define SMB_CMD_WRITE   0x2f
#define SMB_CMD_TRANS2  0x32
#define SMB_CMD_CLOSE   0x04
#define SMB_CMD_SESSION_SETUP_ANDX  0x73
#define SMB_CMD_TREE_CONNECT_ANDX   0x75
#define SMB_CMD_NEGOTIATE           0x72

extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;
static int dissect_smb_msg(struct flow_info *flow, int C2S, const uint8_t *payload, uint32_t payload_len);

static uint64_t SMB_pkt_before_rsm_num;
static uint64_t SMB_pkt_after_rsm_num;
static uint64_t SMB_pkt_miss_rsm_num;
static uint64_t SMB_pkt_drop_length;
static uint64_t SMB_Good_index;
static uint64_t SMB_File_index;

struct smb_info_t;

struct {
    const char *cmd;
    int (*func[2]) (struct flow_info *flow, struct dpi_pkt_st *pkt, int C2S, uint32_t offset, struct smb_info_t *info);
} smb_dissector[SMB_MAX_CMD];

struct smb_hdr_t
{
    uint32_t ServerComponent;
    uint8_t  Cmd;
    uint32_t NtStatus;
    uint8_t  Flags;
    uint16_t Flags2;
    uint16_t PidHigh;
    uint64_t Signature;
    uint16_t Reserved;
    uint16_t Tid;
    uint16_t Pid;
    uint16_t Uid;
    uint16_t Mid;
} __attribute__((packed));

struct smb_create_request_t
{
    uint8_t  WordCount;
    uint8_t  AndXCommand;
    uint8_t  Reserved1;
    uint16_t AndXOffset;
    uint8_t  Reserved2;
    uint16_t FileNameLen;
    uint32_t CreateFlags;
    uint32_t RootFID;
    uint32_t AccessMask;
    uint64_t AllocationSize;
    uint32_t FileAttributes;
    uint32_t ShareAccess;
    uint32_t Disposition;
    uint32_t CreateOptions;
    uint32_t Impersonation;
    uint8_t  SecurityFlags;
    uint16_t ByteCount;
} __attribute__((packed));

struct smb_create_response_t
{
    uint8_t  WordCount;
    uint8_t  AndXCommand;
    uint8_t  Reserved;
    uint16_t AndXOffset;
    uint8_t  Oplocklevel;
    uint16_t FID;
    uint32_t Createaction;
    uint64_t Created;
    uint64_t LastAccess;
    uint64_t LastWrite;
    uint64_t Change;
    uint32_t FileAttributes;
    uint64_t AllocationSize;
    uint64_t EndOfFile;
    uint16_t FileType;
    uint16_t IPCState;
    uint8_t  IsDirectory;
    uint32_t VolumeGUID[4];
    uint64_t ServerUnique;
    uint32_t MaximalAccessRights;
    uint32_t GuestMaximalAccessRights;
    uint16_t ByteCount;
} __attribute__((packed));

struct smb_read_request_t
{
    uint8_t  WordCount;
    uint8_t  AndXCommand;
    uint8_t  Reserved;
    uint16_t AndXOffset;
    uint16_t FID;
    uint32_t Offset;
    uint16_t MaxCountLow;
    uint16_t MinCount;
    uint32_t MaxCountHigh;
    uint16_t Remaining;
    uint32_t HighOffset;
    uint16_t ByteCount;
} __attribute__((packed));

struct smb_read_response_t
{
    uint8_t  WordCount;
    uint8_t  AndXCommand;
    uint8_t  Reserved1;
    uint16_t AndXOffset;
    uint16_t Remaining;
    uint16_t DataCompactionMode;
    uint16_t Reserved2;
    uint16_t DataLengthLow;
    uint16_t DataOffset;
    uint32_t DataLengthHigh;
    uint32_t Reserved3;
    uint16_t Reserved4;
    uint16_t ByteCount;
} __attribute__((packed));

struct smb_write_request_t
{
    uint8_t  WordCount;
    uint8_t  AndXCommand;
    uint8_t  Reserved1;
    uint16_t AndXOffset;
    uint16_t FID;
    uint32_t Offset;
    uint32_t Reserved2;
    uint16_t WriteMode;
    uint16_t Remaining;
    uint16_t DataLengthHigh;
    uint16_t DataLengthLow;
    uint16_t DataOffset;
    uint32_t HighOffset;
    uint16_t ByteCount;
} __attribute__((packed));

struct smb_trans2_request_t
{
    uint8_t  WordCount;
    uint16_t TotalParameterCount;
    uint16_t TotalDataCount;
    uint16_t MaxParameterCount;
    uint16_t MaxDataCount;
    uint8_t  MaxSetupCount;
    uint8_t  Reserved1;
    uint16_t Flags;
    uint32_t Timeout;
    uint16_t Reserved2;
    uint16_t ParameterCount;
    uint16_t ParameterOffset;
    uint16_t DataCount;
    uint16_t DataOffset;
    uint8_t  SetupCount;
    uint8_t  Reserved3;
    uint16_t Subcommand;
    uint16_t ByteCount;
    uint8_t  Reserved4;
    uint16_t Reserved5;
} __attribute__((packed));

struct smb_set_file_info_t
{
    uint16_t FID;
    uint16_t LevelOfInterest;
    uint16_t Reserved;
} __attribute__((packed));

struct smb_set_file_info_eof_t
{
    uint16_t Reserved;
    uint64_t eof;
} __attribute__((packed));

struct smb_close_request_t
{
    uint8_t   WordCount;
    uint16_t  FID;
    uint32_t  LastWrite;
    uint16_t  ByteCount;
} __attribute__((packed));

//存储SMB的解析内容
struct smb_info_t
{
    //方向
    uint8_t  Resp;

    //SMB HDR
    uint8_t  Cmd;
    uint32_t NtStatus;
    uint8_t  Flags;
    uint16_t Flags2;
    uint16_t PidHigh;
    uint64_t Signature;
    uint16_t Tid;
    uint16_t Pid;
    uint16_t Uid;
    uint16_t Mid;
    uint64_t uuid;

    //SMB MSG
    uint32_t AccessMask;
    uint64_t AllocationSize;
    uint8_t  AndXCommand;
    uint16_t AndXOffset;
    uint16_t ByteCount;
    uint64_t Change;
    uint32_t Createaction;
    uint64_t Created;
    uint32_t CreateFlags;
    uint32_t CreateOptions;
    uint16_t DataCompactionMode;
    uint32_t DataLengthHigh;
    uint16_t DataLengthLow;
    uint16_t DataOffset;
    uint32_t Disposition;
    uint64_t EndOfFile;
    uint16_t FID;
    uint32_t FileAttributes;
    uint16_t FileNameLen;
    uint16_t FileType;
    uint32_t GuestMaximalAccessRights;
    uint32_t HighOffset;
    uint32_t Impersonation;
    uint16_t IPCState;
    uint8_t  IsDirectory;
    uint64_t LastAccess;
    uint64_t LastWrite;
    uint32_t MaxCountHigh;
    uint16_t MaxCountLow;
    uint32_t MaximalAccessRights;
    uint16_t MinCount;
    uint32_t Offset;
    uint8_t  Oplocklevel;
    uint16_t Remaining;
    uint32_t RootFID;
    uint8_t  SecurityFlags;
    uint64_t ServerUnique;
    uint32_t ShareAccess;
    uint32_t VolumeGUID[4];
    uint8_t  WordCount;
    uint16_t WriteMode;

    // 添加缺失的字段用于解析
    char ClientSB_Username[64];
    char ClientPassword[64];
    char ClientPath[256];
    char ServerNativeOS[256];
    const char *ServerAction;
    const uint8_t *authtype;
};

//SMB解复用
struct smb_cache_t
{
    uint8_t mem[4000];
    int     hold;
};

//HASH KEY [filename] SMB落盘状态
struct smb_file_t
{
    struct smb_session *smb;
    char                name[1024];
    char                local[1024];
    char                suffix[1024];
    char                head[1024];
    int                 C2S;
    uint64_t            eof;
    uint32_t            tcp_miss_at_seq;
    FILE               *fp;
    struct file_rsm_t  *file_rsm; //解决数据块 重叠问题
    struct smb_info_t   info; //内容还原的驱动力是文件输出, 文件输出时写TBL文件
};

//HASH KEY [fileid] SMB落盘状态
struct smb_guid_t
{
    uint32_t    fileid;
    char        filename[1024];
};

//SMB实时传输状态
struct smb_transfer_t
{
    struct smb_file_t *file; //记录着当前正在传输的文件
    uint32_t msg_length_cnt; //本批数据 已接收
    uint32_t msg_length;     //本批数据 长度
    uint64_t msg_offset;     //本批数据 偏移
};

//SMB实时请求应答 关联
struct smb_msgid_t
{
    uint64_t          uuid;
    struct smb_info_t info; //文件的 创建/上传/下载/关闭/查询 关联在一起, 此处仅仅用于上下行的传递.
    union {
        struct { //CMD = Create
            char        filename[1024];
            uint64_t    AllocationSize;
        } create;
        struct { //CMD = Read
            uint16_t    fileid;
            uint64_t    offset;
            uint64_t    length;
        } read_write;
    };
};

#define SMB_RESULT_UPDATE(a,b,c) if(b->c){a->c = b->c;}
static int smb_info_result_copy(struct smb_info_t *dst, const struct smb_info_t *src)
{
    SMB_RESULT_UPDATE(dst,src,Cmd);
    SMB_RESULT_UPDATE(dst,src,NtStatus);
    SMB_RESULT_UPDATE(dst,src,Flags);
    SMB_RESULT_UPDATE(dst,src,Flags2);
    SMB_RESULT_UPDATE(dst,src,PidHigh);
    SMB_RESULT_UPDATE(dst,src,Signature);
    SMB_RESULT_UPDATE(dst,src,Tid);
    SMB_RESULT_UPDATE(dst,src,Pid);
    SMB_RESULT_UPDATE(dst,src,Uid);
    SMB_RESULT_UPDATE(dst,src,Mid);
    SMB_RESULT_UPDATE(dst,src,AccessMask);
    SMB_RESULT_UPDATE(dst,src,AllocationSize);
    SMB_RESULT_UPDATE(dst,src,AndXCommand);
    SMB_RESULT_UPDATE(dst,src,AndXOffset);
    SMB_RESULT_UPDATE(dst,src,ByteCount);
    SMB_RESULT_UPDATE(dst,src,Change);
    SMB_RESULT_UPDATE(dst,src,Createaction);
    SMB_RESULT_UPDATE(dst,src,Created);
    SMB_RESULT_UPDATE(dst,src,CreateFlags);
    SMB_RESULT_UPDATE(dst,src,CreateOptions);
    SMB_RESULT_UPDATE(dst,src,DataCompactionMode);
    SMB_RESULT_UPDATE(dst,src,DataLengthHigh);
    SMB_RESULT_UPDATE(dst,src,DataLengthLow);
    SMB_RESULT_UPDATE(dst,src,DataOffset);
    SMB_RESULT_UPDATE(dst,src,Disposition);
    SMB_RESULT_UPDATE(dst,src,EndOfFile);
    SMB_RESULT_UPDATE(dst,src,FID);
    SMB_RESULT_UPDATE(dst,src,FileAttributes);
    SMB_RESULT_UPDATE(dst,src,FileNameLen);
    SMB_RESULT_UPDATE(dst,src,FileType);
    SMB_RESULT_UPDATE(dst,src,GuestMaximalAccessRights);
    SMB_RESULT_UPDATE(dst,src,HighOffset);
    SMB_RESULT_UPDATE(dst,src,Impersonation);
    SMB_RESULT_UPDATE(dst,src,IPCState);
    SMB_RESULT_UPDATE(dst,src,IsDirectory);
    SMB_RESULT_UPDATE(dst,src,LastAccess);
    SMB_RESULT_UPDATE(dst,src,LastWrite);
    SMB_RESULT_UPDATE(dst,src,MaxCountHigh);
    SMB_RESULT_UPDATE(dst,src,MaxCountLow);
    SMB_RESULT_UPDATE(dst,src,MaximalAccessRights);
    SMB_RESULT_UPDATE(dst,src,MinCount);
    SMB_RESULT_UPDATE(dst,src,Offset);
    SMB_RESULT_UPDATE(dst,src,Oplocklevel);
    SMB_RESULT_UPDATE(dst,src,Remaining);
    SMB_RESULT_UPDATE(dst,src,RootFID);
    SMB_RESULT_UPDATE(dst,src,SecurityFlags);
    SMB_RESULT_UPDATE(dst,src,ServerUnique);
    SMB_RESULT_UPDATE(dst,src,ShareAccess);
    SMB_RESULT_UPDATE(dst,src,WordCount);
    SMB_RESULT_UPDATE(dst,src,WriteMode);
    return 0;
}

static int smb_hdr_result_copy(struct smb_info_t *info, const struct smb_hdr_t *hdr)
{
    info->Resp      = !!(0x80 & hdr->Flags);
    info->Cmd       = hdr->Cmd;
    info->Mid       = hdr->Mid;
    info->NtStatus  = hdr->NtStatus;
    info->Flags     = hdr->Flags;
    info->Flags2    = hdr->Flags2;
    info->PidHigh   = hdr->PidHigh;
    info->Signature = hdr->Signature;
    info->Tid       = hdr->Tid;
    info->Pid       = hdr->Pid;
    info->Uid       = hdr->Uid;
    info->uuid      = *(const uint64_t*)(&hdr->Tid);
    return 0;
}

static int smb_create_request_result_copy(struct smb_info_t *info, const struct smb_create_request_t *create_request)
{
    info->WordCount      = create_request->WordCount;
    info->AndXCommand    = create_request->AndXCommand;
    info->AndXOffset     = create_request->AndXOffset;
    info->FileNameLen    = create_request->FileNameLen;
    info->CreateFlags    = create_request->CreateFlags;
    info->RootFID        = create_request->RootFID;
    info->AccessMask     = create_request->AccessMask;
    info->AllocationSize = create_request->AllocationSize;
    info->FileAttributes = create_request->FileAttributes;
    info->ShareAccess    = create_request->ShareAccess;
    info->Disposition    = create_request->Disposition;
    info->CreateOptions  = create_request->CreateOptions;
    info->Impersonation  = create_request->Impersonation;
    info->SecurityFlags  = create_request->SecurityFlags;
    info->ByteCount      = create_request->ByteCount;
    return 0;
}

static int smb_create_response_result_copy(struct smb_info_t *info, const struct smb_create_response_t *create_response)
{
    info->WordCount                = create_response->WordCount;
    info->AndXCommand              = create_response->AndXCommand;
    info->AndXOffset               = create_response->AndXOffset;
    info->Oplocklevel              = create_response->Oplocklevel;
    info->FID                      = create_response->FID;
    info->Createaction             = create_response->Createaction;
    info->Created                  = create_response->Created;
    info->LastAccess               = create_response->LastAccess;
    info->LastWrite                = create_response->LastWrite;
    info->Change                   = create_response->Change;
    info->FileAttributes           = create_response->FileAttributes;
    info->AllocationSize           = create_response->AllocationSize;
    info->EndOfFile                = create_response->EndOfFile;
    info->FileType                 = create_response->FileType;
    info->IPCState                 = create_response->IPCState;
    info->IsDirectory              = create_response->IsDirectory;
    info->ServerUnique             = create_response->ServerUnique;
    info->MaximalAccessRights      = create_response->MaximalAccessRights;
    info->GuestMaximalAccessRights = create_response->GuestMaximalAccessRights;
    info->ByteCount                = create_response->ByteCount;
    memcpy(info->VolumeGUID, create_response->VolumeGUID, sizeof(info->VolumeGUID));
    return 0;
}

static int smb_read_request_result_copy(struct smb_info_t *info, const struct smb_read_request_t *read_request)
{
    info->WordCount    = read_request->WordCount;
    info->AndXCommand  = read_request->AndXCommand;
    info->AndXOffset   = read_request->AndXOffset;
    info->FID          = read_request->FID;
    info->Offset       = read_request->Offset;
    info->MaxCountLow  = read_request->MaxCountLow;
    info->MinCount     = read_request->MinCount;
    info->MaxCountHigh = read_request->MaxCountHigh;
    info->Remaining    = read_request->Remaining;
    info->HighOffset   = read_request->HighOffset;
    info->ByteCount    = read_request->ByteCount;
    return 0;
}

static int smb_read_response_result_copy(struct smb_info_t *info, const struct smb_read_response_t *read_response)
{
    info->WordCount          = read_response->WordCount;
    info->AndXCommand        = read_response->AndXCommand;
    info->AndXOffset         = read_response->AndXOffset;
    info->Remaining          = read_response->Remaining;
    info->DataCompactionMode = read_response->DataCompactionMode;
    info->DataLengthLow      = read_response->DataLengthLow;
    info->DataOffset         = read_response->DataOffset;
    info->DataLengthHigh     = read_response->DataLengthHigh;
    info->ByteCount          = read_response->ByteCount;
    return 0;
}

static int smb_write_request_result_copy(struct smb_info_t *info, const struct smb_write_request_t *write_request)
{
    info->WordCount      = write_request->WordCount;
    info->AndXCommand    = write_request->AndXCommand;
    info->AndXOffset     = write_request->AndXOffset;
    info->FID            = write_request->FID;
    info->Offset         = write_request->Offset;
    info->WriteMode      = write_request->WriteMode;
    info->Remaining      = write_request->Remaining;
    info->DataLengthHigh = write_request->DataLengthHigh;
    info->DataLengthLow  = write_request->DataLengthLow;
    info->DataOffset     = write_request->DataOffset;
    info->HighOffset     = write_request->HighOffset;
    info->ByteCount      = write_request->ByteCount;
    return 0;
}

static int smb_close_request_result_copy(struct smb_info_t *info, const struct smb_close_request_t *close_request)
{
    info->WordCount  = close_request->WordCount;
    info->FID        = close_request->FID;
    info->LastWrite  = close_request->LastWrite;
    info->ByteCount  = close_request->ByteCount;
    return 0;
}

//传输方向 状态
struct smb_stream_t {
    int (*do_work)(struct flow_info *flow, int C2S, const uint8_t *p, uint32_t l);
    struct smb_transfer_t transfer;
    struct smb_cache_t    cache;
};

//SMB 双向会话
struct smb_session {
    struct flow_info    *flow;
    GHashTable          *table_msgid; //key:msgid, value:msg上下文
    GHashTable          *table_guid;  //key:guid, value:filename
    GHashTable          *table_file;  //key:filename, value:file
    struct smb_stream_t  stream[2];   //双向传输
    struct smb_info_t    session_info; //保存会话级别的认证信息
};

//输出字段
enum smb_index_em{
    EM_SMB_PROTOCOL,
    EM_SMB_COMMAND,
    EM_SMB_NTSTATUS,
    EM_SMB_FLAGS,
    EM_SMB_FLAGS2,
    EM_SMB_PIDHIGH,
    EM_SMB_SIGNATURE,
    EM_SMB_TID,
    EM_SMB_PIDLOW,
    EM_SMB_UID,
    EM_SMB_MID,
    EM_SMB_WORDCOUNT,
    EM_SMB_BYTECOUNT,
    EM_SMB_DIALECTNUMS,
    EM_SMB_DIALECTS,
    EM_SMB_SERVERDIALECTINDEX,
    EM_SMB_SERVERSECURITYMODE,
    EM_SMB_SERVERMAXMPXCOUNT,
    EM_SMB_SERVERMAXNUMBERVCS,
    EM_SMB_SERVERMAXBUFFERSIZE,
    EM_SMB_SERVERMAXRAWSIZE,
    EM_SMB_SERVERSESSIONKEY,
    EM_SMB_SERVERCAPABILITIES,
    EM_SMB_SERVERSYSTEMTIME,
    EM_SMB_SERVERTIMEZONE,
    EM_SMB_SERVERKEYLENGTH,
    EM_SMB_SERVERGUID,
    EM_SMB_SERVERNEGPROTSECURITYBLOB,
    EM_SMB_SERVERENCRYPTIONKEY,
    EM_SMB_SERVERDOMAINNAME,
    EM_SMB_SERVERNAME,
    EM_SMB_NTLMSSPIDENTIFIER,
    EM_SMB_NTLMMESSAGETYPE,
    EM_SMB_CLIENTSESSSETUPXANDXCOMMAND,
    EM_SMB_CLIENTSESSSETUPXANDXOFFSET,
    EM_SMB_CLIENTMAXBUFFERSIZE,
    EM_SMB_CLIENTMAXMPXCOUNT,
    EM_SMB_CLIENTVCNUMBER,
    EM_SMB_CLIENTSESSIONKEY,
    EM_SMB_CLIENTANSIPASSWORDLEN,
    EM_SMB_CLIENTUNICODEPASSWORDLEN,
    EM_SMB_CLIENTSECURITYBLOBLENGTH,
    EM_SMB_CLIENTCAPABILITIES,
    EM_SMB_CLIENTSB_NEGOTIATEFLAGS,
    EM_SMB_CLIENTSB_CALLINGWORKSTATIONDOMAIN,
    EM_SMB_CLIENTSB_CALLINGWORKSTATIONNAME,
    EM_SMB_CLIENTSB_VERSION,
    EM_SMB_CLIENTSB_LANMANAGERRESPONSE,
    EM_SMB_CLIENTSB_NTLMRESPONSE,
    EM_SMB_CLIENTSB_DOMAINNAME,
    EM_SMB_CLIENTSB_USERNAME,
    EM_SMB_CLIENTSB_HOSTNAME,
    EM_SMB_CLIENTSB_SESSIONKEY,
    EM_SMB_CLIENTANSIPASSWORD,
    EM_SMB_CLIENTUNICODEPASSWORD,
    EM_SMB_CLIENTACCOUNTNAME,
    EM_SMB_CLIENTPRIMARYDOMAIN,
    EM_SMB_CLIENTNATIVEOS,
    EM_SMB_CLIENTNATIVELANMAN,
    EM_SMB_SERVERSESSSETUPXANDXCOMMAND,
    EM_SMB_SERVERSESSSETUPXANDXOFFSET,
    EM_SMB_SERVERACTION,
    EM_SMB_SERVERSECURITYBLOBLENGTH,
    EM_SMB_SERVERSB_TARGETNAME,
    EM_SMB_SERVERSB_NEGOTIATEFLAGS,
    EM_SMB_SERVERSB_NTLMSERVERCHALLENGE,
    EM_SMB_SERVERSB_NETBIOSDOMAINNAME,
    EM_SMB_SERVERSB_DNSDOMAINNAME,
    EM_SMB_SERVERSB_DNSCOMPUTERNAME,
    EM_SMB_SERVERSB_TIMESTAMP,
    EM_SMB_SERVERSB_VERSION,
    EM_SMB_SERVERNATIVEOS,
    EM_SMB_SERVERNATIVELANMAN,
    EM_SMB_SERVERPRIMARYDOMAIN,
    EM_SMB_CLIENTTCONXANDXCOMMAND,
    EM_SMB_CLIENTTCONXANDXOFFSET,
    EM_SMB_CLIENTFLAGS,
    EM_SMB_CLIENTPASSWORDLENGTH,
    EM_SMB_CLIENTPASSWORD,
    EM_SMB_CLIENTPATH,
    EM_SMB_CLIENTSERVICE,
    EM_SMB_SERVERTCONXANDXCOMMAND,
    EM_SMB_SERVERTCONXANDXOFFSET,
    EM_SMB_SERVEROPTIONALSUPPORT,
    EM_SMB_MAXIMALSHAREACCESSRIGHTS,
    EM_SMB_GUESTMAXIMALSHAREACCESSRIGHTS,
    EM_SMB_SERVERSERVICE,
    EM_SMB_SERVERNATIVEFILESYSTEM,
    EM_SMB_SERVERSHOREDIRNAME,
    EM_SMB_LOADWAY,
    EM_SMB_PIPENAME,
    EM_SMB_FILEID,
    EM_SMB_MAILSLOTNAME,
    EM_SMB_FILEATTR,
    EM_SMB_ENDOFFILE,
    EM_SMB_SEARCHATTR,
    EM_SMB_REFERRALVERSION,
    EM_SMB_INFO_LEVEL,
    EM_SMB_DIALECTNAME,
    EM_SMB_SMBEXTATTR,
    EM_SMB_FILENAME,
    EM_SMB_FILELOCAL,
    EM_SMB_FILESIZE,
    EM_SMB_FILE_RECOVERED_STATUS,
    EM_SMB_OFFSET_STOP_AT,
    EM_SMB_TCP_MISS_SEQ_AT,
    EM_SMB_FILE_TYPE,
    EM_SMB_FILELOCALNAME,
    EM_SMB_AUTH_TYPE,
    EM_SMB_AUTH_DIRECTORY,
    EM_SMB_MAX,
};

static dpi_field_table  smb_field_array[] = {
    DPI_FIELD_D(EM_SMB_PROTOCOL,                         EM_F_TYPE_STRING, "Protocol"),
    DPI_FIELD_D(EM_SMB_COMMAND,                          EM_F_TYPE_STRING, "Command"),
    DPI_FIELD_D(EM_SMB_NTSTATUS,                         EM_F_TYPE_STRING, "Ntstatus"),
    DPI_FIELD_D(EM_SMB_FLAGS,                            EM_F_TYPE_STRING, "Flags"),
    DPI_FIELD_D(EM_SMB_FLAGS2,                           EM_F_TYPE_STRING, "Flags2"),
    DPI_FIELD_D(EM_SMB_PIDHIGH,                          EM_F_TYPE_UINT16, "Pidhigh"),
    DPI_FIELD_D(EM_SMB_SIGNATURE,                        EM_F_TYPE_STRING, "Signature"),
    DPI_FIELD_D(EM_SMB_TID,                              EM_F_TYPE_UINT16, "Tid"),
    DPI_FIELD_D(EM_SMB_PIDLOW,                           EM_F_TYPE_UINT16, "Pidlow"),
    DPI_FIELD_D(EM_SMB_UID,                              EM_F_TYPE_UINT16, "Uid"),
    DPI_FIELD_D(EM_SMB_MID,                              EM_F_TYPE_UINT16, "Mid"),
    DPI_FIELD_D(EM_SMB_WORDCOUNT,                        EM_F_TYPE_UINT8 , "Wordcount"),
    DPI_FIELD_D(EM_SMB_BYTECOUNT,                        EM_F_TYPE_UINT16, "Bytecount"),
    DPI_FIELD_D(EM_SMB_DIALECTNUMS,                      EM_F_TYPE_UINT8 , "Dialectnums"),
    DPI_FIELD_D(EM_SMB_DIALECTS,                         EM_F_TYPE_STRING, "Dialects"),
    DPI_FIELD_D(EM_SMB_SERVERDIALECTINDEX,               EM_F_TYPE_UINT16, "Serverdialectindex"),
    DPI_FIELD_D(EM_SMB_SERVERSECURITYMODE,               EM_F_TYPE_STRING, "Serversecuritymode"),
    DPI_FIELD_D(EM_SMB_SERVERMAXMPXCOUNT,                EM_F_TYPE_UINT16, "Servermaxmpxcount"),
    DPI_FIELD_D(EM_SMB_SERVERMAXNUMBERVCS,               EM_F_TYPE_UINT16, "Servermaxnumbervcs"),
    DPI_FIELD_D(EM_SMB_SERVERMAXBUFFERSIZE,              EM_F_TYPE_UINT32, "Servermaxbuffersize"),
    DPI_FIELD_D(EM_SMB_SERVERMAXRAWSIZE,                 EM_F_TYPE_UINT32, "Servermaxrawsize"),
    DPI_FIELD_D(EM_SMB_SERVERSESSIONKEY,                 EM_F_TYPE_STRING, "Serversessionkey"),
    DPI_FIELD_D(EM_SMB_SERVERCAPABILITIES,               EM_F_TYPE_STRING, "Servercapabilities"),
    DPI_FIELD_D(EM_SMB_SERVERSYSTEMTIME,                 EM_F_TYPE_STRING, "Serversystemtime"),
    DPI_FIELD_D(EM_SMB_SERVERTIMEZONE,                   EM_F_TYPE_STRING, "Servertimezone"),
    DPI_FIELD_D(EM_SMB_SERVERKEYLENGTH,                  EM_F_TYPE_STRING, "Serverkeylength"),
    DPI_FIELD_D(EM_SMB_SERVERGUID,                       EM_F_TYPE_STRING, "Serverguid"),
    DPI_FIELD_D(EM_SMB_SERVERNEGPROTSECURITYBLOB,        EM_F_TYPE_STRING, "Servernegprotsecurityblob"),
    DPI_FIELD_D(EM_SMB_SERVERENCRYPTIONKEY,              EM_F_TYPE_STRING, "Serverencryptionkey"),
    DPI_FIELD_D(EM_SMB_SERVERDOMAINNAME,                 EM_F_TYPE_STRING, "Serverdomainname"),
    DPI_FIELD_D(EM_SMB_SERVERNAME,                       EM_F_TYPE_STRING, "Servername"),
    DPI_FIELD_D(EM_SMB_NTLMSSPIDENTIFIER,                EM_F_TYPE_STRING, "Ntlmsspidentifier"),
    DPI_FIELD_D(EM_SMB_NTLMMESSAGETYPE,                  EM_F_TYPE_STRING, "Ntlmmessagetype"),
    DPI_FIELD_D(EM_SMB_CLIENTSESSSETUPXANDXCOMMAND,      EM_F_TYPE_STRING, "Clientsesssetupxandxcommand"),
    DPI_FIELD_D(EM_SMB_CLIENTSESSSETUPXANDXOFFSET,       EM_F_TYPE_STRING, "Clientsesssetupxandxoffset"),
    DPI_FIELD_D(EM_SMB_CLIENTMAXBUFFERSIZE,              EM_F_TYPE_UINT16, "Clientmaxbuffersize"),
    DPI_FIELD_D(EM_SMB_CLIENTMAXMPXCOUNT,                EM_F_TYPE_UINT16, "Clientmaxmpxcount"),
    DPI_FIELD_D(EM_SMB_CLIENTVCNUMBER,                   EM_F_TYPE_UINT16, "Clientvcnumber"),
    DPI_FIELD_D(EM_SMB_CLIENTSESSIONKEY,                 EM_F_TYPE_STRING, "Clientsessionkey"),
    DPI_FIELD_D(EM_SMB_CLIENTANSIPASSWORDLEN,            EM_F_TYPE_UINT16, "Clientansipasswordlen"),
    DPI_FIELD_D(EM_SMB_CLIENTUNICODEPASSWORDLEN,         EM_F_TYPE_UINT32, "Clientunicodepasswordlen"),
    DPI_FIELD_D(EM_SMB_CLIENTSECURITYBLOBLENGTH,         EM_F_TYPE_UINT16, "Clientsecuritybloblength"),
    DPI_FIELD_D(EM_SMB_CLIENTCAPABILITIES,               EM_F_TYPE_STRING, "Clientcapabilities"),
    DPI_FIELD_D(EM_SMB_CLIENTSB_NEGOTIATEFLAGS,          EM_F_TYPE_STRING, "Clientsb_negotiateflags"),
    DPI_FIELD_D(EM_SMB_CLIENTSB_CALLINGWORKSTATIONDOMAIN,EM_F_TYPE_STRING, "Clientsb_callingworkstationdomain"),
    DPI_FIELD_D(EM_SMB_CLIENTSB_CALLINGWORKSTATIONNAME,  EM_F_TYPE_STRING, "Clientsb_callingworkstationname"),
    DPI_FIELD_D(EM_SMB_CLIENTSB_VERSION,                 EM_F_TYPE_STRING, "Clientsb_version"),
    DPI_FIELD_D(EM_SMB_CLIENTSB_LANMANAGERRESPONSE,      EM_F_TYPE_STRING, "Clientsb_lanmanagerresponse"),
    DPI_FIELD_D(EM_SMB_CLIENTSB_NTLMRESPONSE,            EM_F_TYPE_STRING, "Clientsb_ntlmresponse"),
    DPI_FIELD_D(EM_SMB_CLIENTSB_DOMAINNAME,              EM_F_TYPE_STRING, "Clientsb_domainname"),
    DPI_FIELD_D(EM_SMB_CLIENTSB_USERNAME,                EM_F_TYPE_STRING, "Clientsb_username"),
    DPI_FIELD_D(EM_SMB_CLIENTSB_HOSTNAME,                EM_F_TYPE_STRING, "Clientsb_hostname"),
    DPI_FIELD_D(EM_SMB_CLIENTSB_SESSIONKEY,              EM_F_TYPE_STRING, "Clientsb_sessionkey"),
    DPI_FIELD_D(EM_SMB_CLIENTANSIPASSWORD,               EM_F_TYPE_STRING, "Clientansipassword"),
    DPI_FIELD_D(EM_SMB_CLIENTUNICODEPASSWORD,            EM_F_TYPE_STRING, "Clientunicodepassword"),
    DPI_FIELD_D(EM_SMB_CLIENTACCOUNTNAME,                EM_F_TYPE_STRING, "Clientaccountname"),
    DPI_FIELD_D(EM_SMB_CLIENTPRIMARYDOMAIN,              EM_F_TYPE_STRING, "Clientprimarydomain"),
    DPI_FIELD_D(EM_SMB_CLIENTNATIVEOS,                   EM_F_TYPE_STRING, "Clientnativeos"),
    DPI_FIELD_D(EM_SMB_CLIENTNATIVELANMAN,               EM_F_TYPE_STRING, "Clientnativelanman"),
    DPI_FIELD_D(EM_SMB_SERVERSESSSETUPXANDXCOMMAND,      EM_F_TYPE_STRING, "Serversesssetupxandxcommand"),
    DPI_FIELD_D(EM_SMB_SERVERSESSSETUPXANDXOFFSET,       EM_F_TYPE_UINT16, "Serversesssetupxandxoffset"),
    DPI_FIELD_D(EM_SMB_SERVERACTION,                     EM_F_TYPE_UINT16, "Serveraction"),
    DPI_FIELD_D(EM_SMB_SERVERSECURITYBLOBLENGTH,         EM_F_TYPE_STRING, "Serversecuritybloblength"),
    DPI_FIELD_D(EM_SMB_SERVERSB_TARGETNAME,              EM_F_TYPE_STRING, "Serversb_targetname"),
    DPI_FIELD_D(EM_SMB_SERVERSB_NEGOTIATEFLAGS,          EM_F_TYPE_STRING, "Serversb_negotiateflags"),
    DPI_FIELD_D(EM_SMB_SERVERSB_NTLMSERVERCHALLENGE,     EM_F_TYPE_STRING, "Serversb_ntlmserverchallenge"),
    DPI_FIELD_D(EM_SMB_SERVERSB_NETBIOSDOMAINNAME,       EM_F_TYPE_STRING, "Serversb_netbiosdomainname"),
    DPI_FIELD_D(EM_SMB_SERVERSB_DNSDOMAINNAME,           EM_F_TYPE_STRING, "Serversb_dnsdomainname"),
    DPI_FIELD_D(EM_SMB_SERVERSB_DNSCOMPUTERNAME,         EM_F_TYPE_STRING, "Serversb_dnscomputername"),
    DPI_FIELD_D(EM_SMB_SERVERSB_TIMESTAMP,               EM_F_TYPE_STRING, "Serversb_timestamp"),
    DPI_FIELD_D(EM_SMB_SERVERSB_VERSION,                 EM_F_TYPE_STRING, "Serversb_version"),
    DPI_FIELD_D(EM_SMB_SERVERNATIVEOS,                   EM_F_TYPE_STRING, "Servernativeos"),
    DPI_FIELD_D(EM_SMB_SERVERNATIVELANMAN,               EM_F_TYPE_STRING, "Servernativelanman"),
    DPI_FIELD_D(EM_SMB_SERVERPRIMARYDOMAIN,              EM_F_TYPE_STRING, "Serverprimarydomain"),
    DPI_FIELD_D(EM_SMB_CLIENTTCONXANDXCOMMAND,           EM_F_TYPE_STRING, "Clienttconxandxcommand"),
    DPI_FIELD_D(EM_SMB_CLIENTTCONXANDXOFFSET,            EM_F_TYPE_UINT16, "Clienttconxandxoffset"),
    DPI_FIELD_D(EM_SMB_CLIENTFLAGS,                      EM_F_TYPE_STRING, "Clientflags"),
    DPI_FIELD_D(EM_SMB_CLIENTPASSWORDLENGTH,             EM_F_TYPE_UINT16, "Clientpasswordlength"),
    DPI_FIELD_D(EM_SMB_CLIENTPASSWORD,                   EM_F_TYPE_STRING, "Clientpassword"),
    DPI_FIELD_D(EM_SMB_CLIENTPATH,                       EM_F_TYPE_STRING, "Clientpath"),
    DPI_FIELD_D(EM_SMB_CLIENTSERVICE,                    EM_F_TYPE_STRING, "Clientservice"),
    DPI_FIELD_D(EM_SMB_SERVERTCONXANDXCOMMAND,           EM_F_TYPE_STRING, "Servertconxandxcommand"),
    DPI_FIELD_D(EM_SMB_SERVERTCONXANDXOFFSET,            EM_F_TYPE_UINT16, "Servertconxandxoffset"),
    DPI_FIELD_D(EM_SMB_SERVEROPTIONALSUPPORT,            EM_F_TYPE_STRING, "Serveroptionalsupport"),
    DPI_FIELD_D(EM_SMB_MAXIMALSHAREACCESSRIGHTS,         EM_F_TYPE_STRING, "Maximalshareaccessrights"),
    DPI_FIELD_D(EM_SMB_GUESTMAXIMALSHAREACCESSRIGHTS,    EM_F_TYPE_STRING, "Guestmaximalshareaccessrights"),
    DPI_FIELD_D(EM_SMB_SERVERSERVICE,                    EM_F_TYPE_STRING, "Serverservice"),
    DPI_FIELD_D(EM_SMB_SERVERNATIVEFILESYSTEM,           EM_F_TYPE_STRING, "Servernativefilesystem"),
    DPI_FIELD_D(EM_SMB_SERVERSHOREDIRNAME,               EM_F_TYPE_STRING, "Servershoredirname"),
    DPI_FIELD_D(EM_SMB_LOADWAY,                          EM_F_TYPE_STRING, "Loadway"),
    DPI_FIELD_D(EM_SMB_PIPENAME,                         EM_F_TYPE_STRING, "Pipename"),
    DPI_FIELD_D(EM_SMB_FILEID,                           EM_F_TYPE_UINT16, "Fileid"),
    DPI_FIELD_D(EM_SMB_MAILSLOTNAME,                     EM_F_TYPE_STRING, "Mailslotname"),
    DPI_FIELD_D(EM_SMB_FILEATTR,                         EM_F_TYPE_UINT16, "Fileattr"),
    DPI_FIELD_D(EM_SMB_ENDOFFILE,                        EM_F_TYPE_UINT16, "FileEOF"),
    DPI_FIELD_D(EM_SMB_SEARCHATTR,                       EM_F_TYPE_UINT16, "Searchattr"),
    DPI_FIELD_D(EM_SMB_REFERRALVERSION,                  EM_F_TYPE_UINT16, "Referralversion"),
    DPI_FIELD_D(EM_SMB_INFO_LEVEL,                       EM_F_TYPE_UINT16, "Infolevel"),
    DPI_FIELD_D(EM_SMB_DIALECTNAME,                      EM_F_TYPE_STRING, "Dialectname"),
    DPI_FIELD_D(EM_SMB_SMBEXTATTR,                       EM_F_TYPE_STRING, "Smbextattr"),
    DPI_FIELD_D(EM_SMB_FILENAME,                         EM_F_TYPE_STRING, "Filename"),
    DPI_FIELD_D(EM_SMB_FILELOCAL,                        EM_F_TYPE_STRING, "localFilepath"),
    DPI_FIELD_D(EM_SMB_FILESIZE,                         EM_F_TYPE_UINT64, "FileSize"),
    DPI_FIELD_D(EM_SMB_FILE_RECOVERED_STATUS,            EM_F_TYPE_STRING, "File_recovered_status"),
    DPI_FIELD_D(EM_SMB_OFFSET_STOP_AT,                   EM_F_TYPE_UINT64, "Offset_stop_at"),
    DPI_FIELD_D(EM_SMB_TCP_MISS_SEQ_AT,                  EM_F_TYPE_UINT64, "Tcp_miss_seq_at"),
    DPI_FIELD_D(EM_SMB_FILE_TYPE,                        EM_F_TYPE_STRING, "filetpye"),
    DPI_FIELD_D(EM_SMB_FILELOCALNAME,                    EM_F_TYPE_STRING, "localFilename"),
    DPI_FIELD_D(EM_SMB_AUTH_TYPE,                        EM_F_TYPE_STRING,  "authType"),
    DPI_FIELD_D(EM_SMB_AUTH_DIRECTORY,                   EM_F_TYPE_STRING,  "auth_directory"),


};

static const char*str_cmd(int cmd)
{
    return smb_dissector[cmd].cmd;
}

static const char*str_nt(uint32_t nt)
{
    const char *p = NULL;
    if(0 == nt)
    {
        p ="STATUS_SUCCESS";
    }
    else
    {
        p =  "";
    }
    return p;
}

//输出解析结果
static void write_smb_log(struct flow_info *flow, struct smb_file_t *file)
{
    int     idx                     = 0;
    int     i                       = 0;
    const char    *p                = NULL;
    char   buff[1024];
    struct tbl_log *log_ptr         = NULL;
    struct smb_session *si          = (struct smb_session *)flow->app_session;
    struct smb_stream_t *stream     = si->stream + file->C2S;
    struct smb_transfer_t *transfer = &stream->transfer;
    struct smb_info_t *info         = &file->info;

    int C2S = file->C2S;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return;
    }
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, flow->direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
    idx = 0;
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "smb");
    for(i=0;i<EM_SMB_MAX;i++){
        switch(smb_field_array[i].index){
            case EM_SMB_PROTOCOL:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, (const uint8_t*)"SMB", 4);
                break;

            case EM_SMB_COMMAND:
                p = str_cmd(info->Cmd);
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, (const uint8_t*)p, strlen(p));
                break;

            case EM_SMB_NTSTATUS:
                p = str_nt(info->NtStatus);
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, (const uint8_t*)p, strlen(p));
                break;

            case EM_SMB_FLAGS:
                memset(buff, 0, sizeof(buff));
                bintohex((const unsigned char*)&info->Flags, sizeof(info->Flags), (char*)buff, sizeof(buff));
                p = buff;
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, (const uint8_t*)p, strlen(p));
                break;

            case EM_SMB_FLAGS2:
                memset(buff, 0, sizeof(buff));
                bintohex((const unsigned char*)&info->Flags2, sizeof(info->Flags2), (char*)buff, sizeof(buff));
                p = buff;
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, (const uint8_t*)p, strlen(p));
                break;

            case EM_SMB_PIDHIGH:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, NULL, info->PidHigh);
                break;

            case EM_SMB_SIGNATURE:
                memset(buff, 0, sizeof(buff));
                bintohex((const unsigned char*)&info->Signature, sizeof(info->Signature), (char*)buff, sizeof(buff));
                p = buff;
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, (const uint8_t*)p, strlen(p));
                break;

            case EM_SMB_TID:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, NULL, info->Tid);
                break;

            case EM_SMB_PIDLOW:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, NULL, info->Pid);
                break;

            case EM_SMB_UID:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, NULL, info->Uid);
                break;

            case EM_SMB_MID:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, NULL, info->Mid);
                break;

            case EM_SMB_WORDCOUNT:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, NULL, info->WordCount);
                break;

            case EM_SMB_BYTECOUNT:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, NULL, info->ByteCount);
                break;

            case EM_SMB_SERVERSB_TIMESTAMP:
                snprintf(buff, sizeof(buff), "%zu", time(NULL));
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, (const uint8_t*)p, strlen(p));
                break;
            case EM_SMB_SERVERSB_VERSION:
                p = "NT1";
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, (const uint8_t*)p, strlen(p));
                break;
 
            case EM_SMB_SERVERSHOREDIRNAME: //消息类型暂未完全解析 -- 拼凑出目录
                snprintf(buff, sizeof(buff), "%s", file->name);
                if(my_strrstr(buff, "\\")) *my_strrstr(buff, "\\") = '\0';
                if(my_strrstr(buff, "/"))  *my_strrstr(buff, "/") = '\0';
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, (const uint8_t*)buff, strlen(buff));
                break;

            case EM_SMB_FILEID:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, NULL, info->FID);
                break;
            //case EM_SMB_MAILSLOTNAME:
            //    break;
            case EM_SMB_FILEATTR:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, NULL, info->FileAttributes);
                break;

            case EM_SMB_ENDOFFILE:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, NULL, file_rsm_len(file->file_rsm));
                break;

            case EM_SMB_FILENAME:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, (const uint8_t*)file->name, strlen(file->name));
                break;

            case EM_SMB_FILELOCAL:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, (const uint8_t*)file->local, strlen(file->local));
                break;
            case EM_SMB_FILELOCALNAME:
                  write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, (const uint8_t*)file->local + g_config.tbl_out_dir_len , strlen(file->local) -  g_config.tbl_out_dir_len );
                break;
            case EM_SMB_FILESIZE:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, NULL, file->eof);
                break;

            case EM_SMB_FILE_RECOVERED_STATUS:
                p = file_rsm_len(file->file_rsm) == file->eof ? "完整" : "残缺";
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, (const uint8_t*)p, strlen(p));
                break;

            case EM_SMB_OFFSET_STOP_AT:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, NULL, file_rsm_len(file->file_rsm));
                break;

            case EM_SMB_TCP_MISS_SEQ_AT:
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, NULL, file->tcp_miss_at_seq);
                break;
            case EM_SMB_FILE_TYPE:{
                  char *flietype = strchr(file->name, '.');
                  if (flietype) {
                    write_coupler_log(
                        log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, (const uint8_t*)flietype+1, strlen(flietype)-1);
                  } else {
                    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
                  }
                }
                break;
            case EM_SMB_AUTH_DIRECTORY:
                // 从文件路径中提取目录信息
                snprintf(buff, sizeof(buff), "%s", file->name);
                if(my_strrstr(buff, "\\")) *my_strrstr(buff, "\\") = '\0';
                if(my_strrstr(buff, "/"))  *my_strrstr(buff, "/") = '\0';
                if(strlen(buff) > 0) {
                    write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, (const uint8_t*)buff, strlen(buff));
                } else {
                    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
                }
                break;
            case EM_SMB_CLIENTSB_USERNAME:
                if(strlen(si->session_info.ClientSB_Username) > 0) {
                    write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, (const uint8_t*)si->session_info.ClientSB_Username, strlen(si->session_info.ClientSB_Username));
                } else {
                    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
                }
                break;
            case EM_SMB_CLIENTPASSWORD:
                if(strlen(si->session_info.ClientPassword) > 0) {
                    write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, (const uint8_t*)si->session_info.ClientPassword, strlen(si->session_info.ClientPassword));
                } else {
                    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
                }
                break;
            case EM_SMB_CLIENTPATH:
                if(strlen(si->session_info.ClientPath) > 0) {
                    write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, (const uint8_t*)si->session_info.ClientPath, strlen(si->session_info.ClientPath));
                } else {
                    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
                }
                break;
            case EM_SMB_SERVERNATIVEOS:
                if(strlen(si->session_info.ServerNativeOS) > 0) {
                    write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, (const uint8_t*)si->session_info.ServerNativeOS, strlen(si->session_info.ServerNativeOS));
                } else {
                    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
                }
                break;
            case EM_SMB_SERVERACTION:
                if(si->session_info.ServerAction) {
                    write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, (const uint8_t*)si->session_info.ServerAction, strlen(si->session_info.ServerAction));
                } else {
                    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
                }
                break;
            case EM_SMB_AUTH_TYPE:
                if(si->session_info.authtype) {
                    write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, smb_field_array[i].type, si->session_info.authtype, 7); // "NTLMSSP"
                } else {
                    write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
                }
                break;
            default:
                write_n_empty_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, 1);
            break;
        }
    }

    log_ptr->thread_id = flow->thread_id;
    log_ptr->log_type = TBL_LOG_SMB;
    log_ptr->log_len = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;
    if (write_tbl_log(log_ptr) != 1)
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
}

static void free_value_msg(gpointer data)
{
    free(data);
}

static void free_value_guid(gpointer p)
{
    struct smb_guid_t *guid = (struct smb_guid_t*)p;
    free(p);
}

static int file_type_detect(struct smb_file_t *file)
{
    char newname[1024] = {0};
    snprintf(newname, sizeof(newname), "%s", file->local);
    discern_filetype((const uint8_t*)file->head, sizeof(file->head), newname, sizeof(newname));
    if(strcmp(newname, file->local))
    {
        rename(file->local, newname);
        snprintf(file->local, sizeof(file->local), "%s", newname); //update
    }
    return 0;
}

static int file_set_status(struct smb_file_t *file, const char *status)
{
    char filelocal[1024]={0};
    snprintf(filelocal, sizeof(filelocal), "%s", file->local);

    //计算 文件目录, 文件名
    const char *filedir = filelocal;
    char *filebase= basename(filelocal);
    if(filebase)
    {
        *(filebase-1) = '\0';
    }

    char newname[1024] = {0};
    snprintf(newname, sizeof(newname), "%s/%s_%s", filedir, status, filebase);

    //update
    rename(file->local, newname);
    snprintf(file->local, sizeof(file->local), "%s", newname); //update
    return 0;
}

static int filename_writing_offload(struct smb_file_t *file)
{
    char newname[1024];
    snprintf(newname, sizeof(newname), "%s", file->local);
    char *find = my_strrstr(newname, ".writing");
    {
        *find = '\0';
        rename(file->local, newname);
        snprintf(file->local, sizeof(file->local), "%s", newname);
    }
    return 0;
}

//close_immediately
//文件的超时 时机存在2个 [1: close_request] [2: flow_timeout]
//但是超时的时刻, 文件不一定已经完整,没有完整要暂存.
//比如 MP4文件拖进播放器,播放某进度片段立即关闭.然后又打开播放某其他片段,
//将这所有的片段去重后组合在一起, 判定是否已经完整
//返回1代表完整
static int smb_file_close(struct smb_file_t *file, int close_immediately)
{
    char buff[1024];
    const char*str_err = "";
    uint64_t file_len   = file_rsm_len(file->file_rsm);
    uint64_t file_write = file_rsm_write(file->file_rsm);

    //文件没有存够 的得等到超时
    if(0 == close_immediately && file->eof != file_len)
    {
        return file->eof == file_len;
    }

    fclose(file->fp);
    file->fp = NULL;

    filename_writing_offload(file);

    //探测格式
    if('\0' == file->suffix[0])
    {
        file_type_detect(file);
    }

    if(file->eof != file_len)
    {
        str_err = buff;
        snprintf(buff, sizeof(buff), " 有效值[%zu] 写入量[%zu] TCP_MISS_SEQ[%u]", file_len, file_write, file->tcp_miss_at_seq);
        file_set_status(file, "1001");
    }
    else
    {
        file_set_status(file, "1002");
        ATOMIC_ADD_FETCH(&SMB_Good_index);
    }
    ATOMIC_ADD_FETCH(&SMB_File_index);
    char *local = basename(file->local);
    printf("SMB文件[%zu/%zu][%s][%s] EOF[%zu]%s\n", SMB_Good_index, SMB_File_index, file->name, local, file->eof, str_err);
    write_smb_log(file->smb->flow, file);
    return file->eof == file_len;
}

static void free_value_file(gpointer p)
{
    struct smb_file_t *file = (struct smb_file_t*)p;

    if(file->fp)
    {
        smb_file_close(file, 1);
    }

    if(file->file_rsm)
    {
        file_rsm_free(file->file_rsm);
    }
    free(p);
}

static int smb_session_hash_table_init(struct smb_session *si)
{
    if(si->table_file == NULL){
        si->table_file = g_hash_table_new_full(g_str_hash, g_str_equal, NULL, free_value_file);
    }
    if(si->table_file == NULL){
        goto ERROR;
    }

    if(si->table_msgid == NULL){
        si->table_msgid = g_hash_table_new_full(g_int64_hash, g_int64_equal, NULL, free_value_msg);
    }
    if(si->table_msgid == NULL){
        goto ERROR;
    }

    if(si->table_guid == NULL){
        si->table_guid = g_hash_table_new_full(g_int_hash, g_int_equal, NULL, free_value_guid);
    }
    if(si->table_guid == NULL){
        goto ERROR;
    }

    return 0;
ERROR:
    if(si->table_msgid)
    {
        g_hash_table_destroy(si->table_msgid);
        si->table_msgid = NULL;
    }

    if(si->table_guid)
    {
        g_hash_table_destroy(si->table_guid);
        si->table_guid = NULL;
    }
    if(si->table_file)
    {
        g_hash_table_destroy(si->table_file);
        si->table_file = NULL;
    }
    return -1;
}

static int is_file_ignore(const char *filename)
{
    int i = 0;
    const char *file_ignore_list[] = {
        "\\srvsvc",
        "\\MsFteWds",
        "srvsvc",
        "MsFteWds",
        NULL,
    };

    for(i = 0; file_ignore_list[i]; i++)
    {
        if(0 == strcasecmp(filename, file_ignore_list[i]))
        {
            return 1;
        }
    }

    const char *file_ignore_suffix[] = {
        ":", //YV-S2000_email_20230625140544_192.168.108.23_67612_10.eml:OECustomProperty 这是啥?
        ":Zone.Identifier",
        NULL,
    };
    for(i = 0; file_ignore_suffix[i]; i++)
    {
        if(strstr(filename, file_ignore_suffix[i]))
        {
            return 1;
        }
    }
    return 0;
}
static int store_cache(struct flow_info *flow, int C2S, const uint8_t *p, uint32_t l)
{
    struct smb_session *si          = (struct smb_session *)flow->app_session;
    struct smb_stream_t *stream     = si->stream + C2S;
    struct smb_transfer_t *transfer = &stream->transfer;
    struct smb_cache_t *cache       = &stream->cache;

    //Cache 操作
    if(cache->hold + l <= sizeof(cache->mem))
    {
        //装载
        memcpy(cache->mem + cache->hold, p, l);
        cache->hold += l;
        DBG("装载 hold[%u]\n", cache->hold);
    }
    else
    {
        ABORT("装不下了 C2S[%u] hold[%u] len[%u]\n", C2S, cache->hold, l);
    }
    return 0;
}

static FILE *smb_open_file(struct smb_file_t *file)
{
    if(file->fp)
    {
        return file->fp;
    }

    struct timeval tv;
    gettimeofday(&tv, NULL);
    srand(tv.tv_sec*1000000 + tv.tv_usec);

    char localname[COMMON_FILE_PATH];

    char *file_suffix = NULL;
    char *file_suffix1 = NULL;
    char *file_suffix2 = NULL;

    //像这样的文件路径,没有后缀,但是存在 "."
    //-rw-r--r--. 1 <USER> <GROUP> 41K Aug  2  2017 /usr/include/c++/4.8.2/tr2/dynamic_bitset
    //-rw-r--r--. 1 <USER> <GROUP> 1.4K Aug 2  2017 \usr\include\c++\4.8.2\parallel\algorithm
    //如果文件名中存在 "/" 或 "\", 那就继续搜索后缀
    file_suffix1 = (char*)g_strrstr_len(file->name, strlen(file->name), "/");
    file_suffix2 = (char*)g_strrstr_len(file->name, strlen(file->name), "\\");
    file_suffix = file_suffix1 ? file_suffix1 : file_suffix;
    file_suffix = file_suffix2 ? file_suffix2 : file_suffix;
    if(file_suffix)
    {
        file_suffix++;
        file_suffix = (char*)g_strrstr_len(file_suffix, strlen(file_suffix), ".");
    }
    const char *suffix = file_suffix ? file_suffix : "";
    // snprintf(localname, COMMON_FILE_PATH, "%s/smb_%zu_%s%s.writing", dir, random(), g_config.task_id, suffix);
    // snprintf(file->local, sizeof(file->local), "%s", localname);
    get_special_filename(NULL, "smb", suffix+1, file->local, sizeof(file->local), 1);
    strcat(file->local,".writing");
    snprintf(file->suffix, sizeof(file->suffix), "%s", suffix);

    FILE *f = fopen(file->local, "wb");
    if(NULL == f)
    {
        perror(file->local);
        return NULL;
    }
    file->fp = f;

    file->file_rsm = file_rsm_init(32); //SMB的offset长度是4字节

    return f;
}

static int write_pdu_to_file(struct flow_info *flow, int C2S, const uint8_t *p, uint32_t l)
{
    DBG("pdu_to_file C2S[%u] len[%u]\n", C2S, l);
    uint32_t data_len = l;
    const uint8_t *data = p;
    struct smb_session *si = (struct smb_session *)flow->app_session;
    struct smb_stream_t *stream     = si->stream + C2S;
    struct smb_transfer_t *transfer = &stream->transfer;
    struct smb_file_t *file         = transfer->file;
    uint32_t remaining = 0;

    file->fp = smb_open_file(file);

    //处理末端
    if(transfer->msg_length_cnt + data_len > transfer->msg_length){
        remaining = transfer->msg_length_cnt + data_len - transfer->msg_length;
        data_len = transfer->msg_length - transfer->msg_length_cnt;
        DBG("更新长度 %u\n", data_len);
    }

    //记下文件头
    if(0 == transfer->msg_offset)
    {
        int min = sizeof(file->head) < data_len ? sizeof(file->head) : data_len;
        memcpy(file->head, data, min);
    }

    //跳转到指定偏移位置写数据
    fseek(file->fp, transfer->msg_offset, SEEK_SET);
    int rc = fwrite(data, 1, data_len, file->fp);
    if(rc < !data_len)
    {
        perror(file->local);
        ABORT("写入错误\n");
    }
    fflush(file->fp);
    file_rsm_push(file->file_rsm, transfer->msg_offset, data_len);//记录文件增长信息
    transfer->msg_offset        += data_len;
    transfer->msg_length_cnt    += data_len;
    DBG("write_pdu_to_file[%s] eof[%zu] data_len[%u] seek[%zu]\n", file->name, file->eof, data_len, transfer->msg_offset);

    //恢复
    if(transfer->msg_length_cnt >= transfer->msg_length)
    {
        transfer->msg_length_cnt = 0;
        stream->do_work = dissect_smb_msg;
        DBG("结束转储 C2S[%u] len[%u]\n", C2S, data_len);
    }

    //残留物
    if(remaining)
    {
        //将末端剩余数据装载到缓存
        store_cache(flow, C2S, data + data_len, remaining);
    }

    return data_len;
}

static uint32_t offset_to_new_netbios_by_smb(const uint8_t *p, int32_t len)
{
    if(len < 4 || len < 0)
    {
        return 0;
    }

    void *find = memmem(p, len, "\xFF\x53\x4d\x42", 4);
    if(find)
    {
        //假设SMB1 总是 (NetBios+SMB)+(NetBios+SMB)+...这样成对的出现
        return (size_t)find - (size_t)p -4; //跳到  NetBios 域
    }
    return len;
}

static int dissect_null(struct flow_info *flow, struct dpi_pkt_st *pkt, int C2S, uint32_t offset, struct smb_info_t *info)
{
    UNUSED(flow);
    UNUSED(info);
    DBG("ignore By dissect_null\n");
    return offset;
}

// 简单的字符串解析辅助函数
static int get_string_from_packet(struct dpi_pkt_st *pkt, uint32_t offset, uint32_t max_len, char *dest, uint32_t dest_size)
{
    if (offset >= pkt->payload_len || max_len == 0) {
        return -1;
    }

    uint32_t copy_len = (max_len < dest_size - 1) ? max_len : dest_size - 1;
    if (offset + copy_len > pkt->payload_len) {
        copy_len = pkt->payload_len - offset;
    }

    memcpy(dest, pkt->payload + offset, copy_len);
    dest[copy_len] = '\0';
    return copy_len;
}

// 解析Session Setup AndX请求
static int dissect_smb_session_setup_request(struct flow_info *flow, struct dpi_pkt_st *pkt, int C2S, uint32_t offset, struct smb_info_t *info)
{
    struct smb_session *si = (struct smb_session *)flow->app_session;
    UNUSED(C2S);

    // 跳过AndX头部
    if (offset + 13 > pkt->payload_len) {
        return offset;
    }

    uint8_t word_count = get_uint8_t(pkt->payload, offset);
    offset += 1;

    if (word_count < 10) {
        return offset;
    }

    // 跳过AndX命令和偏移
    offset += 4;

    // 获取密码长度
    uint16_t ansi_pwd_len = get_uint16_t(pkt->payload, offset);
    offset += 2;
    uint16_t unicode_pwd_len = get_uint16_t(pkt->payload, offset);
    offset += 2;

    // 跳过其他字段到ByteCount
    offset += 8;

    uint16_t byte_count = get_uint16_t(pkt->payload, offset);
    offset += 2;

    // 解析密码（使用ANSI密码）并保存到会话中
    if (ansi_pwd_len > 0 && ansi_pwd_len < sizeof(si->session_info.ClientPassword)) {
        get_string_from_packet(pkt, offset, ansi_pwd_len, si->session_info.ClientPassword, sizeof(si->session_info.ClientPassword));
    }
    offset += ansi_pwd_len + unicode_pwd_len;

    // 查找NTLMSSP标识符来设置authtype，并尝试解析用户名
    for (uint32_t i = offset; i < pkt->payload_len - 7; i++) {
        if (memcmp(pkt->payload + i, "NTLMSSP", 7) == 0) {
            si->session_info.authtype = pkt->payload + i;

            // 简单的用户名解析 - 在NTLMSSP消息中查找可打印字符串
            for (uint32_t j = i + 7; j < pkt->payload_len - 8; j++) {
                if (pkt->payload[j] >= 0x20 && pkt->payload[j] <= 0x7E) {
                    // 找到可打印字符，检查是否是用户名
                    uint32_t str_len = 0;
                    while (j + str_len < pkt->payload_len &&
                           pkt->payload[j + str_len] >= 0x20 &&
                           pkt->payload[j + str_len] <= 0x7E &&
                           str_len < sizeof(si->session_info.ClientSB_Username) - 1) {
                        str_len++;
                    }
                    if (str_len > 2 && str_len < 32) { // 合理的用户名长度
                        memcpy(si->session_info.ClientSB_Username, pkt->payload + j, str_len);
                        si->session_info.ClientSB_Username[str_len] = '\0';
                        break;
                    }
                }
            }
            break;
        }
    }

    return offset;
}

// 解析Session Setup AndX响应
static int dissect_smb_session_setup_response(struct flow_info *flow, struct dpi_pkt_st *pkt, int C2S, uint32_t offset, struct smb_info_t *info)
{
    struct smb_session *si = (struct smb_session *)flow->app_session;
    UNUSED(C2S);

    if (offset + 7 > pkt->payload_len) {
        return offset;
    }

    uint8_t word_count = get_uint8_t(pkt->payload, offset);
    offset += 1;

    if (word_count < 3) {
        return offset;
    }

    // 跳过AndX命令和偏移
    offset += 4;

    // 获取Action字段并保存到会话中
    uint16_t action = get_uint16_t(pkt->payload, offset);
    if (action == 0x0001) {
        si->session_info.ServerAction = "Client logged in as GUEST ? TRUE";
    } else {
        si->session_info.ServerAction = "Client logged in as GUEST ? FALSE";
    }
    offset += 2;

    // 跳过到ByteCount
    if (word_count >= 4) {
        offset += 2; // Security blob length
    }

    uint16_t byte_count = get_uint16_t(pkt->payload, offset);
    offset += 2;

    // 查找并解析服务器操作系统字符串
    uint32_t start_offset = offset;
    uint32_t end_offset = offset + byte_count;

    // 跳过安全blob，查找字符串
    for (uint32_t i = start_offset; i < end_offset - 1; i++) {
        if (pkt->payload[i] != 0) {
            // 找到非零字符，可能是OS字符串的开始
            uint32_t str_len = 0;
            while (i + str_len < end_offset && pkt->payload[i + str_len] != 0) {
                str_len++;
            }
            if (str_len > 0 && str_len < sizeof(si->session_info.ServerNativeOS)) {
                get_string_from_packet(pkt, i, str_len, si->session_info.ServerNativeOS, sizeof(si->session_info.ServerNativeOS));
                break;
            }
        }
    }

    return offset;
}

// 解析Tree Connect AndX请求
static int dissect_smb_tree_connect_request(struct flow_info *flow, struct dpi_pkt_st *pkt, int C2S, uint32_t offset, struct smb_info_t *info)
{
    struct smb_session *si = (struct smb_session *)flow->app_session;
    UNUSED(C2S);

    if (offset + 7 > pkt->payload_len) {
        return offset;
    }

    uint8_t word_count = get_uint8_t(pkt->payload, offset);
    offset += 1;

    if (word_count < 4) {
        return offset;
    }

    // 跳过AndX命令和偏移
    offset += 4;

    // 获取密码长度
    uint16_t pwd_len = get_uint16_t(pkt->payload, offset);
    offset += 2;

    uint16_t byte_count = get_uint16_t(pkt->payload, offset);
    offset += 2;

    // 跳过密码
    offset += pwd_len;

    // 解析路径并保存到会话中
    uint32_t remaining = byte_count - pwd_len;
    if (remaining > 0 && remaining < sizeof(si->session_info.ClientPath)) {
        get_string_from_packet(pkt, offset, remaining, si->session_info.ClientPath, sizeof(si->session_info.ClientPath));

        // 清理路径字符串，移除末尾的服务类型
        char *service_sep = strstr(si->session_info.ClientPath, "\x00");
        if (service_sep) {
            *service_sep = '\0';
        }
    }

    return offset;
}


static int dissect_smb_create_request(struct flow_info *flow, struct dpi_pkt_st *pkt, int C2S, uint32_t offset, struct smb_info_t *info)
{
    //数据 长度有效判断
    if(pkt->payload_len - offset < sizeof(struct smb_create_request_t))
    {
        DBG("smb_create_request_t 长度错误\n");
        return pkt->payload_len;
    }

	struct smb_session *si = (struct smb_session *)flow->app_session;
    const struct smb_create_request_t *create_request = (const struct smb_create_request_t*)(pkt->payload + offset);

    //更新 offset
    uint32_t offset_to_filename = offset + sizeof(struct smb_create_request_t);
    int msglen = sizeof(struct smb_create_request_t) + create_request->ByteCount;
    offset += msglen;

    //协议有效性
    if(0 != create_request->Reserved1 || 0 != create_request->Reserved2)
    {
        DBG("smb_create_request reserved 消息错误\n");
        return offset;
    }

    //动作有效性
    if(0 == create_request->FileNameLen)
    {
        DBG("FileNameLen 空\n");
        return offset;
    }

    int  len;
    char filename[1024];
    uint16_t filename_len = create_request->FileNameLen;
    memset(filename, 0, sizeof(filename));

    //SMB1 存在方言版本 ASCII/UNICODE 共存
    uint16_t unicode_string = 1 << 15;
    if(unicode_string & info->Flags2)
    {
        offset_to_filename++;
        int ret = get_unicode_or_ascii_string(pkt, &offset_to_filename, 1, &len, 0, 0, &filename_len, filename, (int)(sizeof(filename)));
        if(ret == -1)
        {
            DBG("smb_create_request 没有找到文件名\n");
            return offset;
        }
        DBG("smb_create_request Unicode FileName %s\n", filename);
    }
    else
    {
        if(filename_len > sizeof(filename))
        {
            DBG("SMB FileName ASCII 文件名太长\n");
            return offset;
        }
        memcpy(filename, (pkt->payload + offset_to_filename), filename_len);
        DBG("smb_create_request ASCII FileName %s\n", filename);
    }

    //动作有效性
    if(is_file_ignore(filename))
    {
        DBG("smb_create_request 忽略此文件名\n");
        return offset;
    }

    DBG("create_request MID[%u] [%s]\n", info->Mid, filename);

    //复合
    smb_create_request_result_copy(info, create_request);

    // CMD: Create_request
    //此MSGID 创建文件的 filename 要记录下来, 后面 关联 fileid 要用到
    struct smb_msgid_t *msg = malloc(sizeof(struct smb_msgid_t));
    memset(msg, 0, sizeof(struct smb_msgid_t));
    msg->uuid  = info->uuid;
    msg->info  = *info;
    msg->create.AllocationSize = create_request->AllocationSize;
    snprintf(msg->create.filename, sizeof(msg->create.filename), "%s", filename);
    g_hash_table_insert(si->table_msgid, &msg->uuid, msg);
    return offset;
}

static int dissect_smb_create_response(struct flow_info *flow, struct dpi_pkt_st *pkt, int C2S, uint32_t offset, struct smb_info_t *info)
{
	struct smb_session *si = (struct smb_session *)flow->app_session;
    const struct smb_create_response_t *create_response = (const struct smb_create_response_t*)(pkt->payload + offset);

    //数据 长度有效判断
    if(pkt->payload_len - offset < sizeof(struct smb_create_response_t))
    {
        DBG("smb_create_response_t 长度错误 payload_len=%u offset=%u smb_create_response_t=%zu\n",
                pkt->payload_len, offset, sizeof(struct smb_create_response_t));
        return pkt->payload_len;
    }

    //更新 offset
    int msglen = sizeof(struct smb_create_response_t) + create_response->ByteCount;
    offset += msglen;

    //协议有效性
    if(0 != create_response->Reserved)
    {
        DBG("smb_create_response reserved 消息错误\n");
        return offset;
    }

    if(create_response->IsDirectory)
    {
        DBG("忽略目录\n");
        goto EXIT;
    }

    //复合
    smb_create_response_result_copy(info, create_response);

    // CMD: Create_reponse
    //查询出文件名
    struct smb_msgid_t *msg = g_hash_table_lookup(si->table_msgid, &info->uuid);
    if(NULL == msg)
    {
        DBG("没有查询到此MID[%u]\n", info->Mid);
        return offset;
    }

    //复合
    smb_info_result_copy(info, &msg->info);

    //关联 GUID 与 filename
    uint32_t fileid = create_response->FID;
    struct smb_guid_t *guid = g_hash_table_lookup(si->table_guid, &fileid);
    if(NULL == guid)
    {
        guid = malloc(sizeof(struct smb_guid_t));
        memset(guid, 0, sizeof(struct smb_guid_t));
        guid->fileid = create_response->FID;
        snprintf(guid->filename, sizeof(guid->filename), "%s", msg->create.filename);
        g_hash_table_insert(si->table_guid, &guid->fileid, guid);
    }

    //关联 filename 与 file 结构
    //一个文件, 多个guid共享操作
    struct smb_file_t *file = NULL;
    if(NULL == (file=g_hash_table_lookup(si->table_file, msg->create.filename)))
    {
        file = malloc(sizeof(struct smb_file_t));
        memset(file, 0, sizeof(struct smb_file_t));
        snprintf(file->name, sizeof(file->name), "%s", msg->create.filename);
        file->smb        = si;
        smb_info_result_copy(&file->info, info);//复合
        g_hash_table_insert(si->table_file, file->name, file);
    }

    //get eof
    uint32_t eof = 0;
    //文件上传 Create的请求和响应 AllocationSize一致. EndOfFile 为空
    if(create_response->AllocationSize)
    {
        eof = create_response->AllocationSize;
    }

    //如果请求中存在 Allocation Size 弃用上值, 选中新值(文件上传场景)
    if(msg->create.AllocationSize)
    {
        eof = msg->create.AllocationSize;
    }

    //文件下载 以 Create的响应消息 EndOfFile字段 为主
    if(create_response->EndOfFile)
    {
        eof = create_response->EndOfFile;
    }

    //update eof
    if(0 == file->eof)
    {
        file->eof = eof;
    }
    DBG("create_response MID[%u] FID[%u] [%s][%zu]\n", info->Mid, create_response->FID, file->name, file->eof);
EXIT:
    //剩余价值 零
    g_hash_table_remove(si->table_msgid, &info->uuid);//及时清理
    return offset;
}

static int dissect_smb_write_request(struct flow_info *flow, struct dpi_pkt_st *pkt, int C2S, uint32_t offset, struct smb_info_t *info)
{
    struct smb_session *si          = (struct smb_session *)flow->app_session;
    struct smb_stream_t *stream     = si->stream + C2S;
    struct smb_transfer_t *transfer = &stream->transfer ;
    struct smb_cache_t *cache       = &stream->cache;
    const struct smb_write_request_t *write_request = (const struct smb_write_request_t *)(pkt->payload + offset);

    //数据 长度有效判断
    if(pkt->payload_len - offset < sizeof(struct smb_write_request_t))
    {
        DBG("smb_write_request_t 长度错误\n");
        return pkt->payload_len;//全部移走
    }

    //协议有效性
    if(write_request->Reserved1 || UINT32_MAX != write_request->Reserved2)
    {
        DBG("smb_write_request_t reserved 消息错误\n");
        return pkt->payload_len;//全部移走
    }

    //根据 guid 找 filename
    uint32_t  fileid = write_request->FID;
    struct smb_guid_t *guid = g_hash_table_lookup(si->table_guid, &fileid);
    if(NULL == guid)
    {
        DBG("没有找到文件[%u]\n", fileid);
        return pkt->payload_len;//全部移走
    }

    //根据 filename 找 file
    struct smb_file_t *file = g_hash_table_lookup(si->table_file, guid->filename);
    if(NULL == file)
    {
        DBG("没有找到文件[%s]\n", guid->filename);
        return pkt->payload_len;//全部移走
    }

    smb_write_request_result_copy(info, write_request);//复合
    smb_info_result_copy(&file->info, info);//复合
    transfer->msg_offset = write_request->Offset;
    transfer->msg_length = write_request->DataLengthLow;
    transfer->file       = file;//关联当前的file
    file->C2S            = C2S;

    //更新 offset
    offset = offset - sizeof(struct smb_hdr_t) + write_request->DataOffset;;

    DBG("write_request MID[%u] [%s]offset[%zu] len[%u]\n", fileid, guid->filename, transfer->msg_offset, transfer->msg_length);
    if(pkt->payload_len - offset > 0)
    {
        const uint8_t  *p= pkt->payload     + offset;
        uint32_t        l= pkt->payload_len - offset;
        file->C2S        = C2S;
        DBG("开始转储 C2S[%u] len[%u]\n", C2S, l);
        stream->do_work  = write_pdu_to_file;
        write_pdu_to_file(flow, C2S, p, l);
    }
    return pkt->payload_len;
}

static int dissect_smb_read_request(struct flow_info *flow, struct dpi_pkt_st *pkt, int C2S, uint32_t offset, struct smb_info_t *info)
{
    struct smb_session *si = (struct smb_session *)flow->app_session;
    const struct smb_read_request_t *read_request = (const struct smb_read_request_t *)(pkt->payload + offset);

    //数据 长度有效判断
    if(pkt->payload_len - offset < sizeof(struct smb_read_request_t))
    {
        DBG("smb_read_request_t 长度错误\n");
        return pkt->payload_len;
    }

    //更新 offset
    int msglen = sizeof(struct smb_read_request_t);
    offset += msglen;

    //协议有效性
    if( 0 != read_request->Reserved)
    {
        DBG("dissect_smb_read_request reserved 消息错误\n");
        return offset;
    }

    smb_read_request_result_copy(info, read_request);//复合
    DBG("read_request MID[%u] FID[%u][%u][%u]\n", info->Mid, read_request->FID, read_request->Offset, read_request->MaxCountLow);
    //CMD: Read Request
    //此MSGID 客户端请求文件的 offset,length,guid 要记录下来
    struct smb_msgid_t *msg = malloc(sizeof(struct smb_msgid_t));
    memset(msg, 0, sizeof(struct smb_msgid_t));
    smb_info_result_copy(&msg->info, info);//复合
    msg->uuid               = info->uuid;
    msg->read_write.offset  = read_request->Offset;
    msg->read_write.length  = read_request->MaxCountLow;
    msg->read_write.fileid  = read_request->FID;
    g_hash_table_insert(si->table_msgid, &msg->uuid, msg);
    return offset;
}

static int dissect_smb_read_response(struct flow_info *flow, struct dpi_pkt_st *pkt, int C2S, uint32_t offset, struct smb_info_t *info)
{
    struct smb_session *si          = (struct smb_session *)flow->app_session;
    struct smb_stream_t *stream     = si->stream + C2S;
    struct smb_transfer_t *transfer = &stream->transfer ;
    struct smb_cache_t *cache       = &stream->cache;
    const struct smb_read_response_t *read_response = (const struct smb_read_response_t*)(pkt->payload + offset);

    //数据 长度有效判断
    if(pkt->payload_len - offset < sizeof(struct smb_read_response_t))
    {
        DBG("smb_read_response_t 长度错误\n");
        return pkt->payload_len; //全部移走
    }

    //协议有效性
    if(0 != read_response->Reserved1 || 0 != read_response->Reserved2)
    {
        DBG("dissect_smb_read_response reserved 消息错误\n");
        return pkt->payload_len; //全部移走
    }

    if(0 != read_response->Reserved3 || 0 != read_response->Reserved4)
    {
        DBG("dissect_smb_read_response reserved 消息错误\n");
        return pkt->payload_len; //全部移走
    }

    //根据 msgid 找 guid
    struct smb_msgid_t *msg = g_hash_table_lookup(si->table_msgid, &info->uuid);
    if(NULL == msg)
    {
        DBG("没有找到的MiD[%u]\n", info->Mid);
        return pkt->payload_len; //全部移走;
    }

    //根据 guid 找 filename
    uint32_t fileid = msg->read_write.fileid;
    struct smb_guid_t *guid = g_hash_table_lookup(si->table_guid, &fileid);
    if(NULL == guid)
    {
        DBG("没有找到的FID[%u]\n", msg->read_write.fileid);
        return pkt->payload_len; //全部移走;
    }

    //根据 filename 找 file
    struct smb_file_t *file = g_hash_table_lookup(si->table_file, guid->filename);
    if(NULL == file)
    {
        DBG("没有找到的FileName[%s]\n", guid->filename);
        return pkt->payload_len; //全部移走;
    }

    //设置状态
    smb_read_response_result_copy(info, read_response);//复合
    smb_info_result_copy(&file->info, info);//复合
    transfer->file                = file;//关联当前的file
    transfer->msg_offset          = msg->read_write.offset;
    transfer->msg_length          = msg->read_write.length;

    //更新 offset(DataOffset是相对于smb_hdr)
    offset = offset - sizeof(struct smb_hdr_t) + read_response->DataOffset;

    if(pkt->payload_len - offset > 0)
    {
        const uint8_t  *p= pkt->payload     + offset;
        uint32_t        l= pkt->payload_len - offset;
        file->C2S        = C2S;
        DBG("开始转储 C2S[%u] len[%u]\n", C2S, l);
        stream->do_work  = write_pdu_to_file;
        write_pdu_to_file(flow, C2S, p, l);
    }

    //剩余价值 零
    g_hash_table_remove(si->table_msgid, &info->uuid);
    return pkt->payload_len;
}

#define LEVELOFINTEREST_SETEOFINFORMATION 1020
#define SUBCOMMAND_SET_FILE_INFO 0x0008
static int dissect_smb_trans2_request(struct flow_info *flow, struct dpi_pkt_st *pkt, int C2S, uint32_t offset, struct smb_info_t *info)
{
    struct smb_session *si          = (struct smb_session *)flow->app_session;
    struct smb_stream_t *stream     = si->stream + C2S;
    struct smb_transfer_t *transfer = &stream->transfer ;
    struct smb_cache_t *cache       = &stream->cache;
    const struct smb_trans2_request_t *trans2_request = (const struct smb_trans2_request_t *)(pkt->payload + offset);
    const struct smb_set_file_info_t *set_file_info = (const struct smb_set_file_info_t*)(trans2_request+1);
    const struct smb_set_file_info_eof_t *set_file_eof = (const struct smb_set_file_info_eof_t*)(set_file_info+1);

    //数据 长度有效判断
    if(pkt->payload_len - offset < sizeof(struct smb_trans2_request_t))
    {
        DBG("dissect_smb_trans2_request 长度错误\n");
        return pkt->payload_len; //全部移走
    }

    //只关心这个类型
    if(SUBCOMMAND_SET_FILE_INFO != trans2_request->Subcommand)
    {
        // 不关心的协议 -- 再见
        DBG("不关心的 TRANS2类型[%u]\n", trans2_request->Subcommand);
        return  offset; //不能全部偏移掉.(1460: write尾+trans2+write头 导致后面的write无效了)
    }

    //其中的子类型--只关心这个
    if(LEVELOFINTEREST_SETEOFINFORMATION != set_file_info->LevelOfInterest)
    {
        // 不关心的协议 -- 再见
        DBG("不关心的 LevelOfInterest类型[%u]\n", set_file_info->LevelOfInterest);
        return  pkt->payload_len;
    }

    //根据 guid 找 filename
    uint32_t fileid = set_file_info->FID;
    struct smb_guid_t *guid = g_hash_table_lookup(si->table_guid, &fileid);
    if(NULL == guid)
    {
        DBG("没有找到的FID[%u]\n", fileid);
        return pkt->payload_len; //全部移走;
    }

    //根据 filename 找 file
    struct smb_file_t *file = g_hash_table_lookup(si->table_file, guid->filename);
    if(NULL == file)
    {
        DBG("没有找到的FileName[%s]\n", guid->filename);
        return pkt->payload_len; //全部移走;
    }

    //设定EOF(有的文件传输 没有在 Create命令中携带 EOF信息)
    if(0 == file->eof)
    {
        file->eof = set_file_eof->eof;
    }
    return pkt->payload_len;
}

static int dissect_smb_close_request(struct flow_info *flow, struct dpi_pkt_st *pkt, int C2S, uint32_t offset, struct smb_info_t *info)
{
    struct smb_session *si          = (struct smb_session *)flow->app_session;
    struct smb_stream_t *stream     = si->stream + C2S;
    struct smb_transfer_t *transfer = &stream->transfer ;
    struct smb_cache_t *cache       = &stream->cache;
    const struct smb_close_request_t *close_request = (const struct smb_close_request_t*)(pkt->payload + offset);

    //数据 长度有效判断
    if(pkt->payload_len - offset < sizeof(struct smb_close_request_t))
    {
        DBG("smb_close_request_t 长度错误\n");
        return pkt->payload_len; //全部移走
    }

    //更新 offset
    offset += sizeof(struct smb_close_request_t);

    //协议有效性
    if(UINT32_MAX != close_request->LastWrite)
    {
        DBG("smb_create_request reserved 消息错误\n");
        return pkt->payload_len; //全部移走
    }

    //根据 guid 找 filename
    uint32_t fileid = close_request->FID;
    struct smb_guid_t *guid = g_hash_table_lookup(si->table_guid, &fileid);
    if(NULL == guid)
    {
        DBG("没有找到的FID[%u]\n", fileid);
        return pkt->payload_len; //全部移走;
    }

    //根据 filename 找 file
    struct smb_file_t *file = g_hash_table_lookup(si->table_file, guid->filename);
    if(NULL == file)
    {
        DBG("没有找到的FileName[%s]\n", guid->filename);
        return pkt->payload_len; //全部移走;
    }

    //输出file
    int rc = 0;
    if(file->fp) //这个文件可能并没有真正 传输过
    {
        rc = smb_file_close(file, 0);
    }

    if(1 == rc)
    {
        //移除
        g_hash_table_remove(si->table_file, guid->filename);
        g_hash_table_remove(si->table_guid, &fileid);
        transfer->file = NULL;
    }
    return pkt->payload_len; //全部移走;
}

static int init_smb_table_func(void)
{
    int i = 0;
    for(i = 0; i < SMB_MAX_CMD; i++)
    {
        switch(i)
        {
            case SMB_CMD_CREATE:
                smb_dissector[i].cmd               = "SMB_CMD_CREATE";
                smb_dissector[i].func[SMB_CMD_REQ] = dissect_smb_create_request;
                smb_dissector[i].func[SMB_CMD_RES] = dissect_smb_create_response;
                break;

            case SMB_CMD_READ:
                smb_dissector[i].cmd               = "SMB_CMD_READ";
                smb_dissector[i].func[SMB_CMD_REQ] = dissect_smb_read_request;
                smb_dissector[i].func[SMB_CMD_RES] = dissect_smb_read_response;
                break;

            case SMB_CMD_WRITE:
                smb_dissector[i].cmd               = "SMB_CMD_WRITE";
                smb_dissector[i].func[SMB_CMD_REQ] = dissect_smb_write_request;
                smb_dissector[i].func[SMB_CMD_RES] = dissect_null;
                break;

            case SMB_CMD_TRANS2:
                smb_dissector[i].cmd               = "SMB_CMD_TRANS2";
                smb_dissector[i].func[SMB_CMD_REQ] = dissect_smb_trans2_request;
                smb_dissector[i].func[SMB_CMD_RES] = dissect_null;
                break;

            case SMB_CMD_CLOSE:
                smb_dissector[i].cmd               = "SMB_CMD_CLOSE";
                smb_dissector[i].func[SMB_CMD_REQ] = dissect_smb_close_request;
                smb_dissector[i].func[SMB_CMD_RES] = dissect_null;
                break;

            case SMB_CMD_SESSION_SETUP_ANDX:
                smb_dissector[i].cmd               = "SMB_CMD_SESSION_SETUP_ANDX";
                smb_dissector[i].func[SMB_CMD_REQ] = dissect_smb_session_setup_request;
                smb_dissector[i].func[SMB_CMD_RES] = dissect_smb_session_setup_response;
                break;

            case SMB_CMD_TREE_CONNECT_ANDX:
                smb_dissector[i].cmd               = "SMB_CMD_TREE_CONNECT_ANDX";
                smb_dissector[i].func[SMB_CMD_REQ] = dissect_smb_tree_connect_request;
                smb_dissector[i].func[SMB_CMD_RES] = dissect_null;
                break;

            default:
                smb_dissector[i].cmd               = "";
                smb_dissector[i].func[SMB_CMD_REQ] = dissect_null;
                smb_dissector[i].func[SMB_CMD_RES] = dissect_null;
                break;
        }
    }
    return 0;
}

static int dissect_smb_command(struct flow_info *flow, struct dpi_pkt_st *pkt, int C2S, uint32_t offset, struct smb_info_t *info)
{
    return smb_dissector[info->Cmd].func[info->Resp](flow, pkt, C2S, offset, info);
}

static int dissect_netbios_smb(struct flow_info *flow,  struct dpi_pkt_st *pkt, int C2S, uint32_t offset)
{
    DBG("netbios_smb C2S[%u] offset[%u]\n", C2S, offset);
    struct smb_info_t info = {0};
    uint32_t netbios_type = get_uint8_t(pkt->payload, offset);
    uint32_t netbios_len  = get_uint32_ntohl(pkt->payload, offset) & 0x00FFFFFF;

    //Message Type: Session keep-alive (0x85)
    if(0x85 == netbios_type && 0 == netbios_len)
    {
        return offset + 4;
    }

    //更新 offset by netbios
    offset +=4;

    //SMB Head数据有效性
    if(pkt->payload_len - offset < sizeof(struct smb_hdr_t))
    {
        //错误长度
        DBG("ERROR on SMB Head len\n");
        return pkt->payload_len;
    }

    const struct smb_hdr_t *smb_hdr = (const struct smb_hdr_t*)(pkt->payload + offset);
    //数据有效性
    if(ntohl(0xff534d42) != smb_hdr->ServerComponent)
    {
        DBG("ERROR SMB HEAD on ServerComponent\n");
        ATOMIC_ADD_NUM(&SMB_pkt_drop_length, (pkt->payload_len-offset));
        return pkt->payload_len;//遇到了不关心的消息
    }

    //数据有效性
    if(smb_hdr->Reserved)
    {
        DBG("ERROR SMB HEAD on Reserved\n");
    }

    //更新netbios by SMB_Head
    offset += sizeof(struct smb_hdr_t);

    if(0xc0000120 == smb_hdr->NtStatus)
    {
        // NT Status: STATUS_CANCELLED (0xc0000120)
        return offset + 39;
    }
    if(0xc0000034 == smb_hdr->NtStatus)
    {
        // NT Status: STATUS_OBJECT_NAME_NOT_FOUND (0xc0000034)
        return offset + 3;
    }

    smb_hdr_result_copy(&info, smb_hdr);
    DBG("CMD[%i] mid[%u] resp[%u]\n", info.Cmd, info.Mid, info.Resp);

    //驱动表
    offset = dissect_smb_command(flow, pkt, C2S, offset, &info);
    return offset;//GDB 调试专用
}

static int dissect_smb_msg(struct flow_info *flow, int C2S, const uint8_t *payload, uint32_t payload_len)
{
    uint32_t var32 = 0;
    uint32_t offset = 0;
    struct dpi_pkt_st pkt;
    struct smb_session *si          = (struct smb_session *)flow->app_session;
    struct smb_stream_t *stream     = si->stream + C2S;
    struct smb_transfer_t *transfer = &stream->transfer ;
    struct smb_cache_t *cache       = &stream->cache;

    //Cache 操作
    if(cache->hold) //说明上一次有剩余
    {
        if(cache->hold + payload_len <= sizeof(cache->mem))
        {
            //装载
            memcpy(cache->mem + cache->hold, payload, payload_len);
            cache->hold += payload_len;

            //更新
            payload     = cache->mem;
            payload_len = cache->hold;
            DBG("更新 pl[%u]\n", payload_len);
        }
        else
        {
            ABORT("无法承接 C2S[%u] hold[%u] len[%u]\n", C2S, cache->hold, payload_len);
            return 0;
        }
    }

    pkt.payload     = payload;
    pkt.payload_len = payload_len;

    //SMB1 我所遇到过的,
    //  有(NetBios + SMB1)这样的结构
    //也有(NetBios + SMB1) + (NetBios + SMB1) + ... 这样的结构
    //我没遇到过(NetBios + SMB1 + SMB1 + ...) 这样的结构
    while(offset < payload_len)
    {
        offset = dissect_netbios_smb(flow, &pkt, C2S, offset); //递归
        //解决 1460字节的帧(Write的尾巴+Trans2全部+下个Write的头部)
        offset+= offset_to_new_netbios_by_smb(pkt.payload+offset, pkt.payload_len-offset);
    }
    cache->hold = 0; //reset .
    return offset;
}

static int smb_pkt_miss(struct flow_info *flow, uint8_t C2S, uint32_t miss_len)
{
    ATOMIC_ADD_FETCH(&SMB_pkt_miss_rsm_num);
    struct smb_session *si          = (struct smb_session *)flow->app_session;
    struct smb_stream_t *stream     = si->stream + C2S;
    struct smb_transfer_t *transfer = &stream->transfer ;
    struct smb_cache_t *cache       = &stream->cache;
    struct smb_file_t *file         = transfer->file;

    DBG("TCP SMB MISS C2S[%u] len[%u]\n", C2S, miss);
    if(file && 0 == file->tcp_miss_at_seq)
    {
        file->tcp_miss_at_seq = tcp_rsm_status(flow->rsm, C2S)->seq_now;
    }

    //重置
    stream->do_work = dissect_smb_msg;

    return 0;
}

// 新版接口：数据包处理（适配函数）
static int dissect_smb_dequeue(struct flow_info *flow, uint8_t C2S, const uint8_t *p, uint32_t pl)
{
    ATOMIC_ADD_FETCH(&SMB_pkt_after_rsm_num);
    DBG("\nsmb_dequeue C2S[%u] len[%u][%02X:%02X:%02X:%02X:%02X:%02X:%02X:%02X]\n", C2S, pl, p[0],p[1],p[2],p[3],p[4],p[5],p[6],p[7]);
    struct smb_session *si          = (struct smb_session *)flow->app_session;
    struct smb_stream_t *stream     = si->stream + C2S;

    //初始化
    if (si == NULL) {
        si = dpi_malloc(sizeof(struct smb_session));
        memset(si, 0, sizeof(struct smb_session));

        //顺便初始化 HASH 表
        si->flow = flow;
        (si->stream + C2S)->do_work = dissect_smb_msg;
        (si->stream +!C2S)->do_work = dissect_smb_msg;
        int rc = smb_session_hash_table_init(si);
        if(rc < 0)
        {
            DPI_LOG(DPI_LOG_ERROR, "SMB smb_session_hash_table_init FAIL");
            return PKT_OK;
        }
        flow->app_session = si;
    }

    //走.你!
    (si->stream + C2S)->do_work(flow, C2S, p, pl);
    return 0;
}

static void smb_flow_finish(struct flow_info *flow)
{
    struct smb_session *si = (struct smb_session *)flow->app_session;
    if(si)
    {
        if(si->table_msgid != NULL){
            g_hash_table_destroy(si->table_msgid);
            si->table_msgid = NULL;
        }
        if(si->table_guid != NULL){
            g_hash_table_destroy(si->table_guid);
            si->table_guid = NULL;
        }
        if(si->table_file != NULL){
            g_hash_table_destroy(si->table_file);
            si->table_file = NULL;
        }
        free(flow->app_session);
    }

    //printf("SMB_pkt_before_rsm_num %p 累积 %zu\n", flow->app_session, SMB_pkt_before_rsm_num);
    //printf("SMB_pkt_after_rsm_num  %p 累积 %zu\n", flow->app_session, SMB_pkt_after_rsm_num);
    //printf("SMB_pkt_miss_rsm_num   %p 累积 %zu\n", flow->app_session, SMB_pkt_miss_rsm_num);
    //printf("SMB_pkt_drop_length    %p 累积 %zu\n", flow->app_session, SMB_pkt_drop_length);
    flow->app_session = NULL;
    return;
}

// 新版接口：SMB协议识别
static int identify_smb_new(struct flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len)
{
    if (g_config.protocol_switch[PROTOCOL_SMB] == 0)
        return 0;

    uint32_t smb = get_uint32_ntohl(payload, 4);
    if ( 0xff534d42 == smb && 0x72 != *(payload+8)) //过滤掉  SMB Command: Negotiate Protocol (0x72)
    {
        flow->real_protocol_id = PROTOCOL_SMB;
        return PROTOCOL_SMB; // 识别成功
    }
    return 0; // 识别失败
}

// 保留旧版接口用于兼容
static void identify_smb(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if (g_config.protocol_switch[PROTOCOL_SMB] == 0)
        return;

    uint32_t smb = get_uint32_ntohl(payload, 4);
    if ( 0xff534d42 == smb && 0x72 != *(payload+8)) //过滤掉  SMB Command: Negotiate Protocol (0x72)
    {
        flow->real_protocol_id = PROTOCOL_SMB;
    }
    return;
}

// 新版接口：SMB协议初始化
extern struct decode_t decode_smb;

static int init_smb_dissector_new(struct decode_t *decode)
{
    // 注册协议字段
    dpi_register_proto_schema(smb_field_array, EM_SMB_MAX, "smb");

    // 注册端口
    decode_on_port_tcp(139, &decode_smb);
    decode_on_port_tcp(445, &decode_smb);

    // 注册日志表
    register_tbl_array(TBL_LOG_SMB, 0, "smb", NULL);

    // 注册字段映射信息
    map_fields_info_register(smb_field_array, PROTOCOL_SMB, EM_SMB_MAX, "smb");

    // 初始化SMB命令表
    init_smb_table_func();

    return 0;
}

static int smb_destroy(struct decode_t *decode)
{
    return 0;
}

// 新版decode_t结构体定义
struct decode_t decode_smb = {
    .name           = "smb",
#ifdef DPI_SDT_ZDY
    .identify_type  = DPI_IDENTIFY_PORT_CONTENT,
#endif
    .decode_initial = init_smb_dissector_new,
    .pkt_identify   = identify_smb_new,
    .pkt_dissect    = dissect_smb_dequeue,
    .pkt_miss       = smb_pkt_miss,
    .flow_finish    = smb_flow_finish,
    .decode_destroy = smb_destroy,
};
