/****************************************************************************************
 * 文 件 名 : dpi_log.h
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/07/06
编码: wangy            2018/07/06
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#ifndef _DPI_LOG_H_
#define _DPI_LOG_H_

#include <stdio.h>
#include <stdint.h>
#include <errno.h>

#include "dpi_detect.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"

extern const char *protocol_name_array[PROTOCOL_MAX];
extern struct global_config g_config;

typedef enum
{
    DPI_LOG_TRACE,
    DPI_LOG_DEBUG,
    DPI_LOG_INFO,
    DPI_LOG_WARNING,
    DPI_LOG_ERROR,
    DPI_LOG_FATAL,
    DPI_LOG_MAX
}dpi_log_level_t;

static const char *level_strings[] = {
  "TRACE", "DEBUG", "INFO", "WARN", "ERROR", "FATAL"
};

struct file_line_info
{
    const char *debug_print_file;
    const char *debug_print_function;
    uint32_t debug_print_line;
};

enum error_type{
    ERROR_ORDINARY,
    ERROR_SYSCALL
};

void dpi_debug_printf(struct file_line_info *info, enum error_type type, dpi_log_level_t log_level, const char *format, ...);

#define DPI_LOG(level, args...)                             \
  {                                                         \
    if (level >= g_config.log_output_level) {               \
      struct file_line_info info;                           \
      info.debug_print_file     = __FILE__;                 \
      info.debug_print_function = __FUNCTION__;             \
      info.debug_print_line     = __LINE__;                 \
      dpi_debug_printf(&info, ERROR_ORDINARY, level, args); \
    }                                                       \
  }

#define log_trace(fmt, args...) DPI_LOG(DPI_LOG_TRACE, fmt, ##args)
#define log_debug(fmt, args...) DPI_LOG(DPI_LOG_DEBUG, fmt, ##args)
#define log_info(fmt, args...) DPI_LOG(DPI_LOG_INFO, fmt, ##args)
#define log_warn(fmt, args...) DPI_LOG(DPI_LOG_WARNING, fmt, ##args)
#define log_error(fmt, args...) DPI_LOG(DPI_LOG_ERROR, fmt, ##args)
#define log_fatal(fmt, args...) DPI_LOG(DPI_LOG_FATAL, fmt, ##args)

//系统调用错误日志输出接口，在DPI_LOG功能上添加errno对应的错误描述
#define DPI_SYS_LOG(level, args...)                        \
  {                                                        \
    if (level <= g_config.log_output_level) {              \
      struct file_line_info info;                          \
      info.debug_print_file     = __FILE__;                \
      info.debug_print_function = __FUNCTION__;            \
      info.debug_print_line     = __LINE__;                \
      dpi_debug_printf(&info, ERROR_SYSCALL, level, args); \
    }                                                      \
  }

void dpi_log_set_level_str(const char * level_str);
void dpi_log_set_level(int level);


#if DPI_PRINT_DETAILS
#include <sdtapp_interface.h>
void dpi_match_result_log(SdtMatchResult *result, const struct pkt_info *pkt, struct ProtoRecord *pRec);

#else
#define dpi_match_result_log(...)

#endif

#endif
