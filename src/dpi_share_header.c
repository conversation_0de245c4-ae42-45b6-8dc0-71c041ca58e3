/****************************************************************************************
 * 文 件 名 : dpi_share_header.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: liugh              2022/08/12
编码: liugh              2022/08/12
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/


#include <rte_ring.h>
#include <stdio.h>
#include <unistd.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/time.h>
#include <string.h>
#include <arpa/inet.h>
#include <rte_mempool.h>
#include <errno.h>
#include <maxminddb.h>
#include <stdlib.h>
#include <assert.h>
#include <fcntl.h>
#include <string.h>
#include <time.h>
#include <endian.h>
#include <openssl/md5.h>
#include <glib.h>

#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_trailer.h"
#include "ip2region.h"
#include "cJSON.h"
#include "sdt_ip_protocols.h"
#include "dpi_high_app_protos.h"
#include "dpi_utils.h"

GHashTable *mac_oui_hash=NULL;

extern struct int_to_string data_link_layer_proto[];

dpi_field_table common_field_array[] ={
#ifdef DPI_SDT_ZDY
#ifndef DPI_SDT_P327
    DPI_FIELD_D(EM_COMMON_TASKINFO,                  EM_F_TYPE_STRING,                "task_info"),
#endif
    #include "dpi_common_field_trailer.h"
    DPI_FIELD_D(EM_COMMON_RESV8,                     EM_F_TYPE_STRING,                "resv8"),
    DPI_FIELD_D(EM_COMMON_CAPDATE,                   EM_F_TYPE_STRING,               "CapDate"),
    DPI_FIELD_D(EM_COMMON_SRCIP,                     EM_F_TYPE_STRING,               "SrcIp"),
    DPI_FIELD_D(EM_COMMON_SRCCOUNTRY,                EM_F_TYPE_STRING,                "SrcCountry"),
    DPI_FIELD_D(EM_COMMON_SRCAREA,                   EM_F_TYPE_STRING,                "SrcArea"),
    DPI_FIELD_D(EM_COMMON_SRCCITY,                   EM_F_TYPE_STRING,                "SrcCity"),
    DPI_FIELD_D(EM_COMMON_SRCCARRIER,                EM_F_TYPE_STRING,                "SrcCarrier"),
    DPI_FIELD_D(EM_COMMON_DSTIP,                     EM_F_TYPE_STRING,               "DstIp"),
    DPI_FIELD_D(EM_COMMON_DSTCOUNTRY,                EM_F_TYPE_STRING,                "DstCountry"),
    DPI_FIELD_D(EM_COMMON_DSTAREA,                   EM_F_TYPE_STRING,                "DstArea"),
    DPI_FIELD_D(EM_COMMON_DSTCITY,                   EM_F_TYPE_STRING,                "DstCity"),
    DPI_FIELD_D(EM_COMMON_DSTCARRIER,                EM_F_TYPE_STRING,                "DstCarrier"),
    DPI_FIELD_D(EM_COMMON_SRCPORT,                   EM_F_TYPE_UINT16,               "SrcPort"),
    DPI_FIELD_D(EM_COMMON_DSTPORT,                   EM_F_TYPE_UINT16,               "DstPort"),
    DPI_FIELD_D(EM_COMMON_C2S,                       EM_F_TYPE_STRING,               "C2S"),
    DPI_FIELD_D(EM_COMMON_PROTO,                     EM_F_TYPE_UINT8,                "Proto"),
    DPI_FIELD_D(EM_COMMON_TTL,                       EM_F_TYPE_UINT8,                "TTL"),
#elif defined(DPI_SDT_YNAO)
    DPI_FIELD_D(ENUM_SHARE_YNAO_TAGS_SITE,             EM_F_TYPE_STRING,             "tags_site"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_TAGS_UNIT,             EM_F_TYPE_STRING,             "tags_unit"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_TAGS_TASK,             EM_F_TYPE_STRING,             "tags_task"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_TAGS_RULE,             EM_F_TYPE_STRING,             "tags_rule"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_TAGS_ANOMALY,          EM_F_TYPE_STRING,             "tags_anomaly"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_TAGS_THREAT,           EM_F_TYPE_STRING,             "tags_threat"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_TAGS_USER,             EM_F_TYPE_STRING,             "tags_user"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_TAGS_ATTACK,           EM_F_TYPE_STRING,             "tags_attack"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_TIME_CAPTURE,          EM_F_TYPE_STRING,             "time_capture"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_TAGS_ISP,              EM_F_TYPE_STRING,             "tags_isp"),

    DPI_FIELD_D(ENUM_SHARE_YNAO_MOBILE_IMSI,           EM_F_TYPE_STRING,             "mobile_imsi"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_MOBILE_IMEI,           EM_F_TYPE_STRING,             "mobile_imei"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_MOBILE_MSISDN,         EM_F_TYPE_STRING,             "mobile_msisdn"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_MOBILE_INTERFACE,      EM_F_TYPE_STRING,             "mobile_interface"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_MOBILE_SRCIP,          EM_F_TYPE_STRING,             "mobile_srcip"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_MOBILE_SRCPORT,        EM_F_TYPE_STRING,             "mobile_srcport"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_MOBILE_DESTIP,         EM_F_TYPE_STRING,             "mobile_destip"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_MOBILE_DESTPORT,       EM_F_TYPE_STRING,             "mobile_destport"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_MOBILE_APN,            EM_F_TYPE_STRING,             "mobile_apn"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_MOBILE_ECGI,           EM_F_TYPE_STRING,             "mobile_ecgi"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_MOBILE_TAI,            EM_F_TYPE_STRING,             "mobile_tai"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_MOBILE_TEID,           EM_F_TYPE_STRING,             "mobile_teid"),

    DPI_FIELD_D(ENUM_SHARE_YNAO_MOBILE_SRCISP,         EM_F_TYPE_STRING,             "mobile_srcisp"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_MOBILE_DESTISP,        EM_F_TYPE_STRING,             "mobile_destisp"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_MOBILE_LOCAL_PROVINCE, EM_F_TYPE_STRING,             "mobile_local_province"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_MOBILE_LOCAL_CITY,     EM_F_TYPE_STRING,             "mobile_local_city"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_MOBILE_OWNER_PROVINCE, EM_F_TYPE_STRING,             "mobile_owner_province"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_MOBILE_OWNER_CITY,     EM_F_TYPE_STRING,             "mobile_owner_city"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_MOBILE_ROAMING_TYPE,   EM_F_TYPE_STRING,             "mobile_roaming_type"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_MOBILE_RAT,            EM_F_TYPE_STRING,             "mobile_rat"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_MAP_STREAM_ID,         EM_F_TYPE_UINT64,             "map_stream_id"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_IP_PROTO,              EM_F_TYPE_UINT8,                "ip_proto"),
    DPI_FIELD_D(ENUM_SHARE_YNAO_PROTO_INFO,            EM_F_TYPE_STRING,                "proto_info"),
#else
    DPI_FIELD_D(ENUM_SHARE_HEADER_LINENO1,            EM_F_TYPE_UINT32,      "LINENO1"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_LINENO2,            EM_F_TYPE_UINT32,      "LINENO2"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_LINENO3,            EM_F_TYPE_UINT32,      "LINENO3"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_LINENO4,            EM_F_TYPE_UINT32,      "LINENO4"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_LINENAME1,          EM_F_TYPE_STRING,      "lineName1"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_LINENAME2,          EM_F_TYPE_STRING,      "lineName2"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_SYSFROM,            EM_F_TYPE_STRING,      "SSYSFROM"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_DATAFROM,           EM_F_TYPE_STRING,      "DATAFROM"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_SIGTYPE,            EM_F_TYPE_STRING,      "SIGTYPE"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_INFOTYPE,           EM_F_TYPE_STRING,      "infotype"),

    DPI_FIELD_D(ENUM_SHARE_HEADER_BEGTIME,            YA_FT_UINT64,          "begTime"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_ENDTIME,            YA_FT_UINT64,          "endTime"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_COMDUR,             EM_F_TYPE_UINT32,      "comDur"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_MEANID,             EM_F_TYPE_UINT32,      "meanID"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_SITEID,             EM_F_TYPE_UINT32,      "siteID"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_UNITID,             EM_F_TYPE_UINT32,      "unitID"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_TASKID,             EM_F_TYPE_STRING,      "taskID"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_GUID,               EM_F_TYPE_UINT32,      "guid"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_STORTIME,           EM_F_TYPE_STRING,      "stortime"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_MDSECDEG,           EM_F_TYPE_STRING,      "mdsecdeg"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_FILESECDEG,         EM_F_TYPE_STRING,      "filesecdeg"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_SECDEGPRO,          EM_F_TYPE_STRING,      "secdegpro"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_IPVER,              EM_F_TYPE_UINT32,      "ipVer"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_TTL,                EM_F_TYPE_UINT32,      "ttl"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_SRCADDR,            EM_F_TYPE_UINT64,      "srcAddr"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_DSTADDR,            EM_F_TYPE_UINT64,      "dstAddr"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_SRCPORT,            EM_F_TYPE_UINT16,      "srcPort"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_DSTPORT,            EM_F_TYPE_UINT16,      "dstPort"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_PROTNUM,            EM_F_TYPE_UINT32,      "protNum"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_SRCADDRV6,          EM_F_TYPE_STRING,      "srcAddrV6"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_DSTADDRV6,          EM_F_TYPE_STRING,      "dstAddrV6"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_PROTINFO,           EM_F_TYPE_STRING,      "protInfo"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_PROTTYPE,           EM_F_TYPE_STRING,      "protType"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_PROTNAME,           EM_F_TYPE_STRING,      "protName"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_MULROUFLAG,         EM_F_TYPE_STRING,      "mulRouFlag"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_INTFLAG,            EM_F_TYPE_STRING,      "intFlag"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_STRDIREC,           EM_F_TYPE_UINT32,      "strDirec"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_PKTNUM,             EM_F_TYPE_UINT32,      "pktNum"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_PAYLEN,             EM_F_TYPE_UINT32,      "payLen"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_STREAMID,           EM_F_TYPE_STRING,      "streamId"),


    DPI_FIELD_D(ENUM_SHARE_HEADER_ETAGS,              EM_F_TYPE_STRING,      "etags"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_TTAGS,              EM_F_TYPE_STRING,      "ttags"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_ATAGS,              EM_F_TYPE_STRING,      "atags"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_UTAGS,              EM_F_TYPE_STRING,      "utags"),

    DPI_FIELD_D(ENUM_SHARE_HEADER_LABLE1,             EM_F_TYPE_UINT32,      "lable1"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_LABLE2,             EM_F_TYPE_UINT32,      "lable2"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_LABLE3,             EM_F_TYPE_UINT32,      "lable3"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_LABLE4,             EM_F_TYPE_UINT32,      "lable4"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_VLANID1,            EM_F_TYPE_UINT32,      "vlanID1"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_VLANID2,            EM_F_TYPE_UINT32,      "vlanID2"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_SRCMAC,             EM_F_TYPE_STRING,      "srcMac"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_DSTMAC,             EM_F_TYPE_STRING,      "dstMac"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_TUNNELID,           EM_F_TYPE_UINT32,      "tunnelID"),


    DPI_FIELD_D(ENUM_SHARE_HEADER_SRCCOUNTRY,         EM_F_TYPE_STRING,      "srcCountry"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_SRCSTATE,           EM_F_TYPE_STRING,      "srcState"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_SRCCITY,            EM_F_TYPE_STRING,      "SrcCity"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_SRCLONGITUDE,       EM_F_TYPE_STRING,      "srcLongitude"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_SRCLATITUDE,        EM_F_TYPE_STRING,      "srclatitude"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_SRCISP,             EM_F_TYPE_STRING,      "srcISP"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_SRCASN,             EM_F_TYPE_UINT32,      "srcASN"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_DSTCOUNTRY,         EM_F_TYPE_STRING,      "dstCountry"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_DSTSTATE,           EM_F_TYPE_STRING,      "dstState"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_DSTCITY,            EM_F_TYPE_STRING,      "dstCity"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_DSTLONGITUDE,       EM_F_TYPE_STRING,      "dstLongitude"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_DSTLATITUDE,        EM_F_TYPE_STRING,      "dstLatitude"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_DSTISP,             EM_F_TYPE_STRING,      "dstISP"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_DSTASN,             EM_F_TYPE_UINT32,      "dstASN"),

    DPI_FIELD_D(ENUM_SHARE_HEADER_OUTADDRTYPE,        EM_F_TYPE_UINT8,       "outAddrType"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_OUTSRCADDR,         EM_F_TYPE_STRING,      "outSrcAddr"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_OUTDSTADDR,         EM_F_TYPE_STRING,      "outDstAddr"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_OUTER_IPV6_SRC,     EM_F_TYPE_STRING,      "outer.ipv6.src"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_OUTER_IPV6_DST,     EM_F_TYPE_STRING,      "outer.ipv6.dst"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_OUTSRCPORT,         EM_F_TYPE_UINT16,      "outSrcPort"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_OUTDSTPORT,         EM_F_TYPE_UINT16,      "outDstPort"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_OUTTRANSPROTO,      EM_F_TYPE_UINT8,       "outTransProto"),


    DPI_FIELD_D(ENUM_SHARE_HEADER_CAPTURETIME,        EM_F_TYPE_STRING,      "captureTime"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_SRCMACOUI,          EM_F_TYPE_STRING,      "srcMacOui"),
    DPI_FIELD_D(ENUM_SHARE_HEADER_DSTMACOUI,          EM_F_TYPE_STRING,      "dstMacOui"),
#endif
};


//按（ 时间戳（小时级）+ 五元组）  拼接后算Hash
void gen_map_stream_id(struct flow_info *flow)
{

    if (!flow)
        return;
    char __str[256] = {0};
    uint16_t port_src =  flow->tuple.inner.port_src;
    uint16_t port_dst = flow->tuple.inner.port_dst;
    uint8_t  proto = flow->tuple.inner.proto;
    char begin_time[64] = {0};
    timet_to_datetime_local(flow->create_time / 1e6, begin_time, 64);
    begin_time[strlen(begin_time)-6] = 0;//去除小时后的数据
    if (flow->ip_version == 4) {
        uint32_t ip_src =  get_uint32_ntohl(flow->tuple.inner.ip_src,0);
        uint32_t ip_dst = get_uint32_ntohl(flow->tuple.inner.ip_dst,0);
        snprintf(__str, sizeof(__str), "%s%u%u%u%u%u", begin_time, ip_src, ip_dst, port_src, port_dst, proto);
    } else if(flow->ip_version == 6){
        snprintf(__str, sizeof(__str), "%s%u%u%u%u%u%u%u%u%u%u%u%u%u%u%u%u%u%u%u%u%u%u%u%u%u%u%u%u%u%u%u%u%u%u%u", begin_time,
        flow->tuple.inner.ip_src[0],
        flow->tuple.inner.ip_src[1],
        flow->tuple.inner.ip_src[2],
        flow->tuple.inner.ip_src[3],
        flow->tuple.inner.ip_src[4],
        flow->tuple.inner.ip_src[5],
        flow->tuple.inner.ip_src[6],
        flow->tuple.inner.ip_src[7],
        flow->tuple.inner.ip_src[8],
        flow->tuple.inner.ip_src[9],
        flow->tuple.inner.ip_src[10],
        flow->tuple.inner.ip_src[11],
        flow->tuple.inner.ip_src[12],
        flow->tuple.inner.ip_src[13],
        flow->tuple.inner.ip_src[14],
        flow->tuple.inner.ip_src[15],
        flow->tuple.inner.ip_dst[0],
        flow->tuple.inner.ip_dst[1],
        flow->tuple.inner.ip_dst[2],
        flow->tuple.inner.ip_dst[3],
        flow->tuple.inner.ip_dst[4],
        flow->tuple.inner.ip_dst[5],
        flow->tuple.inner.ip_dst[6],
        flow->tuple.inner.ip_dst[7],
        flow->tuple.inner.ip_dst[8],
        flow->tuple.inner.ip_dst[9],
        flow->tuple.inner.ip_dst[10],
        flow->tuple.inner.ip_dst[11],
        flow->tuple.inner.ip_dst[12],
        flow->tuple.inner.ip_dst[13],
        flow->tuple.inner.ip_dst[14],
        flow->tuple.inner.ip_dst[15],
        port_src, port_dst, proto);
    }

    uint64_t md5sum = 0;
    if (strlen(__str) > 0){
        MD5((unsigned char*)__str, strlen(__str), (uint8_t *)&flow->map_stream_id);
    }

}

struct value_type*cb_share_linename1(ProtoRecord *pRec)
{
    memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
    char *buff = pRec->val_temp_buff;

    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    // Data_SRC.Global_LineNO B.1.2.(3) 要求本次自定义转换输出库
    uint32_t HW[4] = {0,0,0,0};
    struct mac_packet_header *psdx               = flow->pSDTMacHeader[0]; //方向?
    if (psdx) {
        sdx_convert_linename(psdx->Datasrc.Global_LineNO, buff, HW);
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_BYTES;
        p->len  = sizeof(pRec->val_temp_buff);
        p->val  = buff;
        return p;
    }
    return NULL;
}
struct value_type*cb_share_begin_time(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->create_time /1000);
    return p;
}
struct value_type*cb_share_end_time(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->end_time/1000);
    return p;
}
struct value_type*cb_share_dru_time(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    int var = flow->end_time - flow->create_time;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(var/1000);
    return p;
}
struct value_type*cb_share_ip_ver(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->ip_version);
    return p;
}
struct value_type*cb_share_src_addr(ProtoRecord *pRec)
{
    //附录B.4.1 某筛选还原设备-元数据提取要求明细（JM）.pdf
    //ip.src 要求是 BIGINT
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    int tuple_direction = get_flow_direction(flow);
    uint32_t ipaddr = 0;
    if (tuple_direction == FLOW_DIR_SRC2DST) {
        if (flow->ip_version == 4) ipaddr = *(uint32_t*)flow->tuple.inner.ip_src;
    } else {
        if (flow->ip_version == 4) ipaddr = *(uint32_t*)flow->tuple_reverse.inner.ip_src;
    }
    if(ipaddr)
    {
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_UINT;
        p->len  = 0;
        p->val  = (void*)(size_t)(ipaddr);
        return p;
    }
    return NULL;
}
struct value_type*cb_share_dst_addr(ProtoRecord *pRec)
{
    //附录B.4.1 某筛选还原设备-元数据提取要求明细（JM）.pdf
    //ip.src 要求是 BIGINT
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    int tuple_direction = get_flow_direction(flow);
    uint32_t ipaddr = 0;
    if (tuple_direction == FLOW_DIR_SRC2DST) {
        if (flow->ip_version == 4) ipaddr = *(uint32_t*)flow->tuple.inner.ip_dst;
    } else {
        if (flow->ip_version == 4) ipaddr = *(uint32_t*)flow->tuple_reverse.inner.ip_dst;
    }
    if(ipaddr)
    {
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_UINT;
        p->len  = 0;
        p->val  = (void*)(size_t)(ipaddr);
        return p;
    }
    return NULL;
}
struct value_type*cb_share_src_port(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    int port = 0;
    int tuple_direction = get_flow_direction(flow);
    if (tuple_direction == FLOW_DIR_SRC2DST) {
        port = ntohs(flow->tuple.inner.port_src);
    } else {
        port = ntohs(flow->tuple_reverse.inner.port_src);
    }
    if(port)
    {
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_UINT;
        p->len  = 0;
        p->val  = (void*)(size_t)(port);
        return p;
    }
    return NULL;
}
struct value_type*cb_share_dst_port(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    int port = 0;
    int tuple_direction = get_flow_direction(flow);
    if (tuple_direction == FLOW_DIR_SRC2DST) {
        port = ntohs(flow->tuple.inner.port_dst);
    } else {
        port = ntohs(flow->tuple_reverse.inner.port_dst);
    }
    if(port)
    {
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_UINT;
        p->len  = 0;
        p->val  = (void*)(size_t)(port);
        return p;
    }
    return NULL;
}
struct value_type*cb_share_porto(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    int porto = flow->tuple.inner.proto;
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(porto);
    return p;
}
struct value_type*cb_share_src_addr_v6(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
    int tuple_direction = get_flow_direction(flow);
    if (tuple_direction == FLOW_DIR_SRC2DST) {
        if (flow->ip_version == 6) get_ip6string(pRec->val_temp_buff, sizeof(pRec->val_temp_buff), flow->tuple.inner.ip_src);
    } else {
        if (flow->ip_version == 6) get_ip6string(pRec->val_temp_buff, sizeof(pRec->val_temp_buff), flow->tuple_reverse.inner.ip_src);
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_BYTES;
    p->len  = strlen(pRec->val_temp_buff);
    p->val  = (void*)(size_t)(pRec->val_temp_buff);
    return p;
}
struct value_type*cb_share_dst_addr_v6(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
    int tuple_direction = get_flow_direction(flow);
    if (tuple_direction == FLOW_DIR_SRC2DST) {
        if (flow->ip_version == 6) get_ip6string(pRec->val_temp_buff, sizeof(pRec->val_temp_buff), flow->tuple.inner.ip_dst);
    } else {
        if (flow->ip_version == 6) get_ip6string(pRec->val_temp_buff, sizeof(pRec->val_temp_buff), flow->tuple_reverse.inner.ip_dst);
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_BYTES;
    p->len  = strlen(pRec->val_temp_buff);
    p->val  = (void*)(size_t)(pRec->val_temp_buff);
    return p;
}
struct value_type*cb_share_proto_info(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
    get_protoinfo(pRec->val_temp_buff, flow->proto_layer, flow->proto_layer_cnt);
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_BYTES;
    p->len  = strlen(pRec->val_temp_buff);
    p->val  = (void*)(size_t)(pRec->val_temp_buff);
    return p;
}
struct value_type*cb_share_proto_type(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_BYTES;
    p->len  = strlen(protocol_name_array[flow->real_protocol_id]);
    p->val  = (void*)(size_t)(protocol_name_array[flow->real_protocol_id]);
    return p;
}
struct value_type*cb_share_proto_name(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    const char *name = dpi_high_proto_find_by_id(flow->high_app_proto_id)->name;
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_BYTES;
    p->len  = strlen(name);
    p->val  = (void*)(size_t)(name);
    return p;
}
struct value_type*cb_share_pkt_num(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->src2dst_packets+flow->dst2src_packets);
    return p;
}
struct value_type*cb_share_pkt_len(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->src2dst_payload_len+flow->dst2src_payload_len);
    return p;
}
struct value_type*cb_share_etags(ProtoRecord *pRec)
{
    return NULL;
}
struct value_type*cb_share_ttags(ProtoRecord *pRec)
{
    return NULL;
}
struct value_type*cb_share_atags(ProtoRecord *pRec)
{
    return NULL;
}
struct value_type*cb_share_utags(ProtoRecord *pRec)
{
    return NULL;
}
struct value_type*cb_share_mpls_lable1(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->data_link_layer.mpls_label[0]);
    return p;
}
struct value_type*cb_share_mpls_lable2(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->data_link_layer.mpls_label[1]);
    return p;
}
struct value_type*cb_share_mpls_lable3(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->data_link_layer.mpls_label[2]);
    return p;
}
struct value_type*cb_share_mpls_lable4(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->data_link_layer.mpls_label[3]);
    return p;
}
struct value_type*cb_share_vlan_id1(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->data_link_layer.vlan_id[0]);
    return p;
}
struct value_type*cb_share_vlan_id2(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->data_link_layer.vlan_id[1]);
    return p;
}
struct value_type*cb_share_src_mac(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
    int direction = get_flow_direction(flow);
    get_macstring(pRec->val_temp_buff, sizeof(pRec->val_temp_buff), flow->session_ethhdr[direction].h_source);
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_BYTES;
    p->len  = strlen(pRec->val_temp_buff);
    p->val  = (void*)(size_t)(pRec->val_temp_buff);
    return p;
}
struct value_type*cb_share_dst_mac(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
    int direction = get_flow_direction(flow);
    get_macstring(pRec->val_temp_buff, sizeof(pRec->val_temp_buff), flow->session_ethhdr[direction].h_dest);
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_BYTES;
    p->len  = strlen(pRec->val_temp_buff);
    p->val  = (void*)(size_t)(pRec->val_temp_buff);
    return p;
}
struct value_type*cb_share_src_ountry(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    if (g_config.ip_position_switch)
    {
        IPINFO srcIp, dstIp;
        memset(&srcIp, 0, sizeof srcIp);
        memset(&dstIp, 0, sizeof dstIp);
        int direction = get_flow_direction(flow);
        dissect_ip_position(flow, direction, &srcIp, &dstIp);
        memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
        strncpy(pRec->val_temp_buff, srcIp.country, sizeof(pRec->val_temp_buff));
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_BYTES;
        p->len  = strlen(pRec->val_temp_buff);
        p->val  = (void*)(size_t)(pRec->val_temp_buff);
        return p;
    }
    return NULL;
}
struct value_type*cb_share_src_state(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    if (g_config.ip_position_switch)
    {
        IPINFO srcIp, dstIp;
        memset(&srcIp, 0, sizeof srcIp);
        memset(&dstIp, 0, sizeof dstIp);
        int direction = get_flow_direction(flow);
        dissect_ip_position(flow, direction, &srcIp, &dstIp);
        memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
        strncpy(pRec->val_temp_buff, srcIp.state, sizeof(pRec->val_temp_buff));
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_BYTES;
        p->len  = strlen(pRec->val_temp_buff);
        p->val  = (void*)(size_t)(pRec->val_temp_buff);
        return p;
    }
    return NULL;
}
struct value_type*cb_share_src_city(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    if (g_config.ip_position_switch)
    {
        IPINFO srcIp, dstIp;
        memset(&srcIp, 0, sizeof srcIp);
        memset(&dstIp, 0, sizeof dstIp);
        int direction = get_flow_direction(flow);
        dissect_ip_position(flow, direction, &srcIp, &dstIp);
        memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
        strncpy(pRec->val_temp_buff, srcIp.city, sizeof(pRec->val_temp_buff));
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_BYTES;
        p->len  = strlen(pRec->val_temp_buff);
        p->val  = (void*)(size_t)(pRec->val_temp_buff);
        return p;
    }
    return NULL;
}
struct value_type*cb_share_src_lon(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    if (g_config.ip_position_switch)
    {
        IPINFO srcIp, dstIp;
        memset(&srcIp, 0, sizeof srcIp);
        memset(&dstIp, 0, sizeof dstIp);
        int direction = get_flow_direction(flow);
        dissect_ip_position(flow, direction, &srcIp, &dstIp);
        memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
        strncpy(pRec->val_temp_buff, srcIp.longitude, sizeof(pRec->val_temp_buff));
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_BYTES;
        p->len  = strlen(pRec->val_temp_buff);
        p->val  = (void*)(size_t)(pRec->val_temp_buff);
        return p;
    }
    return NULL;
}
struct value_type*cb_share_src_lat(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    if (g_config.ip_position_switch)
    {
        IPINFO srcIp, dstIp;
        memset(&srcIp, 0, sizeof srcIp);
        memset(&dstIp, 0, sizeof dstIp);
        int direction = get_flow_direction(flow);
        dissect_ip_position(flow, direction, &srcIp, &dstIp);
        memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
        strncpy(pRec->val_temp_buff, srcIp.latitude, sizeof(pRec->val_temp_buff));
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_BYTES;
        p->len  = strlen(pRec->val_temp_buff);
        p->val  = (void*)(size_t)(pRec->val_temp_buff);
        return p;
    }
    return NULL;
}
struct value_type*cb_share_src_isp(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    if (g_config.ip_position_switch)
    {
        IPINFO srcIp, dstIp;
        memset(&srcIp, 0, sizeof srcIp);
        memset(&dstIp, 0, sizeof dstIp);
        int direction = get_flow_direction(flow);
        dissect_ip_position(flow, direction, &srcIp, &dstIp);
        memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
        strncpy(pRec->val_temp_buff, srcIp.isp, sizeof(pRec->val_temp_buff));
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_BYTES;
        p->len  = strlen(pRec->val_temp_buff);
        p->val  = (void*)(size_t)(pRec->val_temp_buff);
        return p;
    }
    return NULL;
}
struct value_type*cb_share_src_asn(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    if (g_config.ip_position_switch)
    {
        IPINFO srcIp, dstIp;
        memset(&srcIp, 0, sizeof srcIp);
        memset(&dstIp, 0, sizeof dstIp);
        int direction = get_flow_direction(flow);
        dissect_ip_position(flow, direction, &srcIp, &dstIp);
        memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
        strncpy(pRec->val_temp_buff, srcIp.asn, sizeof(pRec->val_temp_buff));
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_BYTES;
        p->len  = strlen(pRec->val_temp_buff);
        p->val  = (void*)(size_t)(pRec->val_temp_buff);
        return p;
    }
    return NULL;
}
struct value_type*cb_share_dst_ountry(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    if (g_config.ip_position_switch)
    {
        IPINFO srcIp, dstIp;
        memset(&srcIp, 0, sizeof srcIp);
        memset(&dstIp, 0, sizeof dstIp);
        int direction = get_flow_direction(flow);
        dissect_ip_position(flow, direction, &srcIp, &dstIp);
        memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
        strncpy(pRec->val_temp_buff, dstIp.country, sizeof(pRec->val_temp_buff));
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_BYTES;
        p->len  = strlen(pRec->val_temp_buff);
        p->val  = (void*)(size_t)(pRec->val_temp_buff);
        return p;
    }
    return NULL;
}
struct value_type*cb_share_dst_state(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    if (g_config.ip_position_switch)
    {
        IPINFO srcIp, dstIp;
        memset(&srcIp, 0, sizeof srcIp);
        memset(&dstIp, 0, sizeof dstIp);
        int direction = get_flow_direction(flow);
        dissect_ip_position(flow, direction, &srcIp, &dstIp);
        memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
        strncpy(pRec->val_temp_buff, dstIp.state, sizeof(pRec->val_temp_buff));
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_BYTES;
        p->len  = strlen(pRec->val_temp_buff);
        p->val  = (void*)(size_t)(pRec->val_temp_buff);
        return p;
    }
    return NULL;
}
struct value_type*cb_share_dst_city(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    if (g_config.ip_position_switch)
    {
        IPINFO srcIp, dstIp;
        memset(&srcIp, 0, sizeof srcIp);
        memset(&dstIp, 0, sizeof dstIp);
        int direction = get_flow_direction(flow);
        dissect_ip_position(flow, direction, &srcIp, &dstIp);
        memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
        strncpy(pRec->val_temp_buff, dstIp.city, sizeof(pRec->val_temp_buff));
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_BYTES;
        p->len  = strlen(pRec->val_temp_buff);
        p->val  = (void*)(size_t)(pRec->val_temp_buff);
        return p;
    }
    return NULL;
}
struct value_type*cb_share_dst_lon(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    if (g_config.ip_position_switch)
    {
        IPINFO srcIp, dstIp;
        memset(&srcIp, 0, sizeof srcIp);
        memset(&dstIp, 0, sizeof dstIp);
        int direction = get_flow_direction(flow);
        dissect_ip_position(flow, direction, &srcIp, &dstIp);
        memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
        strncpy(pRec->val_temp_buff, dstIp.longitude, sizeof(pRec->val_temp_buff));
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_BYTES;
        p->len  = strlen(pRec->val_temp_buff);
        p->val  = (void*)(size_t)(pRec->val_temp_buff);
        return p;
    }
    return NULL;
}
struct value_type*cb_share_dst_lat(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    if (g_config.ip_position_switch)
    {
        IPINFO srcIp, dstIp;
        memset(&srcIp, 0, sizeof srcIp);
        memset(&dstIp, 0, sizeof dstIp);
        int direction = get_flow_direction(flow);
        dissect_ip_position(flow, direction, &srcIp, &dstIp);
        memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
        strncpy(pRec->val_temp_buff, dstIp.latitude, sizeof(pRec->val_temp_buff));
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_BYTES;
        p->len  = strlen(pRec->val_temp_buff);
        p->val  = (void*)(size_t)(pRec->val_temp_buff);
        return p;
    }
    return NULL;
}
struct value_type*cb_share_dst_isp(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    if (g_config.ip_position_switch)
    {
        IPINFO srcIp, dstIp;
        memset(&srcIp, 0, sizeof srcIp);
        memset(&dstIp, 0, sizeof dstIp);
        int direction = get_flow_direction(flow);
        dissect_ip_position(flow, direction, &srcIp, &dstIp);
        memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
        strncpy(pRec->val_temp_buff, dstIp.isp, sizeof(pRec->val_temp_buff));
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_BYTES;
        p->len  = strlen(pRec->val_temp_buff);
        p->val  = (void*)(size_t)(pRec->val_temp_buff);
        return p;
    }
    return NULL;
}
struct value_type*cb_share_dst_asn(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    if (g_config.ip_position_switch)
    {
        IPINFO srcIp, dstIp;
        memset(&srcIp, 0, sizeof srcIp);
        memset(&dstIp, 0, sizeof dstIp);
        int direction = get_flow_direction(flow);
        dissect_ip_position(flow, direction, &srcIp, &dstIp);
        memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
        strncpy(pRec->val_temp_buff, dstIp.asn, sizeof(pRec->val_temp_buff));
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_BYTES;
        p->len  = strlen(pRec->val_temp_buff);
        p->val  = (void*)(size_t)(pRec->val_temp_buff);
        return p;
    }
    return NULL;
}
struct value_type*cb_share_out_addr_type(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->tuple.outer.ip_version);
    return p;
}
struct value_type*cb_share_out_src_addr(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    uint32_t ip = 0;
    int tuple_direction = get_flow_direction(flow);
    if (tuple_direction == FLOW_DIR_SRC2DST) {
        if (flow->ip_version == 4) ip = *(uint32_t*)flow->tuple.outer.ip_src;
    } else {
        if (flow->ip_version == 4) ip = *(uint32_t*)flow->tuple_reverse.outer.ip_dst;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)ip;
    return p;
}
struct value_type*cb_share_out_dst_addr(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    uint32_t ip = 0;
    int tuple_direction = get_flow_direction(flow);
    if (tuple_direction == FLOW_DIR_SRC2DST) {
        if (flow->ip_version == 4) ip = *(uint32_t*)flow->tuple.outer.ip_dst;
    } else {
        if (flow->ip_version == 4) ip = *(uint32_t*)flow->tuple_reverse.outer.ip_src;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)ip;
    return p;
}
struct value_type*cb_share_out_ipv6_src(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
    int tuple_direction = get_flow_direction(flow);
    if(flow->tuple.outer.ip_version == 6)
    {
        if (tuple_direction == FLOW_DIR_SRC2DST){
            get_ip6string(pRec->val_temp_buff, sizeof(pRec->val_temp_buff), flow->tuple.outer.ip_src);
        }else{
            get_ip6string(pRec->val_temp_buff, sizeof(pRec->val_temp_buff), flow->tuple_reverse.outer.ip_dst);
        }
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_BYTES;
    p->len  = strlen(pRec->val_temp_buff);
    p->val  = (void*)(size_t)(pRec->val_temp_buff);
    return p;
}
struct value_type*cb_share_out_ipv6_dst(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    memset(pRec->val_temp_buff, 0, sizeof(pRec->val_temp_buff));
    int tuple_direction = get_flow_direction(flow);
    if (flow->tuple.outer.ip_version == 6)
    {
        if (tuple_direction == FLOW_DIR_SRC2DST){
            get_ip6string(pRec->val_temp_buff, sizeof(pRec->val_temp_buff), flow->tuple.outer.ip_dst);
        }else{
            get_ip6string(pRec->val_temp_buff, sizeof(pRec->val_temp_buff), flow->tuple_reverse.outer.ip_src);
        }
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_BYTES;
    p->len  = strlen(pRec->val_temp_buff);
    p->val  = (void*)(size_t)(pRec->val_temp_buff);
    return p;
}
struct value_type*cb_share_out_port_src(ProtoRecord *pRec)
{
    int var = 0;
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    int tuple_direction = get_flow_direction(flow);
    if (tuple_direction == FLOW_DIR_SRC2DST) {
        var = ntohs(flow->tuple.outer.port_src);
    } else {
        var = ntohs(flow->tuple_reverse.outer.port_src);
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(var);
    return p;
}
struct value_type*cb_share_out_port_dst(ProtoRecord *pRec)
{
    int var = 0;
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    int tuple_direction = get_flow_direction(flow);
    if (tuple_direction == FLOW_DIR_SRC2DST){
        var = ntohs(flow->tuple.outer.port_dst);
    } else {
        var = ntohs(flow->tuple_reverse.outer.port_dst);
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(var);
    return p;
}
struct value_type*cb_share_out_proto(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(flow->tuple.outer.proto);
    return p;
}
struct value_type*cb_share_capture_time(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    struct value_type *p = &pRec->val_temp;
    p->type = VALUE_TYPE_UINT;
    p->len  = 0;
    p->val  = (void*)(size_t)(1749026062);
    return p;
}
struct value_type*cb_share_src_mac_oui(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    int direction = get_flow_direction(flow);
    uchar_t *mac = flow->session_ethhdr[direction].h_source;
    snprintf(pRec->val_temp_buff, sizeof(pRec->val_temp_buff), "%02X:%02X:%02X", mac[0], mac[1], mac[2]);
    char *result = g_hash_table_lookup(mac_oui_hash,  (gpointer)(pRec->val_temp_buff));
    if(result)
    {
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_BYTES;
        p->len  = strlen(result);
        p->val  = (void*)(size_t)(result);
        return p;
    }else{
        return NULL;
    }
}
struct value_type*cb_share_dst_mac_oui(ProtoRecord *pRec)
{
    struct flow_info *flow = pRec->flow;
    if(NULL == flow)
    {
        return NULL;
    }
    int direction = get_flow_direction(flow);
    uchar_t *mac = flow->session_ethhdr[direction].h_dest;
    snprintf(pRec->val_temp_buff, sizeof(pRec->val_temp_buff), "%02X:%02X:%02X", mac[0], mac[1], mac[2]);
    char *result = g_hash_table_lookup(mac_oui_hash,  (gpointer)(pRec->val_temp_buff));
    if(result)
    {
        struct value_type *p = &pRec->val_temp;
        p->type = VALUE_TYPE_BYTES;
        p->len  = strlen(result);
        p->val  = (void*)(size_t)(result);
        return p;
    }else{
        return NULL;
    }
}

static void mac_oui_destroy_key(gpointer hash_key)
 {
     if(hash_key){
         printf("destroy key:%s\n",(char *)hash_key);
         free(hash_key);
         hash_key = NULL;
     }
 }

static void mac_oui_destroy_value(gpointer hash_value)
 {
     if(hash_value){
         printf("destroy value:%d\n", *(int *)hash_value);
         free(hash_value);
         hash_value = NULL;
     }
 }

static int get_spit_words(char chop,char *srcStr, char **word, int size)
{
    int index = 0;
    int i = 0;
    char *str = srcStr;
    while (*(str + i) != '\0')
    {
        if (*(str + i) == chop)
        {
            word[index] = str;
            word[index++][i] = '\0';
            str = (str + i + 1);
            i = -1;
        }
        if (*(str + i) == '\r')
        {
            word[index] = str;
            word[index++][i] = '\0';
            str = (str + i);
            i = 0;
            break;
        }
        if (index >= size)
        {
            return index;
        }
        i++;
    }
    if (strlen(str) > 0)
    {
        word[index++] = str;
    }

    return index;
}


int read_manuf_data(void)
{
#define BUFFER_MAX_SIZE 1024
#define OUI_MAX_SIZE    32
    mac_oui_hash=g_hash_table_new_full(g_str_hash, g_str_equal,mac_oui_destroy_key, mac_oui_destroy_value);
    if(!mac_oui_hash){
        printf("file:%s,function:%s, g_hash_table_new_full create failed!\n",__FILE__,__FUNCTION__);
        exit(-1);
    }

    FILE       *fp = NULL;
    fp = fopen("./manuf", "r");
    if(!fp){
        printf("fopen manuf failed !\n");
        return -1;
    }

    char buff[BUFFER_MAX_SIZE]={0};
    char tmp_key[OUI_MAX_SIZE];
    char tmp_value[OUI_MAX_SIZE];
    int i;

    int space_cnt=0;
    int space_index[3]={0};
    while(fgets(buff, sizeof(buff), fp)!=NULL){
        if(buff[0]=='#' || buff[0]=='\n')
            continue;

        char *words[3]={NULL};
        get_spit_words('|', buff, words, 3);

        if(!words[0] || !words[1])
            continue;

        char *key=strdup(words[0]);
        char *value=strdup(words[1]);
        g_hash_table_insert(mac_oui_hash, (gpointer)key,(gpointer)value);
    }

    if(fp){
        fclose(fp);
        fp = NULL;
    }

    return 0;
}

void get_protoinfo(char * proto_info, uint16_t *layer, uint8_t layer_cnt)
{
    uint16_t index = 0;
    const char *_str;

    if (layer_cnt == 0) {
        return;
    }

    for (int i = 0; i < layer_cnt; ++i) {
        index = layer[i];
        if (index > PROTOCOL_MAX) {
           index -= PROTOCOL_MAX;
            _str = val_to_string(index, data_link_layer_proto);
            if (_str == NULL) {
                continue;
            }
            strcat(proto_info, _str);
        } else {
            strcat(proto_info, protocol_name_array[index]);
        }
        if (i < layer_cnt - 1) {
            strcat(proto_info, ".");
        }
    }
}

int write_shared_header(precord_t *record, int log_len_max, struct flow_info *flow, int direction) {
  int       i;
  int       ret;
  int       var = 0;
  char      __str[64];
  int       index = 0;
  int       *idx;

  idx = &index;
  player_t *layer = precord_layer_put_new_layer(record, "common");
  if (layer == NULL) {
    log_error("put lyaer faied");
  }

  int tuple_direction = 0;
  if (flow->drt_port_src[direction] > 0) {
    if (flow->drt_port_src[direction] > flow->drt_port_dst[direction]) {
      tuple_direction = FLOW_DIR_SRC2DST;  // c2s
    } else {
      tuple_direction = FLOW_DIR_DST2SRC;  // s2c
    }
    if (tuple_direction != direction) {
      tuple_direction = 1 - direction;
    }
  } else {
    tuple_direction = direction;
  }

  IPINFO srcIp, dstIp;
  memset(&srcIp, 0, sizeof srcIp); /* 防止输出随机值 */
  memset(&dstIp, 0, sizeof dstIp);
  if (g_config.ip_position_switch)  // 打开geoip模块
    dissect_ip_position(flow, direction, &srcIp, &dstIp);

  char real_prot[16] = {0};
  if (PROTOCOL_UNKNOWN == flow->real_protocol_id || PROTOCOL_SSL == flow->real_protocol_id) {
    if (ntohs(flow->tuple.inner.port_dst) == 465 || ntohs(flow->tuple.inner.port_src) == 465) {
      snprintf(real_prot, 16, "%s", "SMTPS");
    } else if (ntohs(flow->tuple.inner.port_dst) == 993 || ntohs(flow->tuple.inner.port_src) == 993) {
      snprintf(real_prot, 16, "%s", "IMAPS");
    } else if (ntohs(flow->tuple.inner.port_dst) == 995 || ntohs(flow->tuple.inner.port_src) == 995) {
      snprintf(real_prot, 16, "%s", "POP3S");
    }
  }
#ifdef DPI_SDT_YNAO
  for (i = 0; i < ENUM_SHARE_YNAO_MAX; i++) {
    switch (i) {
        //case ENUM_SHARE_YNAO_TAGS_SITE:
        //    break;
        //case ENUM_SHARE_YNAO_TAGS_UNIT:
        //    break;
        //case ENUM_SHARE_YNAO_TAGS_TASK:
        //case ENUM_SHARE_YNAO_TAGS_RULE:
        //case ENUM_SHARE_YNAO_TAGS_ANOMALY:
        //case ENUM_SHARE_YNAO_TAGS_THREAT:
        //case ENUM_SHARE_YNAO_TAGS_USER:
        //case ENUM_SHARE_YNAO_TAGS_ATTACK:
        case ENUM_SHARE_YNAO_TIME_CAPTURE:
            write_one_str_reconds(record, idx, log_len_max, g_config.time_str, STD_TIME_LEN);
            break;
        //case ENUM_SHARE_YNAO_TAGS_ISP:

        //case ENUM_SHARE_YNAO_MOBILE_IMSI:
        //case ENUM_SHARE_YNAO_MOBILE_IMEI:
        //case ENUM_SHARE_YNAO_MOBILE_MSISDN:
        //case ENUM_SHARE_YNAO_MOBILE_INTERFACE:
        case ENUM_SHARE_YNAO_MOBILE_SRCIP:
            if (tuple_direction == FLOW_DIR_SRC2DST) {  // c2s
                write_one_ip_reconds(record, idx, flow->ip_version, flow->tuple.inner.ip_src);
            } else {
                write_one_ip_reconds(record, idx, flow->ip_version, flow->tuple_reverse.inner.ip_src);
            }
            break;

        case ENUM_SHARE_YNAO_MOBILE_SRCPORT:
            if (tuple_direction == FLOW_DIR_SRC2DST) {
              write_one_num_reconds(record, idx, log_len_max, ntohs(flow->tuple.inner.port_src));
            } else {
              write_one_num_reconds(record, idx, log_len_max, ntohs(flow->tuple_reverse.inner.port_src));
            }
            break;

        case ENUM_SHARE_YNAO_MOBILE_DESTIP:
            if (tuple_direction == FLOW_DIR_SRC2DST) {  // c2s
                write_one_ip_reconds(record, idx, flow->ip_version, flow->tuple.inner.ip_dst);
            } else {
                write_one_ip_reconds(record, idx, flow->ip_version, flow->tuple_reverse.inner.ip_dst);
            }
            break;

        case ENUM_SHARE_YNAO_MOBILE_DESTPORT:
            if (tuple_direction == FLOW_DIR_SRC2DST) {
              write_one_num_reconds(record, idx, log_len_max, ntohs(flow->tuple.inner.port_dst));
            } else {
              write_one_num_reconds(record, idx, log_len_max, ntohs(flow->tuple_reverse.inner.port_dst));
            }
            break;
        //case ENUM_SHARE_YNAO_MOBILE_APN:
        //case ENUM_SHARE_YNAO_MOBILE_ECGI:
        //case ENUM_SHARE_YNAO_MOBILE_TAI:
        case ENUM_SHARE_YNAO_MOBILE_TEID:
            if (flow->tunnel_id) {
                if (flow->real_protocol_id == PROTOCOL_GTP_U)
                    write_one_hexnum_reconds(record, idx, TBL_LOG_MAX_LEN, flow->tunnel_id);
                else if (flow->real_protocol_id == PROTOCOL_L2TP)
                    write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, flow->tunnel_id);
                else
                    write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);

                flow->tunnel_id = 0;
            }
            else
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            break;
 
        //case ENUM_SHARE_YNAO_MOBILE_SRCISP:
        //case ENUM_SHARE_YNAO_MOBILE_DESTISP:
        //case ENUM_SHARE_YNAO_MOBILE_LOCAL_PROVINCE:
        //case ENUM_SHARE_YNAO_MOBILE_LOCAL_CITY:
        //case ENUM_SHARE_YNAO_MOBILE_OWNER_PROVINCE:
        //case ENUM_SHARE_YNAO_MOBILE_OWNER_CITY:
        //case ENUM_SHARE_YNAO_MOBILE_ROAMING_TYPE:
        //case ENUM_SHARE_YNAO_MOBILE_RAT:

        case ENUM_SHARE_YNAO_MAP_STREAM_ID:
            write_uint64_reconds(record, idx, TBL_LOG_MAX_LEN, flow->map_stream_id);
            break;
        case ENUM_SHARE_YNAO_IP_PROTO:
            write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, flow->tuple.inner.proto);
            break;
        case ENUM_SHARE_YNAO_PROTO_INFO:
            write_one_str_reconds(record, idx, log_len_max, protocol_name_array[flow->real_protocol_id],
                strlen(protocol_name_array[flow->real_protocol_id]));

            break;
        default:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        }
    }
#else
  // Data_SRC.Global_LineNO B.1.2.(3) 要求本次自定义转换输出库
  uint32_t HW[4] = {0,0,0,0};
  char                      buff_LINENAME1[512] = {0};
  char                      buff_LINENAME2[20] = {0};
  struct mac_packet_header *psdx               = flow->pSDTMacHeader[direction];
  if (psdx) {
    sdx_convert_linename(psdx->Datasrc.Global_LineNO, buff_LINENAME1, HW);
  }

  for (i = 0; i < ENUM_SHARE_HEADER_MAX; i++) {
    switch (i) {
      case ENUM_SHARE_HEADER_LINENO1:
        write_one_num_reconds(record, idx, log_len_max, HW[0]);
        break;
      case ENUM_SHARE_HEADER_LINENO2:
        write_one_num_reconds(record, idx, log_len_max, HW[1]);
        break;
      case ENUM_SHARE_HEADER_LINENO3:
        write_one_num_reconds(record, idx, log_len_max, HW[2]);
        break;
      case ENUM_SHARE_HEADER_LINENO4:
        write_one_num_reconds(record, idx, log_len_max, HW[3]);
        break;
      case ENUM_SHARE_HEADER_LINENAME1:
        write_one_str_reconds(record, idx, log_len_max, buff_LINENAME1, strlen(buff_LINENAME1));
        break;
      case ENUM_SHARE_HEADER_LINENAME2:
        write_one_str_reconds(record, idx, log_len_max, buff_LINENAME2, strlen(buff_LINENAME2));
        break;
      case ENUM_SHARE_HEADER_SYSFROM:
        write_one_str_reconds(record, idx, log_len_max, g_config.web_config.sys_from, 
                            strlen(g_config.web_config.sys_from));
        break;
      case ENUM_SHARE_HEADER_DATAFROM:
        write_one_str_reconds(record, idx, log_len_max, g_config.web_config.data_from,
                            strlen(g_config.web_config.data_from));
        break;
      case ENUM_SHARE_HEADER_SIGTYPE:
        write_one_str_reconds(record, idx, log_len_max, g_config.web_config.sig_type,
                            strlen(g_config.web_config.sig_type));
        break;
     /*
      case ENUM_SHARE_HEADER_INFOTYPE:
        write_one_str_reconds(record, idx, log_len_max, buff_LINENAME2, strlen(buff_LINENAME2));
        break;
      */
      case ENUM_SHARE_HEADER_BEGTIME:
        write_uint64_reconds(record, idx, log_len_max, flow->create_time / 1e3);
        break;

      case ENUM_SHARE_HEADER_ENDTIME:
        write_uint64_reconds(record, idx, log_len_max, flow->end_time / 1e3);
        break;

      case ENUM_SHARE_HEADER_COMDUR:
        write_one_num_reconds(record, idx, log_len_max, flow->end_time - flow->create_time);
        break;
      // case ENUM_SHARE_HEADER_MEANID:  // 方式代码

      //    break;
      // case ENUM_SHARE_HEADER_SITEID:  // DD代码

      //    break;
      // case ENUM_SHARE_HEADER_UNITID:  // 单位代码

      //    break;
      //case ENUM_SHARE_HEADER_TASKID:  // 任务代码
      //  write_one_str_reconds(record, idx, log_len_max, g_config.task_id, strlen(g_config.task_id));
      //  break;

      case ENUM_SHARE_HEADER_GUID:  // 唯一代码
      {
        uint64_t guid = 0;
        uint8_t  zid  = g_config.hardware_zidnumber;
        guid |= (uint64_t)(flow->timestamp / 1000000) << 32;
        guid |= (uint64_t)(zid & 0xF) << 28;
        uint8_t ip_         = '\0';
        char    ip_addr[64] = {0};
        ip_                 = (uint8_t)ntohl(inet_addr(g_config.sdx_config.sdx_ip_str));
        guid |= (uint64_t)(ip_ & 0xFF) << 20;
        guid |= (uint64_t)(flow->flow_cycle);
        write_one_num_reconds(record, idx, log_len_max, guid);
      } break;

      //    break;
      // case ENUM_SHARE_HEADER_STORTIME:  // 入库时间

      //    break;
      case ENUM_SHARE_HEADER_MDSECDEG:  // 元数据密级
        write_one_str_reconds(record, idx, log_len_max, g_config.mdsecdeg, strlen(g_config.mdsecdeg));
        break;
      case ENUM_SHARE_HEADER_FILESECDEG:  // 数据文件密级
        write_one_str_reconds(record, idx, log_len_max, g_config.filesecdeg, strlen(g_config.filesecdeg));
        break;
      case ENUM_SHARE_HEADER_SECDEGPRO:  // 安全等级保护
        write_one_str_reconds(record, idx, log_len_max, g_config.secdegpro, strlen(g_config.secdegpro));
        break;

      case ENUM_SHARE_HEADER_IPVER:
        write_one_num_reconds(record, idx, log_len_max, flow->ip_version);
        break;

      case ENUM_SHARE_HEADER_TTL: {
        int ttl = flow->last_ttl;
        write_one_num_reconds(record, idx, log_len_max, ttl);
      } break;

      case ENUM_SHARE_HEADER_SRCADDR:
        //附录B.4.2 PDF 中要求 ip.src 是 BIGINT
        if (tuple_direction == FLOW_DIR_SRC2DST) {  // c2s
          if (flow->ip_version == 4) var = *(uint32_t*)flow->tuple.inner.ip_src;
        } else {
          if (flow->ip_version == 4) var = *(uint32_t*)flow->tuple_reverse.inner.ip_src;
        }
        write_one_num_reconds(record, idx, log_len_max, var);
        break;
      case ENUM_SHARE_HEADER_DSTADDR:
        //附录B.4.2 PDF 中要求 ip.dst 是 BIGINT
        if (tuple_direction == FLOW_DIR_SRC2DST) {  // c2s
          if (flow->ip_version == 4) var = *(uint32_t*)flow->tuple.inner.ip_dst;
        } else {
          if (flow->ip_version == 4) var = *(uint32_t*)flow->tuple_reverse.inner.ip_dst;
        }
        write_one_num_reconds(record, idx, log_len_max, var);
        break;
      case ENUM_SHARE_HEADER_SRCPORT:
        if (tuple_direction == FLOW_DIR_SRC2DST) {  // EM_COMMON_SRCPORT
          write_one_num_reconds(record, idx, log_len_max, ntohs(flow->tuple.inner.port_src));
        } else {
          write_one_num_reconds(record, idx, log_len_max, ntohs(flow->tuple_reverse.inner.port_src));
        }

        break;
      case ENUM_SHARE_HEADER_DSTPORT:
        if (tuple_direction == FLOW_DIR_SRC2DST) {  // EM_COMMON_DSTPORT
          write_one_num_reconds(record, idx, log_len_max, ntohs(flow->tuple.inner.port_dst));
        } else {
          write_one_num_reconds(record, idx, log_len_max, ntohs(flow->tuple_reverse.inner.port_dst));
        }
        break;
      case ENUM_SHARE_HEADER_PROTNUM:
        write_one_num_reconds(record, idx, log_len_max, flow->tuple.inner.proto);  // EM_COMMON_PROTO
        break;
      case ENUM_SHARE_HEADER_SRCADDRV6:
        __str[0] = 0;
        if (tuple_direction == FLOW_DIR_SRC2DST) {  // c2s
          if (flow->ip_version == 6) get_ip6string(__str, sizeof(__str), flow->tuple.inner.ip_src);
        } else {
          if (flow->ip_version == 6) get_ip6string(__str, sizeof(__str), flow->tuple_reverse.inner.ip_src);
        }
        write_one_str_reconds(record, idx, log_len_max, __str, strlen(__str));
        break;
      case ENUM_SHARE_HEADER_DSTADDRV6:
        __str[0] = 0;
        if (tuple_direction == FLOW_DIR_SRC2DST) {  // c2s
          if (flow->ip_version == 6) get_ip6string(__str, sizeof(__str), flow->tuple.inner.ip_dst);
        } else {
          if (flow->ip_version == 6) get_ip6string(__str, sizeof(__str), flow->tuple_reverse.inner.ip_dst);
        }
        write_one_str_reconds(record, idx, log_len_max, __str, strlen(__str));
        break;

      case ENUM_SHARE_HEADER_PROTINFO: {
        char proto_info[2048] = {0};
        get_protoinfo(proto_info, flow->proto_layer, flow->proto_layer_cnt);
        write_one_str_reconds(record, idx, log_len_max, (const char *)proto_info, strlen(proto_info));
        break;
      }
      case ENUM_SHARE_HEADER_PROTTYPE:
        write_one_str_reconds(record, idx, log_len_max, (const char *)protocol_name_array[flow->real_protocol_id],
                              strlen((char *)protocol_name_array[flow->real_protocol_id]));
        break;
      case ENUM_SHARE_HEADER_PROTNAME:
        write_string_reconds(record, idx, log_len_max, dpi_high_proto_find_by_id(flow->high_app_proto_id)->name);
        break;
      // case ENUM_SHARE_HEADER_MULROUFLAG:

      //    break;
      case ENUM_SHARE_HEADER_INTFLAG:
        if(flow->real_protocol_id==PROTOCOL_HTTP||
          flow->real_protocol_id==PROTOCOL_SOCKS||flow->real_protocol_id==PROTOCOL_SSH){
            write_one_num_reconds(record, idx, log_len_max, flow->intflag);
          }else {
          write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
          }
           break;
        case ENUM_SHARE_HEADER_STRDIREC:
            if (tuple_direction == FLOW_DIR_SRC2DST){  // c2s 2
                write_one_num_reconds(record, idx, log_len_max, 2);
            }else{  // s2c  1
                write_one_num_reconds(record, idx, log_len_max, 1);
            }

            break;
        case ENUM_SHARE_HEADER_PKTNUM:
            write_one_num_reconds(record, idx, log_len_max, flow->src2dst_packets+flow->dst2src_packets);
            break;
        case ENUM_SHARE_HEADER_PAYLEN:
            write_one_num_reconds(record, idx, log_len_max, flow->src2dst_payload_len+flow->dst2src_payload_len);
            break;
        case ENUM_SHARE_HEADER_STREAMID:
            __str[0]=0;
            if(flow->real_protocol_id == PROTOCOL_HTTP)
                ret = snprintf(__str, 64, "%u%u%lu%u_%d",
                                        g_config.mac, flow->thread_id, flow->flow_id,
                                        flow->flow_cycle, flow->sub_flow_id);
            else
                ret = snprintf(__str, 64, "%u%u%lu%u", g_config.mac,
                                        flow->thread_id, flow->flow_id, flow->flow_cycle);
            write_one_str_reconds(record, idx, log_len_max, __str, ret);
            break;


        /*
        case ENUM_SHARE_HEADER_ETAGS:

            break;
        case ENUM_SHARE_HEADER_TTAGS:

            break;
        case ENUM_SHARE_HEADER_ATAGS:

            break;
        case ENUM_SHARE_HEADER_UTAGS:

            break;
        */
        case ENUM_SHARE_HEADER_LABLE1:
            if(flow->is_mpls>0)
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, common_field_array[i].type, NULL, flow->data_link_layer.mpls_label[0]);
            else
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        case ENUM_SHARE_HEADER_LABLE2:
            if(flow->is_mpls>1)
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, common_field_array[i].type, NULL, flow->data_link_layer.mpls_label[1]);
            else
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        case ENUM_SHARE_HEADER_LABLE3:
            if(flow->is_mpls>2)
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, common_field_array[i].type, NULL, flow->data_link_layer.mpls_label[2]);
            else
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        case ENUM_SHARE_HEADER_LABLE4:
            if(flow->is_mpls>3)
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, common_field_array[i].type, NULL, flow->data_link_layer.mpls_label[3]);
            else
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;


        case ENUM_SHARE_HEADER_VLANID1: // vlan id 1
            if(flow->vlan_flag>0)
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, common_field_array[i].type, NULL, flow->data_link_layer.vlan_id[0]);
            else
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        case ENUM_SHARE_HEADER_VLANID2: // vlan id 2
            if(flow->vlan_flag>1)
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, common_field_array[i].type, NULL, flow->data_link_layer.vlan_id[1]);
            else
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        case ENUM_SHARE_HEADER_SRCMAC:  // src mac
            get_macstring(__str, sizeof(__str), flow->session_ethhdr[direction].h_source);
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, __str, strlen(__str));
            break;
        case ENUM_SHARE_HEADER_DSTMAC:  // dst mac
            get_macstring(__str, sizeof(__str), flow->session_ethhdr[direction].h_dest);
            write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN,  __str, strlen(__str));
            break;

        case ENUM_SHARE_HEADER_TUNNELID:
            if (flow->tunnel_id) {
                if (flow->real_protocol_id == PROTOCOL_GTP_U)
                    write_one_hexnum_reconds(record, idx, TBL_LOG_MAX_LEN, flow->tunnel_id);
                else if (flow->real_protocol_id == PROTOCOL_L2TP)
                    write_one_num_reconds(record, idx, TBL_LOG_MAX_LEN, flow->tunnel_id);
                else
                    write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);

                flow->tunnel_id = 0;
            }
            else
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            break;

        case ENUM_SHARE_HEADER_SRCCOUNTRY:
            if(g_config.ip_position_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, srcIp.country, strlen(srcIp.country));
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }

            break;
        case ENUM_SHARE_HEADER_SRCSTATE:
            if(g_config.ip_position_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, srcIp.state, strlen(srcIp.state));
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case ENUM_SHARE_HEADER_SRCCITY:
            if(g_config.ip_position_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, srcIp.city, strlen(srcIp.city));       //EM_COMMON_SRCCITY
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case ENUM_SHARE_HEADER_SRCLONGITUDE:
            if(g_config.mmdb_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, srcIp.longitude, strlen(srcIp.longitude));       //EM_COMMON_SRCCITY
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case ENUM_SHARE_HEADER_SRCLATITUDE:
            if(g_config.mmdb_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, srcIp.latitude, strlen(srcIp.latitude));       //EM_COMMON_SRCCITY
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case ENUM_SHARE_HEADER_SRCISP:
            if(g_config.ip_position_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, srcIp.isp, strlen(srcIp.isp));       //EM_COMMON_SRCCITY
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case ENUM_SHARE_HEADER_SRCASN:
            if(g_config.mmdb_asn_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, srcIp.asn, strlen(srcIp.asn));       //EM_COMMON_SRCCITY
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;

        case ENUM_SHARE_HEADER_DSTCOUNTRY:
            if(g_config.ip_position_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, dstIp.country, strlen(dstIp.country));
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case ENUM_SHARE_HEADER_DSTSTATE:
            if(g_config.ip_position_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, dstIp.state, strlen(dstIp.state));
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case ENUM_SHARE_HEADER_DSTCITY:
            if(g_config.ip_position_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, dstIp.city, strlen(dstIp.city));       //EM_COMMON_SRCCITY
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case ENUM_SHARE_HEADER_DSTLONGITUDE:
            if(g_config.mmdb_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, dstIp.longitude, strlen(dstIp.longitude));       //EM_COMMON_SRCCITY
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case ENUM_SHARE_HEADER_DSTLATITUDE:
            if(g_config.mmdb_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, dstIp.latitude, strlen(dstIp.latitude));       //EM_COMMON_SRCCITY
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case ENUM_SHARE_HEADER_DSTISP:
            if(g_config.ip_position_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, dstIp.isp, strlen(dstIp.isp));       //EM_COMMON_SRCCITY
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;
        case ENUM_SHARE_HEADER_DSTASN:
            if(g_config.mmdb_asn_switch){
                write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, dstIp.asn, strlen(dstIp.asn));       //EM_COMMON_SRCCITY
            }else{
                write_n_empty_reconds(record, idx, TBL_LOG_MAX_LEN, 1);
            }
            break;



        case ENUM_SHARE_HEADER_OUTADDRTYPE:  // outer ip version
            write_one_num_reconds(record, idx, log_len_max,flow->tuple.outer.ip_version);
            break;
        case ENUM_SHARE_HEADER_OUTSRCADDR:
             __str[0]=0;
            if (tuple_direction == FLOW_DIR_SRC2DST){// c2s
                if (flow->tuple.outer.ip_version == 4)
                    get_iparray_to_string(__str, sizeof(__str), flow->tuple.outer.ip_src);
            }else{
                if (flow->tuple.outer.ip_version == 4)
                    get_iparray_to_string(__str, sizeof(__str), flow->tuple_reverse.outer.ip_src);
            }
            write_one_str_reconds(record, idx, log_len_max, __str, strlen(__str));
            break;
        case ENUM_SHARE_HEADER_OUTDSTADDR:
            __str[0]=0;
            if (tuple_direction == FLOW_DIR_SRC2DST){ // s2c
                if (flow->tuple.outer.ip_version == 4)
                    get_iparray_to_string(__str, sizeof(__str), flow->tuple.outer.ip_dst);
            }else{
                if (flow->tuple.outer.ip_version == 4)
                    get_iparray_to_string(__str, sizeof(__str), flow->tuple_reverse.outer.ip_dst);
            }
            write_one_str_reconds(record, idx, log_len_max, __str, strlen(__str));
            break;
        case ENUM_SHARE_HEADER_OUTER_IPV6_SRC:
            __str[0]=0;
            if (tuple_direction == FLOW_DIR_SRC2DST){// c2s
                if (flow->tuple.outer.ip_version == 6)
                    get_ip6string(__str, sizeof(__str), flow->tuple.outer.ip_src);
            }else{
                if (flow->tuple.outer.ip_version == 6)
                    get_ip6string(__str, sizeof(__str), flow->tuple_reverse.outer.ip_src);
            }
            write_one_str_reconds(record, idx, log_len_max, __str, strlen(__str));
            break;
        case ENUM_SHARE_HEADER_OUTER_IPV6_DST:
            __str[0]=0;
            if (tuple_direction == FLOW_DIR_SRC2DST){ // c2s
                if (flow->tuple.outer.ip_version == 6)
                    get_ip6string(__str, sizeof(__str), flow->tuple.outer.ip_dst);
            }else{
                if (flow->tuple.outer.ip_version == 6)
                    get_ip6string(__str, sizeof(__str), flow->tuple_reverse.outer.ip_dst);
            }
            write_one_str_reconds(record, idx, log_len_max, __str, strlen(__str));
            break;
        case ENUM_SHARE_HEADER_OUTSRCPORT:
            if (tuple_direction == FLOW_DIR_SRC2DST) {                                                             //EM_COMMON_SRCPORT
                write_one_num_reconds(record, idx, log_len_max, ntohs(flow->tuple.outer.port_src));
            } else {
                write_one_num_reconds(record, idx, log_len_max, ntohs(flow->tuple_reverse.outer.port_src));
            }
            break;
        case ENUM_SHARE_HEADER_OUTDSTPORT:

            if (tuple_direction == FLOW_DIR_SRC2DST) {                                                             //EM_COMMON_DSTPORT
                write_one_num_reconds(record, idx, log_len_max, ntohs(flow->tuple.outer.port_dst));
            } else {
                write_one_num_reconds(record, idx, log_len_max, ntohs(flow->tuple_reverse.outer.port_dst));
            }
            break;
        case ENUM_SHARE_HEADER_OUTTRANSPROTO:
            write_one_num_reconds(record, idx, log_len_max, flow->tuple.outer.proto);                 //EM_COMMON_PROTO
            break;

        case ENUM_SHARE_HEADER_CAPTURETIME:
            write_one_str_reconds(record, idx, log_len_max, g_config.time_str, STD_TIME_LEN);
            break;
        case ENUM_SHARE_HEADER_SRCMACOUI:
        {
            char *result=NULL;
            uchar_t *mac = flow->session_ethhdr[direction].h_source;
            snprintf(__str, 64, "%02x:%02x:%02x", mac[0], mac[1], mac[2]);
            result   = g_hash_table_lookup(mac_oui_hash,  (gpointer)__str);
            if(result){
                write_one_str_reconds(record, idx, log_len_max,(char *)result, strlen((char *)result));
            }else{
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            }
        }
            break;
        case ENUM_SHARE_HEADER_DSTMACOUI:
        {
            char *result=NULL;
            uchar_t *mac = flow->session_ethhdr[direction].h_dest;
            snprintf(__str, 64, "%02x:%02x:%02x", mac[0], mac[1], mac[2]);
            result   = g_hash_table_lookup(mac_oui_hash,  (gpointer)__str);
            if(result){
                write_one_str_reconds(record, idx, log_len_max,(char *)result, strlen((char *)result));
            }else{
                write_coupler_log(record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            }
        }
            break;
        default:
            write_coupler_log(record, idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
            break;
        }
    }
#endif

    return 0;
}

int write_tbl_log_share_header(struct tbl_log *log_ptr, int log_len_max, struct flow_info *flow, int direction) {
  precord_t *record = log_ptr->record;

  write_shared_header(record, log_len_max, flow, direction);
  return 0;
}

int dpi_pschema_get_share_header_field(dpi_field_table *field_table_array[])
{
    *field_table_array = common_field_array;
    return ARRAY_LEN(common_field_array);
}

static void init_share_header_dissector(void)
{
#ifdef DPI_SDT_YNAO
    //common_field_array[ENUM_SHARE_YNAO_TAGS_SITE].callback    =;
    //common_field_array[ENUM_SHARE_YNAO_TAGS_UNIT].callback    =;
    //common_field_array[ENUM_SHARE_YNAO_TAGS_TASK].callback    =;
    //common_field_array[ENUM_SHARE_YNAO_TAGS_RULE].callback    =;
    //common_field_array[ENUM_SHARE_YNAO_TAGS_ANOMALY].callback    =;
    //common_field_array[ENUM_SHARE_YNAO_TAGS_THREAT].callback    =;
    //common_field_array[ENUM_SHARE_YNAO_TAGS_USER].callback    =;
    //common_field_array[ENUM_SHARE_YNAO_TAGS_ATTACK].callback    =;
    common_field_array[ENUM_SHARE_YNAO_TIME_CAPTURE].callback    = cb_share_capture_time;
    //common_field_array[ENUM_SHARE_YNAO_TAGS_ISP].callback    =;
    //common_field_array[ENUM_SHARE_YNAO_MOBILE_IMSI].callback    =;
    //common_field_array[ENUM_SHARE_YNAO_MOBILE_IMEI].callback    =;
    //common_field_array[ENUM_SHARE_YNAO_MOBILE_MSISDN].callback    =;
    //common_field_array[ENUM_SHARE_YNAO_MOBILE_INTERFACE].callback    =;
    common_field_array[ENUM_SHARE_YNAO_MOBILE_SRCIP].callback        = cb_share_src_addr;
    common_field_array[ENUM_SHARE_YNAO_MOBILE_SRCPORT].callback      = cb_share_src_port;
    common_field_array[ENUM_SHARE_YNAO_MOBILE_DESTIP].callback       = cb_share_dst_addr;
    common_field_array[ENUM_SHARE_YNAO_MOBILE_DESTPORT].callback     = cb_share_dst_port;
    //common_field_array[ENUM_SHARE_YNAO_MOBILE_APN].callback    =;
    //common_field_array[ENUM_SHARE_YNAO_MOBILE_ECGI].callback    =;
    //common_field_array[ENUM_SHARE_YNAO_MOBILE_TAI].callback    =;
    //common_field_array[ENUM_SHARE_YNAO_MOBILE_TEID].callback    =; 
    //common_field_array[ENUM_SHARE_YNAO_MOBILE_SRCISP].callback    =;
    //common_field_array[ENUM_SHARE_YNAO_MOBILE_DESTISP].callback    =;
    //common_field_array[ENUM_SHARE_YNAO_MOBILE_LOCAL_PROVINCE].callback    =;
    //common_field_array[ENUM_SHARE_YNAO_MOBILE_LOCAL_CITY].callback    =;
    //common_field_array[ENUM_SHARE_YNAO_MOBILE_OWNER_PROVINCE].callback    =;
    //common_field_array[ENUM_SHARE_YNAO_MOBILE_OWNER_CITY].callback    =;
    //common_field_array[ENUM_SHARE_YNAO_MOBILE_ROAMING_TYPE].callback    =;
    //common_field_array[ENUM_SHARE_YNAO_MOBILE_RAT].callback    =;

    map_fields_info_register(common_field_array, PROTOCOL_SHARE_HEADER, ENUM_SHARE_YNAO_MAX,"common");
    dpi_register_proto_schema(common_field_array, ENUM_SHARE_YNAO_MAX, "common");
#else
    common_field_array[ENUM_SHARE_HEADER_LINENAME1].callback      = cb_share_linename1;
    common_field_array[ENUM_SHARE_HEADER_LINENAME2].callback      = cb_share_linename1;
    common_field_array[ENUM_SHARE_HEADER_BEGTIME].callback        = cb_share_begin_time;
    common_field_array[ENUM_SHARE_HEADER_ENDTIME].callback        = cb_share_end_time;
    common_field_array[ENUM_SHARE_HEADER_COMDUR].callback         = cb_share_dru_time;
    common_field_array[ENUM_SHARE_HEADER_IPVER].callback          = cb_share_ip_ver;
    common_field_array[ENUM_SHARE_HEADER_SRCADDR].callback        = cb_share_src_addr;
    common_field_array[ENUM_SHARE_HEADER_DSTADDR].callback        = cb_share_dst_addr;
    common_field_array[ENUM_SHARE_HEADER_SRCPORT].callback        = cb_share_src_port;
    common_field_array[ENUM_SHARE_HEADER_DSTPORT].callback        = cb_share_dst_port;
    common_field_array[ENUM_SHARE_HEADER_PROTNUM].callback        = cb_share_porto;
    common_field_array[ENUM_SHARE_HEADER_SRCADDRV6].callback      = cb_share_src_addr_v6;
    common_field_array[ENUM_SHARE_HEADER_DSTADDRV6].callback      = cb_share_dst_addr_v6;
    common_field_array[ENUM_SHARE_HEADER_PROTINFO].callback       = cb_share_proto_info;
    common_field_array[ENUM_SHARE_HEADER_PROTTYPE].callback       = cb_share_proto_type;
    common_field_array[ENUM_SHARE_HEADER_PROTNAME].callback       = cb_share_proto_name;
    common_field_array[ENUM_SHARE_HEADER_PKTNUM].callback         = cb_share_pkt_num;
    common_field_array[ENUM_SHARE_HEADER_PAYLEN].callback         = cb_share_pkt_len;
    common_field_array[ENUM_SHARE_HEADER_ETAGS].callback          = cb_share_etags;
    common_field_array[ENUM_SHARE_HEADER_TTAGS].callback          = cb_share_ttags;
    common_field_array[ENUM_SHARE_HEADER_ATAGS].callback          = cb_share_atags;
    common_field_array[ENUM_SHARE_HEADER_UTAGS].callback          = cb_share_utags;
    common_field_array[ENUM_SHARE_HEADER_LABLE1].callback         = cb_share_mpls_lable1;
    common_field_array[ENUM_SHARE_HEADER_LABLE2].callback         = cb_share_mpls_lable2;
    common_field_array[ENUM_SHARE_HEADER_LABLE3].callback         = cb_share_mpls_lable3;
    common_field_array[ENUM_SHARE_HEADER_LABLE4].callback         = cb_share_mpls_lable4;
    common_field_array[ENUM_SHARE_HEADER_VLANID1].callback        = cb_share_vlan_id1;
    common_field_array[ENUM_SHARE_HEADER_VLANID2].callback        = cb_share_vlan_id2;
    common_field_array[ENUM_SHARE_HEADER_SRCMAC].callback         = cb_share_src_mac;
    common_field_array[ENUM_SHARE_HEADER_DSTMAC].callback         = cb_share_dst_mac;
    common_field_array[ENUM_SHARE_HEADER_SRCCOUNTRY].callback     = cb_share_src_ountry;
    common_field_array[ENUM_SHARE_HEADER_SRCSTATE].callback       = cb_share_src_state;
    common_field_array[ENUM_SHARE_HEADER_SRCCITY].callback        = cb_share_src_city;
    common_field_array[ENUM_SHARE_HEADER_SRCLONGITUDE].callback   = cb_share_src_lon;
    common_field_array[ENUM_SHARE_HEADER_SRCLATITUDE].callback    = cb_share_src_lat;
    common_field_array[ENUM_SHARE_HEADER_SRCISP].callback         = cb_share_src_isp;
    common_field_array[ENUM_SHARE_HEADER_SRCASN].callback         = cb_share_src_asn;
    common_field_array[ENUM_SHARE_HEADER_DSTCOUNTRY].callback     = cb_share_dst_ountry;
    common_field_array[ENUM_SHARE_HEADER_DSTSTATE].callback       = cb_share_dst_state;
    common_field_array[ENUM_SHARE_HEADER_DSTCITY].callback        = cb_share_dst_city;
    common_field_array[ENUM_SHARE_HEADER_DSTLONGITUDE].callback   = cb_share_dst_lon;
    common_field_array[ENUM_SHARE_HEADER_DSTLATITUDE].callback    = cb_share_dst_lat;
    common_field_array[ENUM_SHARE_HEADER_DSTISP].callback         = cb_share_dst_isp;
    common_field_array[ENUM_SHARE_HEADER_DSTASN].callback         = cb_share_dst_asn;
    common_field_array[ENUM_SHARE_HEADER_OUTADDRTYPE].callback    = cb_share_out_addr_type;
    common_field_array[ENUM_SHARE_HEADER_OUTSRCADDR].callback     = cb_share_out_src_addr;
    common_field_array[ENUM_SHARE_HEADER_OUTDSTADDR].callback     = cb_share_out_dst_addr;
    common_field_array[ENUM_SHARE_HEADER_OUTER_IPV6_SRC].callback = cb_share_out_ipv6_src;
    common_field_array[ENUM_SHARE_HEADER_OUTER_IPV6_DST].callback = cb_share_out_ipv6_dst;
    common_field_array[ENUM_SHARE_HEADER_OUTSRCPORT].callback     = cb_share_out_port_src;
    common_field_array[ENUM_SHARE_HEADER_OUTDSTPORT].callback     = cb_share_out_port_dst;
    common_field_array[ENUM_SHARE_HEADER_OUTTRANSPROTO].callback  = cb_share_out_proto;
    common_field_array[ENUM_SHARE_HEADER_CAPTURETIME].callback    = cb_share_capture_time;
    common_field_array[ENUM_SHARE_HEADER_SRCMACOUI].callback      = cb_share_src_mac_oui;
    common_field_array[ENUM_SHARE_HEADER_DSTMACOUI].callback      = cb_share_dst_mac_oui;

    map_fields_info_register(common_field_array, PROTOCOL_SHARE_HEADER, ENUM_SHARE_HEADER_MAX,"common");
    dpi_register_proto_schema(common_field_array, ENUM_SHARE_HEADER_MAX, "common");
#endif
    read_manuf_data();
    return;
}


static __attribute((constructor)) void     before_init_share_header(void){
    register_tbl_array(TBL_LOG_SHARE_HEADER, 0, "common", init_share_header_dissector);
}

