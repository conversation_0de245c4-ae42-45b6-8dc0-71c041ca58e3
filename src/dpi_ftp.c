/****************************************************************************************
 * 文 件 名 : dpi_ftp.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/07/06
编码: wangy            2018/07/06
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <arpa/inet.h>
#include <ctype.h>
#include <iconv.h>
#include <netinet/in.h>
#include <rte_mempool.h>
#include <stdbool.h>
#include <stdio.h>
#include <string.h>
#include <yaFtypes/fvalue.h>
#include <yaProtoRecord/precord.h>

#include "dpi_common.h"
#include "dpi_conversation.h"
#include "dpi_detect.h"
#include "dpi_dissector.h"
#include "dpi_flow.h"
#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_pschema.h"
#include "dpi_tbl_log.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_typedefs.h"
#include "dpi_utils.h"
#include "pch.h"
#include "utils/dpi_utils.h"
#define EPRT_AF_IPv4 1
#define EPRT_AF_IPv6 2

#define FTP_CONTROL_PORT 21
#define FTP_DATA_PORT 20
#define FTP_MAX_SEARCH 128

extern struct global_config g_config;
extern struct rte_mempool  *tbl_log_mempool;
int                         isUTF8(const char *pData, int len);

/* add by liugh */
extern int session_protocol_st_size[PROTOCOL_MAX];

static int ftp_update_conversion_session_data_ex(
    struct ftp_session *dst_sesseion, struct ftp_session *src_session, bool copy_filename) {
  if (NULL == dst_sesseion) {
    return 0;
  }
  memcpy(dst_sesseion->username, src_session->username, sizeof(dst_sesseion->username));
  memcpy(dst_sesseion->password, src_session->password, sizeof(dst_sesseion->password));
  memcpy(dst_sesseion->hostName, src_session->hostName, sizeof(dst_sesseion->hostName));
  memcpy(dst_sesseion->software, src_session->software, sizeof(dst_sesseion->software));

  // 根据copy_filename参数决定是否拷贝文件名
  if (copy_filename && (strlen(dst_sesseion->storpath) == 0) && src_session->storpath[0] != '\0') {
    memcpy(dst_sesseion->storpath, src_session->storpath, sizeof(dst_sesseion->storpath));
    DPI_LOG(DPI_LOG_TRACE, "[FTP_UPDATE] Copied filename: %s", src_session->storpath);
  } else if (!copy_filename) {
    DPI_LOG(DPI_LOG_TRACE, "[FTP_UPDATE] Skipped filename copy (copy_filename=false)");
  }

  if (strlen(dst_sesseion->filepath) == 0) {
    memcpy(dst_sesseion->filepath, src_session->filepath, sizeof(dst_sesseion->filepath));
  }
  memcpy(dst_sesseion->reqArg, src_session->reqArg, sizeof(dst_sesseion->reqArg));
  memcpy(dst_sesseion->reqCmd, src_session->reqCmd, sizeof(dst_sesseion->reqCmd));
  memcpy(dst_sesseion->resArg, src_session->resArg, sizeof(dst_sesseion->resArg));
  memcpy(dst_sesseion->resCode, src_session->resCode, sizeof(dst_sesseion->resCode));
  memcpy(&dst_sesseion->control_conv_tuple, &src_session->control_conv_tuple, sizeof(src_session->control_conv_tuple));
  memcpy(dst_sesseion->proto_filename, src_session->proto_filename, sizeof(dst_sesseion->proto_filename));
  memcpy(dst_sesseion->login_status, src_session->login_status, sizeof(dst_sesseion->login_status));
  return 1;
}

static int ftp_update_conversion_session_data(struct ftp_session *dst_sesseion, struct ftp_session *src_session) {
  return ftp_update_conversion_session_data_ex(dst_sesseion, src_session, true);
}

static int ftp_find_conv_update_conversion_session_data(struct ftp_session *session) {
  if (!session->control_conv_tuple.proto) {
    return 0;
  }
  struct conversation_value *conv = find_conversation(&session->control_conv_tuple, 0);
  if (conv) {
    conv->createtime = g_config.g_now_time;
    struct ftp_session *control_session = conv->conv_session;
    ftp_update_conversion_session_data(control_session, session);
  }
  return 1;
}

// 统一的文件名插入函数
static bool insert_filename_to_port_mapping(
    struct ftp_session *session, const char *filename, const char *trans_cmd, const char *context, bool allow_duplicates) {
  DPI_LOG(DPI_LOG_TRACE, "[%s] Searching for empty port slot to insert filename: %s (allow_duplicates=%s)", context, filename,
      allow_duplicates ? "true" : "false");

  // 对于LIST/NLST/MLSD命令，允许重复的file.path_list
  // 对于RETR/STOR命令，检查文件名是否已经存在
  bool filename_already_exists = false;
  if (!allow_duplicates) {
    for (int j = 0; j < MAX_FTP_PORT_FILENAME_NUM; j++) {
      if (session->proto_filename[j].dataPort != 0 && strcmp(session->proto_filename[j].storpath, filename) == 0) {
        DPI_LOG(DPI_LOG_TRACE, "[%s] *** FILENAME ALREADY EXISTS *** slot=%d, port=%d, filename=%s", context, j,
            session->proto_filename[j].dataPort, filename);
        filename_already_exists = true;
        break;
      }
    }
  }

  bool filename_inserted = false;
  if (!filename_already_exists) {
    // 修改查找策略：按照时间顺序查找最新的空端口槽位
    // 从前往后查找，确保使用最新分配的端口
    for (int i = 0; i < MAX_FTP_PORT_FILENAME_NUM; i++) {
      if (session->proto_filename[i].dataPort != 0 && session->proto_filename[i].storpath[0] == '\0') {
        strncpy(session->proto_filename[i].storpath, filename, sizeof(session->proto_filename[i].storpath));
        strncpy(session->proto_filename[i].trans_cmd, trans_cmd, sizeof(session->proto_filename[i].trans_cmd));
        DPI_LOG(DPI_LOG_TRACE, "[%s] *** FILENAME INSERTED *** slot=%d, port=%d -> filename=%s", context, i,
            session->proto_filename[i].dataPort, filename);
        filename_inserted = true;
        break;
      }
    }

    // 如果没有找到空槽位，检查是否有端口存在但文件名不同的情况，进行更新
    if (!filename_inserted) {
      for (int i = 0; i < MAX_FTP_PORT_FILENAME_NUM; i++) {
        if (session->proto_filename[i].dataPort != 0 && session->proto_filename[i].storpath[0] != '\0' &&
            strcmp(session->proto_filename[i].storpath, filename) != 0) {
          DPI_LOG(DPI_LOG_TRACE, "[%s] *** FILENAME UPDATED *** slot=%d, port=%d: '%s' -> '%s'", context, i,
              session->proto_filename[i].dataPort, session->proto_filename[i].storpath, filename);
          strncpy(session->proto_filename[i].storpath, filename, sizeof(session->proto_filename[i].storpath));
          strncpy(session->proto_filename[i].trans_cmd, trans_cmd, sizeof(session->proto_filename[i].trans_cmd));
          filename_inserted = true;
          break;
        }
      }
    }

    if (!filename_inserted) {
      DPI_LOG(DPI_LOG_WARNING, "[%s] *** WARNING *** No empty port slot found for filename: %s", context, filename);
    }
  } else {
    filename_inserted = true;  // 已存在，视为已插入
    DPI_LOG(DPI_LOG_TRACE, "[%s] *** SKIPPED DUPLICATE *** filename: %s", context, filename);
  }
  // 继续完文件插入后马上将文件列表更新到control_conv中
  ftp_find_conv_update_conversion_session_data(session);
  return filename_inserted;
}

int decode_IOS8859_to_uft8(char *inbuf, int inlen, char *outbuf, int outlen) {
  iconv_t cd;
  char   *ptr_in = inbuf;
  char   *ptr_out = outbuf;

  size_t local_inlen = inlen;
  size_t local_outlen = outlen;
  if (isUTF8(inbuf, inlen)) {
    return -1;
  }
  cd = iconv_open("utf-8", "GBK");  // gb2312
  if (0 == cd) {
    // printf("iconv open failed!\n");
    return -1;
  }

  if (-1 == (int)iconv(cd, (char **)&ptr_in, (size_t *)&local_inlen, (char **)&ptr_out, (size_t *)&local_outlen)) {
    // printf("iconv failed !, errno %d\n", errno);
    snprintf((char *)outbuf, (unsigned int)outlen, "%s", inbuf);
    iconv_close(cd);
    return -1;
  }

  // 如果最后存在非字符，类似与 \015 \012 \r \n等，不要生成到decode_str中
  int result_len = ptr_out - outbuf;

  // 从末尾开始移除所有非打印字符和控制字符
  while (result_len > 0) {
    char last_char = outbuf[result_len - 1];
    // 检查是否为控制字符：\r(0x0D/\015), \n(0x0A/\012), 以及其他控制字符
    if (last_char == '\r' || last_char == '\n' || last_char == '\015' || last_char == '\012' ||
        (last_char >= 0 && last_char < 32) || last_char == 127) {
      result_len--;
    } else {
      break;
    }
  }

  outbuf[result_len] = '\0';

  iconv_close(cd);
  return 0;
}
struct ftp_info {
  char     software[64];
  char     hostName[64];
  char     username[COMMON_NAME_PASSWD_LEN];
  char     password[COMMON_NAME_PASSWD_LEN];
  char     filename[COMMON_FILE_PATH];  // 文件名
  uint64_t filesize;

  uint16_t ctl_srcport;  // 源端口
  uint16_t ctl_dstport;  // 目的端口
  uint8_t  is_request;
  char     cmd_or_code[COMMON_FILE_PATH];
  char     args[COMMON_FILE_PATH];
  char     met[COMMON_FILE_PATH];          // 发送的ftp命令
  char     met_contend[COMMON_FILE_PATH];  // ftp命令参数
  char     ret_code[8];                    // 服务器响应码
  char     ret_con[COMMON_FILE_PATH];      // 返回消息内容
  int      offset;                         // 文件传输起始偏移位置
  char     con_type[8];                    // 传输文件类型
  char     login_status[COMMON_STATUS_LEN];
  char     server_ip[64];
};

enum ftp_index_em {
  EM_FTP_TYPE,
  EM_FTP_USERNAME,
  EM_FTP_PASSWORD,
  EM_FTP_FILENAME,
  EM_FTP_CONTROL_SRCPORT,
  EM_FTP_CONTROL_DSTPORT,
  EM_FTP_USER_FILE_PATH,
  EM_FTP_CONTROL_CMD,
  EM_FTP_CONTROL_ARGS,
  EM_FTP_SERVER_NAME,
  EM_FTP_SERVER_IP,
  EM_FTP_DATA_FILETYPE,
  EM_FTP_DATA_FILEPATH,
  EM_FTP_MET,
  EM_FTP_MET_CONTEND,
  EM_FTP_RET_CODE,
  EM_FTP_RET_CON,
  EM_FTP_OFFSET,
  EM_FTP_CON_TYPE,
  EM_FTP_DATA_PORT,
  EM_FTP_FILE_SIZE,
  EM_FTP_DATA_IP,
  EM_FTP_MODE,
  EM_FTP_DATA_TOTAL_LEN,  // for test
  EM_FTP_DATA_REAL_LEN,   // for test
  EM_FTP_LOGIN_STATUS,
  EM_FTP_DATA_HOSTNAME,
  EM_FTP_DATA_SOFTWARE,
#ifdef DPI_SDT_YNAO
  EM_FTP_DATA_LOCALFILENAME,
  EM_FTP_DATA_LOCALFILEPATH,
  EM_FTP_DATA_FILE_CONTENT,
  EM_FTP_DATA_OPERATIONS,
#endif
  EM_FTP_MAX
};

static dpi_field_table ftp_field_array[] = {
    DPI_FIELD_D(EM_FTP_TYPE, YA_FT_UINT16, "Ftp_type"),
    DPI_FIELD_D(EM_FTP_USERNAME, YA_FT_STRING, "Username"),
    DPI_FIELD_D(EM_FTP_PASSWORD, YA_FT_STRING, "Password"),
    DPI_FIELD_D(EM_FTP_FILENAME, YA_FT_STRING, "Filename"),
    DPI_FIELD_D(EM_FTP_CONTROL_SRCPORT, YA_FT_UINT16, "Control_srcport"),
    DPI_FIELD_D(EM_FTP_CONTROL_DSTPORT, YA_FT_UINT16, "Control_dstport"),
    DPI_FIELD_D(EM_FTP_USER_FILE_PATH, YA_FT_STRING, "User_filepath"),
    DPI_FIELD_D(EM_FTP_CONTROL_CMD, YA_FT_STRING, "Control_cmd"),
    DPI_FIELD_D(EM_FTP_CONTROL_ARGS, YA_FT_STRING, "Control_args"),
    DPI_FIELD_D(EM_FTP_SERVER_NAME, YA_FT_STRING, "Server_name"),
    DPI_FIELD_D(EM_FTP_SERVER_IP, YA_FT_STRING, "Server_IP"),
    DPI_FIELD_D(EM_FTP_DATA_FILETYPE, YA_FT_STRING, "Data_filetype"),
    DPI_FIELD_D(EM_FTP_DATA_FILEPATH, YA_FT_STRING, "Data_filepath"),
    DPI_FIELD_D(EM_FTP_MET, YA_FT_STRING, "Method"),
    DPI_FIELD_D(EM_FTP_MET_CONTEND, YA_FT_STRING, "Method_content"),
    DPI_FIELD_D(EM_FTP_RET_CODE, YA_FT_INT32, "Return_code"),
    DPI_FIELD_D(EM_FTP_RET_CON, YA_FT_STRING, "Return_content"),
    DPI_FIELD_D(EM_FTP_OFFSET, YA_FT_STRING, "Offset"),
    DPI_FIELD_D(EM_FTP_CON_TYPE, YA_FT_STRING, "Content_type"),
    DPI_FIELD_D(EM_FTP_DATA_PORT, YA_FT_UINT32, "DataPort"),
    DPI_FIELD_D(EM_FTP_FILE_SIZE, YA_FT_UINT64, "FileSize"),
    DPI_FIELD_D(EM_FTP_DATA_IP, YA_FT_UINT32, "DataIp"),
    DPI_FIELD_D(EM_FTP_MODE, YA_FT_UINT8, "Mode"),
    DPI_FIELD_D(EM_FTP_DATA_TOTAL_LEN, YA_FT_STRING, "Data_totallen"),
    DPI_FIELD_D(EM_FTP_DATA_REAL_LEN, YA_FT_UINT32, "Data_reallen"),
    DPI_FIELD_D(EM_FTP_LOGIN_STATUS, YA_FT_UINT8, "LoginStatus"),
    DPI_FIELD_D(EM_FTP_DATA_HOSTNAME, YA_FT_STRING, "hostName"),
    DPI_FIELD_D(EM_FTP_DATA_SOFTWARE, YA_FT_STRING, "software"),
#ifdef DPI_SDT_YNAO
    DPI_FIELD_D(EM_FTP_DATA_LOCALFILENAME, YA_FT_STRING, "localFilename"),
    DPI_FIELD_D(EM_FTP_DATA_LOCALFILEPATH, YA_FT_STRING, "localFilepath"),
    DPI_FIELD_D(EM_FTP_DATA_FILE_CONTENT, YA_FT_BYTES_MAYBE, "ftp_file_content"),
    DPI_FIELD_D(EM_FTP_DATA_OPERATIONS, YA_FT_STRING, "ftp_operations"),
#endif
};

/*ftp control的识别函数，主要基于端口识别，增加一些过滤判断*/
static int identify_ftp_control(struct flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len) {
  int line_len;
  int search_len = 0;

  if (g_config.protocol_switch[PROTOCOL_FTP_CONTROL] == 0)
    return 0;

  if (payload_len > 1460) {
    return 0;
  }

  search_len = FTP_MAX_SEARCH > payload_len ? payload_len : FTP_MAX_SEARCH;

  /* Exclude SMTP, which uses similar commands. */
  if (flow->tuple.inner.port_src == htons(25) || flow->tuple.inner.port_dst == htons(25)) {
    DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_FTP_CONTROL);
    return 0;
  }

  if (flow->pkt_first_line.has_search == 0) {
    line_len = find_packet_line_end(payload, search_len);
    flow->pkt_first_line.has_search = 1;
    flow->pkt_first_line.linelen = line_len;
  } else {
    line_len = flow->pkt_first_line.linelen;
  }

  if (line_len <= 0)
    return 0;

  if (likely(0 == g_config.ftp_identify_flag)) {
    if (flow->tuple.inner.port_src == htons(g_config.ftp_port) || flow->tuple.inner.port_dst == htons(g_config.ftp_port)) {
      flow->real_protocol_id = PROTOCOL_FTP_CONTROL;
    }
  }

  return PROTOCOL_FTP_CONTROL;
}

static uint8_t isvalid_rfc2428_delimiter(const char c) {
  /* RFC2428 sect. 2 states rules for a valid delimiter */
  const char *forbidden = "0123456789abcdef.:";
  if (!isgraph(c))
    return 0;
  if (strchr(forbidden, tolower(c)))
    return 0;
  return 1;
}

/*
 *此函数用于ftp-data会话的识别，port请求和pasv响应中会有ip和端口号，目前ftp-data的识别可能不需要
 */
static int parse_port_pasv(const uint8_t *line, int linelen, uint32_t *ftp_ip, uint16_t *ftp_port) {
  char    args[1024];
  char   *p;
  char    c;
  int     i;
  int     ip_address[4], port[2];
  uint8_t ret = 0;

  size_t copy_len = (uint32_t)linelen >= sizeof(args) ? (sizeof(args) - 1) : (uint32_t)linelen;
  strncpy(args, (const char *)line, copy_len);
  args[copy_len] = 0;

  p = args;

  for (;;) {
    /*
     * Look for a digit.
     */
    while ((c = *p) != '\0' && !isdigit(c)) p++;

    if (*p == '\0') {
      /*
       * We ran out of text without finding anything.
       */
      break;
    }

    /*
     * See if we have six numbers.
     */
    i = sscanf(p, "%d,%d,%d,%d,%d,%d", &ip_address[0], &ip_address[1], &ip_address[2], &ip_address[3], &port[0], &port[1]);
    if (i == 6) {
      /*
       * We have a winner!
       */
      *ftp_port = ((port[0] & 0xFF) << 8) | (port[1] & 0xFF);
      *ftp_ip = htonl((ip_address[0] << 24) | (ip_address[1] << 16) | (ip_address[2] << 8) | ip_address[3]);
      ret = 1;
      break;
    }

    /*
     * Well, that didn't work.  Skip the first number we found,
     * and keep trying.
     */
    while ((c = *p) != '\0' && isdigit(c)) p++;
  }

  return ret;
}

/*eprt命令的解析，和port，pasv类似*/
static uint8_t parse_eprt_request(
    const uint8_t *line, int linelen, uint32_t *eprt_af, uint32_t *eprt_ip, uint16_t *eprt_ipv6, uint16_t *ftp_port) {
  int     delimiters_seen = 0;
  char    delimiter;
  int     fieldlen;
  char   *field;
  int     n;
  int     lastn;
  char   *p;
  char    args[1024];
  uint8_t ret = 0;
  size_t  copy_len;

  /* line contains the EPRT parameters, we need at least the 4 delimiters */
  if (!line || linelen < 4)
    return 0;

  copy_len = (uint32_t)linelen >= sizeof(args) ? (sizeof(args) - 1) : (uint32_t)linelen;
  strncpy(args, (const char *)line, copy_len);
  args[copy_len] = 0;

  p = args;
  /*
   * Handle a NUL being in the line; if there's a NUL in the line,
   * strlen(args) will terminate at the NUL and will thus return
   * a value less than linelen.
   */
  if (strlen(args) < (unsigned)linelen)
    linelen = strlen(args);

  /*
   * RFC2428 sect. 2 states ...
   *
   *     The EPRT command keyword MUST be followed by a single space (ASCII
   *     32). Following the space, a delimiter character (<d>) MUST be
   *     specified.
   *
   * ... the preceding <space> is already stripped so we know that the first
   * character must be the delimiter and has just to be checked to be valid.
   */
  if (!isvalid_rfc2428_delimiter(*p))
    return 0; /* EPRT command does not follow a vaild delimiter;
               * malformed EPRT command - immediate escape */

  delimiter = *p;
  /* Validate that the delimiter occurs 4 times in the string */
  for (n = 0; n < linelen; n++) {
    if (*(p + n) == delimiter)
      delimiters_seen++;
  }

  if (delimiters_seen != 4)
    return 0; /* delimiter doesn't occur 4 times
               * probably no EPRT request - immediate escape */

  /* we know that the first character is a delimiter... */
  delimiters_seen = 1;
  lastn = 0;
  /* ... so we can start searching from the 2nd onwards */
  for (n = 1; n < linelen; n++) {
    if (*(p + n) != delimiter)
      continue;

    /* we found a delimiter */
    delimiters_seen++;

    fieldlen = n - lastn - 1;
    if (fieldlen <= 0)
      return 0; /* all fields must have data in them */
    field = p + lastn + 1;

    if (delimiters_seen == 2) { /* end of address family field */
      char af_str[64] = {0};
      strncpy(af_str, field, (uint32_t)fieldlen >= sizeof(af_str) ? (sizeof(af_str) - 1) : (uint32_t)fieldlen);
      *eprt_af = atoi(af_str);
    } else if (delimiters_seen == 3) { /* end of IP address field */
      char ip_str[64] = {0};
      strncpy(ip_str, field, (uint32_t)fieldlen >= sizeof(ip_str) ? (sizeof(ip_str) - 1) : (uint32_t)fieldlen);

      if (*eprt_af == EPRT_AF_IPv4) {
        if (inet_pton(AF_INET, ip_str, eprt_ip))
          ret = 1;
        else
          ret = 0;
      } else if (*eprt_af == EPRT_AF_IPv6) {
        if (inet_pton(AF_INET6, ip_str, eprt_ipv6))
          ret = 1;
        else
          ret = 0;
      } else
        return 0; /* invalid/unknown address family */

    } else if (delimiters_seen == 4) { /* end of port field */
      char pt_str[64] = {0};
      strncpy(pt_str, field, (uint32_t)fieldlen >= sizeof(pt_str) ? (sizeof(pt_str) - 1) : (uint32_t)fieldlen);
      *ftp_port = (uint16_t)atoi(pt_str);
    }
    lastn = n;
  }

  return ret;
}

/*epasv命令的解析，和port，pasv类似*/
static uint8_t parse_extended_pasv_response(const uint8_t *line, int linelen, uint16_t *ftp_port) {
  int     n;
  char    args[1024];
  char   *p;
  char    c;
  uint8_t ret = 0;
  uint8_t delimiters_seen = 0;
  size_t  copy_len;
  /*
   * Copy the rest of the line into a null-terminated buffer.
   */
  copy_len = (uint32_t)linelen >= sizeof(args) ? (sizeof(args) - 1) : (uint32_t)linelen;
  strncpy(args, (const char *)line, copy_len);
  args[copy_len] = 0;

  p = args;

  /*
   * Look for ( <d> <d> <d>
     (Try to cope with '(' in description)
   */
  for (; !delimiters_seen;) {
    char delimiter = '\0';
    while ((c = *p) != '\0' && (c != '(')) p++;

    if (*p == '\0') {
      return 0;
    }

    /* Skip '(' */
    p++;

    /* Make sure same delimiter is used 3 times */
    for (n = 0; n < 3; n++) {
      if ((c = *p) != '\0') {
        if (delimiter == '\0' && isvalid_rfc2428_delimiter(c)) {
          delimiter = c;
        }
        if (c != delimiter) {
          break;
        }
        p++;
      } else {
        break;
      }
    }
    delimiters_seen = 1;
  }

  /*
   * Should now be at digits.
   */
  if (*p != '\0') {
    int port_valid = atoi(p);

    if (port_valid > 0)
      *ftp_port = (uint16_t)port_valid;
    else
      ret = 0;
  }

  return ret;
}

#define KAFKA_FIELD_MAX 1024
int ftp_field_to_record(int direction, struct ftp_session *session, precord_t *record) {
  int          i = 0;
  uint8_t      field_ptr[KAFKA_FIELD_MAX];  // 字段数据
  ya_fvalue_t *value;

  value = ya_fvalue_new_uinteger(YA_FT_UINT8, session->mode);
  precord_fvalue_put(record, "Mode", value);

  if (session->filepath[0] != '\0') {
    if (session->total_len) {
      value = ya_fvalue_new_uinteger(YA_FT_UINT32, session->total_len);
      precord_fvalue_put(record, "Data_totallen", value);
    }
    if (session->real_len) {
      value = ya_fvalue_new_uinteger(YA_FT_UINT32, session->real_len);
      precord_fvalue_put(record, "Data_reallen", value);
    }
    // 存盘文件路径
    FILE *fp = fopen(session->filepath, "r");
    if (fp) {
      uint8_t   con[129] = {0};
      size_t len = fread(con, 128, 1, fp);

      dpi_fvalue_new_string_put(record, YA_FT_STRING, "localFilename", session->filepath + g_config.tbl_out_dir_len,
          strlen(session->filepath + g_config.tbl_out_dir_len));
      dpi_fvalue_new_string_put(record, YA_FT_STRING, "localFilepath", session->filepath, strlen(session->filepath));
      value = ya_fvalue_new_bytes(YA_FT_BYTES_MAYBE, con, strlen((const char *)con));
      precord_fvalue_put(record, "ftp_file_content", value);

      // 存盘文件名
      if (strlen(session->filepath)) {
        int path_len = 0;
        for (path_len = strlen(session->filepath); path_len > 0; path_len--) {
          if (session->filepath[path_len] == '/')
            break;
        }
        path_len++;
        dpi_fvalue_new_string_put(
            record, YA_FT_STRING, "Data_filepath", session->filepath + path_len, strlen(session->filepath) - path_len);
      }
    }
  }

  dpi_fvalue_new_string_put(record, YA_FT_STRING, "ftp_operations", session->trans_cmd,
      (strlen(session->trans_cmd)) > 1 ? (strlen(session->trans_cmd)) : 0);
  dpi_fvalue_new_string_put(
      record, YA_FT_STRING, "Control_cmd", session->reqCmd, (strlen(session->reqCmd)) > 1 ? (strlen(session->reqCmd) - 1) : 0);

  dpi_fvalue_new_string_put(
      record, YA_FT_STRING, "Control_args", session->reqArg, (strlen(session->reqArg)) > 1 ? (strlen(session->reqArg) - 1) : 0);

  dpi_fvalue_new_string_put(
      record, YA_FT_STRING, "Return_code", session->resCode, (strlen(session->resCode)) > 1 ? (strlen(session->resCode) - 1) : 0);
  dpi_fvalue_new_string_put(
      record, YA_FT_STRING, "Return_content", session->resArg, (strlen(session->resArg)) > 1 ? (strlen(session->resArg) - 1) : 0);

  dpi_fvalue_new_string_put(record, YA_FT_STRING, "Username", session->username, strlen(session->username));

  dpi_fvalue_new_string_put(record, YA_FT_STRING, "Password", session->password, strlen(session->password));

  dpi_fvalue_new_string_put(record, YA_FT_STRING, "Filename", session->storpath, strlen(session->storpath));
  dpi_fvalue_new_string_put(record, YA_FT_STRING, "Data_filetype", session->filetype, strlen(session->filetype));

  dpi_fvalue_new_string_put(record, YA_FT_STRING, "Content_type", session->con_type, strlen(session->con_type));
  dpi_fvalue_new_string_put(record, YA_FT_STRING, "hostName", session->hostName, strlen(session->hostName));
  dpi_fvalue_new_string_put(record, YA_FT_STRING, "software", session->software, strlen(session->software));

  char ip_str[256] = {0};
  if (4 == session->control_ipVer) {
    get_ip4string(
        ip_str, sizeof(ip_str), direction ? session->control_conv_tuple.ip_dst.ip4 : session->control_conv_tuple.ip_src.ip4);
  } else if (6 == session->control_ipVer) {
    get_ip6string(
        ip_str, sizeof(ip_str), direction ? session->control_conv_tuple.ip_dst.ip6 : session->control_conv_tuple.ip_src.ip6);
  }
  dpi_fvalue_new_string_put(record, YA_FT_STRING, "Server_IP", ip_str, strlen(ip_str));
  if (session->ftp_data_ip.ip4) {
    if (4 == session->control_ipVer) {
      get_ip4string(ip_str, sizeof(ip_str), session->ftp_data_ip.ip4);
    } else if (6 == session->control_ipVer) {
      get_ip6string(ip_str, sizeof(ip_str), session->ftp_data_ip.ip6);
    }
    dpi_fvalue_new_string_put(record, YA_FT_STRING, "DataIp", ip_str, strlen(ip_str));
  }

  if (session->dataPort) {
    value = ya_fvalue_new_uinteger(YA_FT_UINT8, session->dataPort);
    precord_fvalue_put(record, "DataPort", value);
  }

  if (!memcmp(session->login_status, "YES", 3)) {
    value = ya_fvalue_new_uinteger(YA_FT_UINT8, 1);
  } else {
    value = ya_fvalue_new_uinteger(YA_FT_UINT8, 0);
  }
  precord_fvalue_put(record, "LoginStatus", value);

  value = ya_fvalue_new_uinteger(YA_FT_UINT8, session->mode);
  precord_fvalue_put(record, "Mode", value);

  return 0;
}

void write_ftp_conversation_log(const struct conversation_tuple *tuple, void *session) {
  char timeout_src_ip[64] = {0};
  char timeout_dst_ip[64] = {0};
  get_ip4string(timeout_src_ip, 64, tuple->ip_src.ip4);
  get_ip4string(timeout_dst_ip, 64, tuple->ip_dst.ip4);
  DPI_LOG(DPI_LOG_TRACE, "[FTP_CONV_TIMEOUT] Writing ftp_control_conversation_log for tuple: %s:%d -> %s:%d (proto=%d)",
      timeout_src_ip, ntohs(tuple->port_src), timeout_dst_ip, ntohs(tuple->port_dst), tuple->proto);

  struct tbl_log     *log_ptr;
  struct ftp_session *ftp_session = session;
  const char         *postfix;

  // 1.如果存在session->filepath,查找session->control_tuple中的文件后置是否是相同的，如果是，则更新record写日志，如果不相同，则修改文件后缀，再写日志
  if (strlen(ftp_session->filepath) > 0) {
    DPI_LOG(DPI_LOG_TRACE, "[FTP_CONV_TIMEOUT] Session has filepath: %s", ftp_session->filepath);

    // 查找control conversation中的文件后缀
    if (ftp_session->control_conv_tuple.proto) {
      struct conversation_value *control_conv = find_conversation(&ftp_session->control_conv_tuple, 0);
      if (control_conv && control_conv->conv_session) {
        control_conv->createtime = g_config.g_now_time;
        struct ftp_session *control_session = control_conv->conv_session;
        ftp_update_conversion_session_data(ftp_session, control_session);
        // 从control session的proto_filename表中查找对应的文件信息
        uint16_t data_port = (ntohs(tuple->port_src) == 20) ? ntohs(tuple->port_dst) : ntohs(tuple->port_src);
        bool     found_control_file = false;
        ftp_session->dataPort = data_port;
        for (int i = 0; i < MAX_FTP_PORT_FILENAME_NUM; i++) {
          if (control_session->proto_filename[i].dataPort == data_port) {
            if (strlen(control_session->proto_filename[i].storpath) > 0) {
              const char *control_postfix = strrchr(control_session->proto_filename[i].storpath, '.');
              const char *session_postfix = strrchr(ftp_session->filepath, '.');

              if (control_postfix && session_postfix) {
                // 比较文件后缀是否相同
                if (strcmp(control_postfix, session_postfix) == 0) {
                  DPI_LOG(DPI_LOG_TRACE, "[FTP_CONV_TIMEOUT] File extensions match: %s", control_postfix);
                  // 文件后缀相同，直接更新record写日志
                  strncpy(ftp_session->storpath, control_session->proto_filename[i].storpath, sizeof(ftp_session->storpath));
                } else {
                  DPI_LOG(DPI_LOG_TRACE, "[FTP_CONV_TIMEOUT] File extensions differ: session=%s, control=%s", session_postfix,
                      control_postfix);
                  // 文件后缀不同，修改文件后缀
                  char new_filepath[COMMON_FILE_PATH] = {0};
                  strncpy(new_filepath, ftp_session->filepath, session_postfix - ftp_session->filepath);
                  // 如果control_postfix中存在\字符，则只保留\前的部分
                  char *backslash_pos = strchr(control_postfix, 0x0d);
                  if (backslash_pos) {
                    // 只拷贝\前面的部分
                    int prefix_len = backslash_pos - control_postfix;
                    strncat(new_filepath, control_postfix, prefix_len);
                  } else {
                    strcat(new_filepath, control_postfix);
                  }

                  DPI_LOG(DPI_LOG_TRACE, "[FTP_CONV_TIMEOUT] Renaming file from %s to %s", ftp_session->filepath, new_filepath);
                  rename(ftp_session->filepath, new_filepath);
                  strncpy(ftp_session->filepath, new_filepath, sizeof(ftp_session->filepath));
                  strncpy(ftp_session->storpath, control_session->proto_filename[i].storpath, sizeof(ftp_session->storpath));
                  strncpy(ftp_session->filetype, control_postfix + 1, sizeof(ftp_session->filetype));
                  strncpy(ftp_session->trans_cmd, control_session->proto_filename[i].trans_cmd, sizeof(ftp_session->trans_cmd));
                }
                found_control_file = true;
                break;
              }
            }
          }
        }

        if (!found_control_file) {
          DPI_LOG(DPI_LOG_TRACE, "[FTP_CONV_TIMEOUT] No matching file found in control session for port %d", data_port);
        }
      }
    }

  } else {
    // 2.如果不存在session->filepath，则认为自己是control的conv，清空自己，不输出
    DPI_LOG(DPI_LOG_TRACE, "[FTP_CONV_TIMEOUT] No filepath found, this is a control conversation, clearing and not outputting");

    // 清空session
    if (ftp_session->record) {
      precord_destroy(ftp_session->record);
      ftp_session->record = NULL;
    }

    // 不输出日志，直接返回
    return;
  }

  if (!ftp_session->record) {
    DPI_LOG(DPI_LOG_TRACE, "[FTP_CONV_TIMEOUT] No record found, skipping log output");
    if (ftp_session->filepath[0] != '\0') {
      DPI_LOG(DPI_LOG_TRACE, "[FTP_CONV_TIMEOUT] No record found, delete file [%s]", ftp_session->filepath);

      remove(ftp_session->filepath);
    }
    return;
  }
  if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
    DPI_LOG(DPI_LOG_WARNING, "not enough memory");
    return;
  }
  init_log_ptr_data(log_ptr, ftp_session->flow, EM_TBL_LOG_ID_BY_DEFAULT);

  // 在conversation超时时，输出data流的log而不是control流的log
  DPI_LOG(DPI_LOG_TRACE, "[FTP_CONV_TIMEOUT] Writing data flow log with filename: %s", ftp_session->storpath);
  ftp_field_to_record(0, ftp_session, ftp_session->record);

  log_ptr->record = ftp_session->record;
  log_ptr->log_type = PROTOCOL_FTP_CONTROL;  // 保持原有的log类型
  dpi_flow_free(ftp_session->flow, dpi_flow_timeout_free);

  if (write_tbl_log(log_ptr) != 1) {
    if (ftp_session->record) {
      precord_destroy(ftp_session->record);
      ftp_session->record = NULL;
    }
    rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    return;
  }

  ftp_session->record = NULL;
  return;
}

/*
 *ftp的tbl日志函数
 */
static void write_ftp_control_log(struct flow_info *flow, int direction, struct ftp_session *session) {
  int             idx = 0;
  struct tbl_log *log_ptr;
  // char __str[64]={0};
  // struct ftp_session *session = (struct ftp_session *)flow->app_session;
  if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
    DPI_LOG(DPI_LOG_WARNING, "not enough memory");
    return;
  }
  init_log_ptr_data(log_ptr, flow, EM_TBL_LOG_ID_BY_DEFAULT);
  char src_ip[64] = {0};
  char dst_ip[64] = {0};
  get_ipstring(flow->tuple.inner.ip_version, flow->tuple.inner.ip_src, src_ip, 64);
  get_ipstring(flow->tuple.inner.ip_version, flow->tuple.inner.ip_dst, dst_ip, 64);

  DPI_LOG(DPI_LOG_TRACE, "[FTP_CONTROL_LOG] creat record Flow Ftp Tuple: %s:%d -> %s:%d (proto=%d)", src_ip,
      ntohs(flow->tuple.inner.port_src), dst_ip, ntohs(flow->tuple.inner.port_dst), flow->tuple.inner.proto);
  dpi_precord_new_record(log_ptr->record, NULL, NULL);
  write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
  player_t *layer = precord_layer_put_new_layer(log_ptr->record, "ftp");

  // 在超时时尝试获取已经写过的文件路径

  ftp_field_to_record(flow->direction, session, log_ptr->record);
  log_ptr->log_type = PROTOCOL_FTP_CONTROL;
  if (write_tbl_log(log_ptr) != 1) {
    sdt_precord_destroy(log_ptr->record);
    session->record = NULL;
    rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    return;
  }
  session->record = NULL;
  return;
}

/**
 * @brief 销毁session中的record
 *
 * @param flow
 * @param session
 * @return int
 */
static int ftp_destorysession_record(struct ftp_session *session) {
  if (session->record)
    precord_destroy(session->record);
  session->record = NULL;
  return 0;
}

/**
 * @brief 创建session中的record函数
 *        在创建conversion前执行，执行时会将此record拷贝到conversion->conv_session中，所以需要在每次创建conversion时创建新的record
 *        需要在session结束时销毁，才能再下一个新的conversion到来时创建新record
 * @param flow
 * @param session
 * @return int
 */
static int ftp_creat_session_record(struct flow_info *flow, struct ftp_session *session) {
  if (session->record == NULL) {
    char src_ip[64] = {0};
    char dst_ip[64] = {0};
    get_ipstring(flow->tuple.inner.ip_version, flow->tuple.inner.ip_src, src_ip, 64);
    get_ipstring(flow->tuple.inner.ip_version, flow->tuple.inner.ip_dst, dst_ip, 64);

    DPI_LOG(DPI_LOG_TRACE, "[FTP_RECORD_CREATE] creat record Flow Ftp Tuple: %s:%d -> %s:%d (proto=%d)", src_ip,
        ntohs(flow->tuple.inner.port_src), dst_ip, ntohs(flow->tuple.inner.port_dst), flow->tuple.inner.proto);
    dpi_precord_new_record(session->record, NULL, NULL);
    struct tbl_log log_ptr;  // 将record保存在临时tbl_log中，用于写公共字段
    int            idx = 0;
    log_ptr.record = session->record;
    write_tbl_log_common(flow, 0, &log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);
    player_t *layer = precord_layer_put_new_layer(session->record, "ftp");
    session->flow = dpi_flow_clone(flow);
  }
  return 0;
}

/**
 * @brief 查找创建ftp_data的conversion
 *
 * @param flow
 * @param session
 * @param tuple
 * @return int
 */
static struct conversation_value *ftp_find_or_create_conversation(
    struct flow_info *flow, struct ftp_session *session, struct conversation_tuple *tuple) {
  uint8_t option = 0;
  // 创建session的record
  if (tuple->port_dst == 0 || tuple->port_src == 0) {
    option = NO_PORT_B;
  }
  struct conversation_value *conv = NULL;
  // 已经创建过! 可能是data流先建立conv
  if ((conv = find_conversation(tuple, option)) != NULL) {
    conv->createtime = g_config.g_now_time;
    DPI_LOG(DPI_LOG_TRACE, "[FTP_CONV] Found existing conversation");
    return conv;
  }

  DPI_LOG(DPI_LOG_TRACE, "[FTP_CONV] Creating new conversation");
  // 在创建conv时，会将此条流的session拷贝进入conv的flow的app_session中
  conv = find_or_create_conversation(tuple, option, PROTOCOL_FTP_DATA, session);

  if (conv) {
    DPI_LOG(DPI_LOG_TRACE, "[FTP_CONV] New conversation created successfully");
  } else {
    DPI_LOG(DPI_LOG_WARNING, "[FTP_CONV] Failed to create new conversation");
  }

  return conv;  // 返回新创建的conversation，而不是NULL
}

static int init_ftp_conv_session(struct flow_info *flow, struct ftp_session *session) {
  session->creatime = time(NULL);
  if (session->control_conv_tuple.proto == 0) {
    if (ntohs(flow->tuple.inner.port_dst) == htons(g_config.ftp_port)) {
      memcpy(session->control_conv_tuple.ip_dst.ip6, flow->tuple.inner.ip_dst, sizeof(session->control_conv_tuple.ip_dst));
      memcpy(session->control_conv_tuple.ip_src.ip6, flow->tuple.inner.ip_src, sizeof(session->control_conv_tuple.ip_dst));
      session->control_conv_tuple.port_dst = flow->tuple.inner.port_dst;
      session->control_conv_tuple.port_src = flow->tuple.inner.port_src;
    } else {
      session->control_ipVer = flow->tuple.inner.ip_version;
      memcpy(session->control_conv_tuple.ip_src.ip6, flow->tuple.inner.ip_dst, sizeof(session->control_conv_tuple.ip_src));
      memcpy(session->control_conv_tuple.ip_dst.ip6, flow->tuple.inner.ip_src, sizeof(session->control_conv_tuple.ip_src));
      session->control_conv_tuple.port_src = flow->tuple.inner.port_dst;
      session->control_conv_tuple.port_dst = flow->tuple.inner.port_src;
    }
    session->control_conv_tuple.proto = IPPROTO_TCP;

    // 添加control conversation建立的调试信息
    char ctrl_src_ip[64] = {0};
    char ctrl_dst_ip[64] = {0};
    get_ip4string(ctrl_src_ip, 64, session->control_conv_tuple.ip_src.ip4);
    get_ip4string(ctrl_dst_ip, 64, session->control_conv_tuple.ip_dst.ip4);
    DPI_LOG(DPI_LOG_TRACE, "[FTP_CONTROL] Flow %p Create control conversation: %s:%d -> %s:%d (proto=%d)", flow, ctrl_src_ip,
        ntohs(session->control_conv_tuple.port_src), ctrl_dst_ip, ntohs(session->control_conv_tuple.port_dst),
        session->control_conv_tuple.proto);

    // 只建立conv，不创建record
    struct conversation_value *conv = find_or_create_conversation(&session->control_conv_tuple, 0, PROTOCOL_FTP_DATA, session);
  }
  return 0;
}

/*
 *ftp-control的实时单包解析入口函数
 */
static int dissect_ftp_control(struct flow_info *flow, uint8_t direction, const uint8_t *payload, uint32_t payload_len) {
  // 通过该函数按包到达顺序先行建立ftp-data的conv建立
  uint16_t            ftp_data_port = 0;
  uint32_t            ftp_data_ip = 0;
  uint8_t             is_port_request = 0;
  uint8_t             is_eprt_request = 0;
  uint8_t             is_pasv_response = 0;
  uint8_t             is_epasv_response = 0;
  struct ftp_session *session;  // add by liugh
  const uint8_t      *line;
  char                code_str[4];
  int                 code;
  int                 linelen;
  uint32_t            tokenlen = 0;
  const uint8_t      *next_token;
  line = payload;
  if (flow->app_session == NULL) {
    flow->app_session = dpi_malloc(sizeof(struct ftp_session));
    if (flow->app_session == NULL) {
      DPI_LOG(DPI_LOG_WARNING, "malloc failed");
      return PKT_OK;
    }
    memset(flow->app_session, 0, sizeof(struct ftp_session));
    session = (struct ftp_session *)flow->app_session;
    init_ftp_conv_session(flow, session);
  }

  session = (struct ftp_session *)flow->app_session;
  linelen = find_packet_line_end(payload, payload_len);
  if (5376 == flow->tuple.inner.port_dst) {
    direction = FLOW_DIR_SRC2DST;
  } else {
    direction = FLOW_DIR_DST2SRC;
  }

  if (direction == FLOW_DIR_SRC2DST) {
    tokenlen = dpi_get_token_len(line, line + linelen, &next_token);
    if (tokenlen != 0) {
      if (strncmp((const char *)line, "PORT", tokenlen) == 0) {
        is_port_request = 1;
      } else if (strncmp((const char *)line, "EPRT", tokenlen) == 0) {
        is_eprt_request = 1;
      }
    }
    struct ftp_info info;
    size_t          copy_len = 0;

    copy_len = tokenlen >= sizeof(info.cmd_or_code) ? (sizeof(info.cmd_or_code) - 1) : tokenlen;
    strncpy(info.cmd_or_code, (const char *)line, copy_len);

    info.cmd_or_code[copy_len] = 0;
  } else {
    if (linelen > 3 && isdigit(line[0]) && isdigit(line[1]) && isdigit(line[2])) {
      memcpy(code_str, line, sizeof(code_str) - 1);
      code_str[sizeof(code_str) - 1] = 0;
      code = atoi(code_str);
      if (code == 227) {
        is_pasv_response = 1;
      } else if (code == 229) {
        is_epasv_response = 1;
      }
    }
  }

  if (is_port_request || is_pasv_response) {
    parse_port_pasv(line, linelen, &ftp_data_ip, &ftp_data_port);

    // 建立FTP data conversation，使用21端口方的IP和20端口，与PORT命令中的IP端口建立ftp_data的conv
    struct conversation_tuple data_tuple;
    memset(&data_tuple, 0, sizeof(data_tuple));
    data_tuple.proto = IPPROTO_TCP;

    if (is_port_request) {
      // PORT命令：客户端指定数据端口，服务端使用20端口连接
      memcpy(&data_tuple.ip_src, &flow->tuple.inner.ip_dst, sizeof(data_tuple.ip_src));  // 服务端IP
      data_tuple.port_src = htons(20);                                                   // 服务端使用20端口
      data_tuple.ip_dst.ip4 = ftp_data_ip;                                               // 客户端指定的IP
      data_tuple.port_dst = htons(ftp_data_port);                                        // 客户端指定的端口
    } else {
      // PASV响应：服务端指定数据端口，客户端连接
      data_tuple.ip_src.ip4 = ftp_data_ip;         // 服务端指定的IP
      data_tuple.port_src = htons(ftp_data_port);  // 服务端指定的端口
      if (direction == FLOW_DIR_SRC2DST)
        memcpy(&data_tuple.ip_dst, &flow->tuple.inner.ip_dst, sizeof(data_tuple.ip_dst));  // 客户端IP
      else
        memcpy(&data_tuple.ip_dst, &flow->tuple.inner.ip_src, sizeof(data_tuple.ip_dst));  // 客户端IP
      data_tuple.port_dst = 0;  // 客户端端口未知，使用NO_PORT_B选项
    }

    session->ftp_data_ip.ip4 = ftp_data_ip;
    session->ftp_data_ipVer = 4;

    // 一条ftp控制流中会出现多次port，在此处会更新conv的信息
    struct conversation_value *conv = ftp_find_or_create_conversation(flow, session, &data_tuple);

    char src_ip[64] = {0};
    char dst_ip[64] = {0};
    get_ip4string(src_ip, 64, data_tuple.ip_src.ip4);
    get_ip4string(dst_ip, 64, data_tuple.ip_dst.ip4);
    DPI_LOG(DPI_LOG_TRACE, "[FTP_CONTROL] Create data conversation: %s:%d -> %s:%d (mode: %s)", src_ip,
        ntohs(data_tuple.port_src), dst_ip, ntohs(data_tuple.port_dst), is_port_request ? "PORT" : "PASV");
    if (conv) {
      struct ftp_session *conv_s = conv->conv_session;
      // 在FTP_CONTROL中不拷贝文件名，等待FTP_CONTROL_RSM处理完成后再拷贝
      ftp_update_conversion_session_data_ex(conv_s, session, false);
      DPI_LOG(DPI_LOG_TRACE, "[FTP_CONTROL] Data conversation created successfully (filename not copied yet)");
    } else {
      DPI_LOG(DPI_LOG_WARNING, "[FTP_CONTROL] Failed to create data conversation");
    }

    session->file_num++;
    session->dataPort = ftp_data_port;
  }

  if (is_eprt_request) {
    uint16_t eprt_ipv6[8];
    uint32_t eprt_af = 0;
    uint32_t eprt_ip;
    parse_eprt_request(line, linelen, &eprt_af, &eprt_ip, eprt_ipv6, &ftp_data_port);

    struct conversation_tuple tuple;
    memset(&tuple, 0, sizeof(tuple));
    tuple.proto = IPPROTO_TCP;
    if (eprt_af == EPRT_AF_IPv4) {
      tuple.ip_src.ip4 = eprt_ip;
      session->ftp_data_ip.ip4 = ftp_data_ip;
      session->ftp_data_ipVer = 4;

    } else if (eprt_af == EPRT_AF_IPv6) {
      memcpy(tuple.ip_src.ip6, eprt_ipv6, sizeof(tuple.ip_src.ip6));
      memcpy(session->ftp_data_ip.ip6, eprt_ipv6, sizeof(tuple.ip_src.ip6));
      session->ftp_data_ipVer = 6;
    }

    tuple.port_src = htons(ftp_data_port);
    if (direction == FLOW_DIR_SRC2DST)
      memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_dst, sizeof(tuple.ip_dst));
    else
      memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_src, sizeof(tuple.ip_dst));

    struct conversation_value *conv = ftp_find_or_create_conversation(flow, session, &tuple);
    // printf("ftp_control - data -port = %d\n",ftp_data_port);
    session->dataPort = ftp_data_port;
  }

  if (is_epasv_response) {
    parse_extended_pasv_response(line, linelen, &ftp_data_port);
    struct conversation_tuple tuple;
    memset(&tuple, 0, sizeof(tuple));
    tuple.proto = IPPROTO_TCP;

    tuple.port_src = htons(ftp_data_port);
    if (direction == FLOW_DIR_SRC2DST) {
      memcpy(&tuple.ip_src, &flow->tuple.inner.ip_src, sizeof(tuple.ip_src));
      memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_dst, sizeof(tuple.ip_dst));
    } else {
      memcpy(&tuple.ip_src, &flow->tuple.inner.ip_dst, sizeof(tuple.ip_src));
      memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_src, sizeof(tuple.ip_dst));
    }

    ftp_find_or_create_conversation(flow, session, &tuple);
    session->dataPort = ftp_data_port;
  }

  return 0;
}

static int dissect_ftp_server_info(struct ftp_info *info, char *code_str) {
  if (code_str == NULL) {
    return -1;
  }
  char *space_ptr = strchr(code_str, ' ');
  if (space_ptr == NULL) {
    return -1;
  }

  char *server_info_start = space_ptr + 1;
  if (*server_info_start == '(') {
    server_info_start++;
    char *end_ptr = strchr(server_info_start, ')');
    if (end_ptr == NULL) {
      return -1;
    }
    *end_ptr = '\0';
  } else {
    char *end_ptr = strchr(server_info_start, '[');
    if (end_ptr != NULL) {
      *end_ptr = '\0';
    }
  }

  char *token = strtok(server_info_start, " ");
  if (token != NULL) {
    strcpy(info->hostName, token);

    token = strtok(NULL, " ");
    if (token != NULL) {
      strcpy(info->software, token);
    }
  } else {
    return -1;
  }
  return 0;
}

/*
 *ftp-control的有序重组解析入口函数
 */
static int dissect_ftp_control_rsm(struct flow_info *flow, uint8_t direction, const uint8_t *payload, uint32_t payload_len) {
  uint8_t             is_request;
  uint8_t             is_port_request = 0;
  uint8_t             is_eprt_request = 0;
  uint8_t             is_pasv_response = 0;
  uint8_t             is_epasv_response = 0;
  size_t              copy_len = 0;
  int                 linelen;
  uint32_t            tokenlen = 0;
  const uint8_t      *next_token;
  const uint8_t      *line;
  char                code_str[4];
  int                 code;
  struct ftp_info     info;
  struct ftp_session *session;  // add by liugh

  memset(&info, 0, sizeof(info));

  if (direction == FLOW_DIR_SRC2DST)
    is_request = 1;
  else
    is_request = 0;

  info.is_request = is_request;
  line = payload;

  if (flow->pkt_first_line.has_search == 0) {
    linelen = find_packet_line_end(payload, payload_len);
    flow->pkt_first_line.has_search = 1;
    flow->pkt_first_line.linelen = linelen;
  } else {
    linelen = payload_len;
  }

  if (linelen <= 0) {
    return PKT_DROP;
  }

  if (flow->app_session == NULL) {
    flow->app_session = dpi_malloc(sizeof(struct ftp_session));
    if (flow->app_session == NULL) {
      DPI_LOG(DPI_LOG_WARNING, "malloc failed");
      return PKT_OK;
    }
    memset(flow->app_session, 0, sizeof(struct ftp_session));
    session = (struct ftp_session *)flow->app_session;
    session->creatime = time(NULL);
  }
  session = (struct ftp_session *)flow->app_session;
  // 获取请求命令
  if (is_request) {
    /*
     * Extract the first token, and, if there is a first
     * token, add it as the request.
     */
    tokenlen = dpi_get_token_len(line, line + linelen, &next_token);
    if (tokenlen != 0) {
      if (strncmp((const char *)line, "PORT", tokenlen) == 0) {
        is_port_request = 1;
        session->mode = 0;
      } else if (strncmp((const char *)line, "PASV", tokenlen) == 0 || strncmp((const char *)line, "EPSV", tokenlen) == 0) {
        session->mode = 1;
      }
      /*
       *  request command, as per RFC 2428
       */
      else if (strncmp((const char *)line, "EPRT", tokenlen) == 0) {
        session->mode = 0;
        is_eprt_request = 1;
      }

      copy_len = tokenlen >= sizeof(info.cmd_or_code) ? (sizeof(info.cmd_or_code) - 1) : tokenlen;
      strncpy(info.cmd_or_code, (const char *)line, copy_len);

      info.cmd_or_code[copy_len] = 0;
    }
  } else {
    // 获取响应代码

    if (linelen > 3 && isdigit(line[0]) && isdigit(line[1]) && isdigit(line[2])) {
      memcpy(code_str, line, sizeof(code_str) - 1);

      code_str[sizeof(code_str) - 1] = 0;

      code = atoi(code_str);
      if (code == 227)
        is_pasv_response = 1;
      else if (code == 229)
        is_epasv_response = 1;
      else if (code == 230) {
        strncpy(session->login_status, "YES", COMMON_STATUS_LEN);
      } else if (code == 530) {
        strncpy(session->login_status, "NO", COMMON_STATUS_LEN);
        // 登入失败
      } else if (code == 257) {
        // PWD失败
        // do nothing
      } else if (code == 226) {
        // 当收到传输结束的命令时 将控制信令的data端口置空 可以继续下一次建立conv
        // do nothing
      } else if (code == 220) {
        // 服务器版本
        dissect_ftp_server_info(&info, (char *)line);
        strncpy(session->hostName, info.hostName, strlen(info.hostName));
        strncpy(session->software, info.software, strlen(info.software));
      }
      // 获取响应命令
      if (linelen >= 4)
        next_token = line + 4;
      else
        next_token = line + linelen;

      strncpy(info.cmd_or_code, (const char *)line, 3);
      info.cmd_or_code[3] = 0;
    } else {
      /*
       * Line doesn't start with 3 digits; assume it's
       * a line in the middle of a multi-line reply.
       */
      next_token = line;
    }
  }

  // 获取请求响应参数
  linelen -= (int)(next_token - line);
  line = next_token;

  if (linelen >= 0) {
    copy_len = (uint32_t)linelen >= sizeof(info.args) ? (sizeof(info.args) - 1) : (uint32_t)linelen;
    strncpy(info.args, (const char *)line, copy_len);
    info.args[copy_len] = 0;
    /* retr-download， stou,stor-upload*/
    if ((strncasecmp(info.cmd_or_code, "retr", 4) == 0 || strncasecmp(info.cmd_or_code, "stor", 4) == 0 ||
            strncasecmp(info.cmd_or_code, "stou", 4) == 0)) {
      // 传输端口已经建立确定
      size_t decode_args_len = sizeof(session->storpath);
      size_t info_args_len = strlen(info.args);
      char   decode_str[1024] = {0};
      if (0 == decode_IOS8859_to_uft8((char *)info.args, info_args_len, decode_str, decode_args_len)) {
        strncpy(session->storpath, decode_str, sizeof(session->storpath));
        strncpy(info.args, session->storpath, sizeof(info.args));
      } else {
        strncpy(session->storpath, info.args, sizeof(session->storpath));
      }
      // 插入文件名
      bool filename_inserted =
          insert_filename_to_port_mapping(session, session->storpath, info.cmd_or_code, "FTP_CONTROL_RSM", false);
    } else if (strncasecmp(info.cmd_or_code, "cwd", 3) == 0 || strncasecmp(info.cmd_or_code, "size", 4) == 0) {
      // 切换目录 显示大小 对文件还原不起作用 所以不更新session中的目录
      size_t decode_args_len = sizeof(session->storpath), info_args_len = strlen(info.args);
      char   decode_str[1024] = {0};
      if (0 == decode_IOS8859_to_uft8((char *)info.args, info_args_len, decode_str, decode_args_len)) {
        strncpy(info.args, decode_str, sizeof(session->storpath));
      }
    } else if (strncasecmp(info.cmd_or_code, "list", 4) == 0 || strncasecmp(info.cmd_or_code, "mlsd", 4) == 0 ||
               strncasecmp(info.cmd_or_code, "nlst", 4) == 0) {
      // 对于LIST/NLST/MLSD命令，允许重复的file.path_list，因为每个命令都有自己的数据传输
      strncpy(session->storpath, "file.path_list", sizeof(session->storpath));
      bool filename_inserted =
          insert_filename_to_port_mapping(session, session->storpath, info.cmd_or_code, "FTP_CONTROL_RSM", true);
    }
  }

  // 处理字段
  if (is_request) {
    strncpy(info.met, info.cmd_or_code, sizeof(info.cmd_or_code) - 1);
    strncpy(info.met_contend, info.args, sizeof(info.args) - 1);
    if (NULL == strstr(session->reqCmd, info.met)) {
      strcat(session->reqCmd, info.met);
      strcat(session->reqCmd, ",");
    }
    if (NULL == strstr(session->reqArg, info.met_contend)) {
      strcat(session->reqArg, info.met_contend);
      strcat(session->reqArg, ",");
    }
  } else {
    strncpy(info.ret_code, info.cmd_or_code, sizeof(info.ret_code) - 1);
    strncpy(info.ret_con, info.args, sizeof(info.args) - 1);
    if (sizeof(session->resCode) > (strlen(session->resCode) + strlen(info.ret_code)) + 2) {
      strncpy(session->resCode + strlen(session->resCode), (char *)info.ret_code,
          sizeof(session->resCode) - strlen(session->resCode));
      strcat(session->resCode, ",");
    }
    if (sizeof(session->resArg) > (strlen(session->resArg) + strlen(info.ret_con)) + 2) {
      strncpy(session->resArg + strlen(session->resArg), (char *)info.ret_con, sizeof(session->resArg) - strlen(session->resArg));
      strcat(session->resArg, ",");
    }
  }

  if (strcasecmp(info.cmd_or_code, "USER") == 0) {
    memcpy(session->username, info.args, copy_len > sizeof(info.username) ? (sizeof(info.username) - 1) : copy_len);
  } else if (strcasecmp(info.cmd_or_code, "PASS") == 0) {
    memcpy(session->password, info.args, copy_len > sizeof(info.password) ? (sizeof(info.password) - 1) : copy_len);
  } else if (strcasecmp(info.cmd_or_code, "REST") == 0) {
    session->offset = atoi(info.args);
  } else if (strcasecmp(info.cmd_or_code, "TYPE") == 0) {
    if (strcasecmp(info.args, "A") == 0 || strcasecmp(info.args, "ASCII") == 0) {
      strncpy(session->con_type, "ASCII", strlen("ASCII"));
    } else if (strcasecmp(info.args, "E") == 0 || strcasecmp(info.args, "EBCDIC") == 0) {
      strncpy(session->con_type, "EBCDIC", strlen("EBCDIC"));
    } else if (strcasecmp(info.args, "i") == 0 || strcasecmp(info.args, "BINARY") == 0) {
      strncpy(session->con_type, "BINARY", strlen("BINARY"));
    }
  }

  uint16_t ftp_data_port = 0;
  uint32_t ftp_data_ip = 0;
  if (is_port_request || is_pasv_response) {
    parse_port_pasv(line, linelen, &ftp_data_ip, &ftp_data_port);
    // if(ftp_data_port == 0){
    //   abort();
    // }

    session->ftp_data_ip.ip4 = ftp_data_ip;
    session->ftp_data_ipVer = 4;
    session->file_num++;
    session->dataPort = ftp_data_port;
    DPI_LOG(DPI_LOG_TRACE, "[FTP_CONTROL_RSM] New data port = %d", ftp_data_port);

    // 检查端口是否已经存在，如果存在则更新，否则添加新的
    bool port_exists = false;
    for (int i = 0; i < MAX_FTP_PORT_FILENAME_NUM; i++) {
      if (session->proto_filename[i].dataPort == ftp_data_port) {
        DPI_LOG(DPI_LOG_TRACE, "[FTP_CONTROL_RSM] Port %d already exists at slot %d, filename='%s'", ftp_data_port, i,
            session->proto_filename[i].storpath);
        // 不清空文件名，保持现有状态，等待后续文件名插入时处理
        port_exists = true;
        break;
      }
    }

    if (!port_exists) {
      // 建立端口与文件名的对应关系，保存在服务端ip_port20的conv中
      for (int i = 0; i < MAX_FTP_PORT_FILENAME_NUM; i++) {
        // 更新传输文件端口
        if (session->proto_filename[i].dataPort != 0) {
          continue;
        }
        session->proto_filename[i].dataPort = ftp_data_port;
        ftp_find_conv_update_conversion_session_data(session);
        DPI_LOG(DPI_LOG_TRACE, "[FTP_CONTROL_RSM] Add port-filename mapping: port=%d, index=%d", ftp_data_port, i);
        break;
      }
    }
  }

  if (is_eprt_request) {
    uint16_t eprt_ipv6[8];
    uint32_t eprt_af = 0;
    uint32_t eprt_ip;
    parse_eprt_request(line, linelen, &eprt_af, &eprt_ip, eprt_ipv6, &ftp_data_port);

    session->file_num++;
    session->dataPort = ftp_data_port;
    for (int i = 0; i < MAX_FTP_PORT_FILENAME_NUM; i++) {
      // 更新传输文件端口
      if (session->proto_filename[i].dataPort != 0) {
        continue;
      }
      session->proto_filename[i].dataPort = ftp_data_port;
      ftp_find_conv_update_conversion_session_data(session);

      break;
    }
  }

  if (is_epasv_response) {
    parse_extended_pasv_response(line, linelen, &ftp_data_port);
    session->file_num++;
    session->dataPort = ftp_data_port;
    for (int i = 0; i < MAX_FTP_PORT_FILENAME_NUM; i++) {
      // 更新传输文件端口
      if (session->proto_filename[i].dataPort != 0) {
        continue;
      }
      session->proto_filename[i].dataPort = ftp_data_port;
      ftp_find_conv_update_conversion_session_data(session);
      break;
    }
  }

  if (g_config.ftp_packet_mode) {
    write_ftp_control_log(flow, direction, session);
  }

  return 0;
}

static void flow_ftp_control_finish(struct flow_info *flow) {
  if (!flow->app_session) {
    return;
  }
  // 添加control conversation建立的调试信息
  char src_ip[64] = {0};
  char dst_ip[64] = {0};
  get_ipstring(flow->tuple.inner.ip_version, flow->tuple.inner.ip_src, src_ip, 64);
  get_ipstring(flow->tuple.inner.ip_version, flow->tuple.inner.ip_dst, dst_ip, 64);

  DPI_LOG(DPI_LOG_TRACE, "[FTP_CONTROL_FINISH] Flow Ftp Tuple: %s:%d -> %s:%d (proto=%d)", src_ip,
      ntohs(flow->tuple.inner.port_src), dst_ip, ntohs(flow->tuple.inner.port_dst), flow->tuple.inner.proto);
  struct ftp_session *session = (struct ftp_session *)flow->app_session;
  ftp_find_conv_update_conversion_session_data(session);

  if (0 == session->file_num) {
    // 登入无文件传输,在此处写log，有文件动作，在conv超时时写文件（write_ftp_conversation_log）
    write_ftp_control_log(flow, 0, session);
  }

  free(flow->app_session);
  flow->app_session = NULL;

  return;
}

/*
 *ftp-data的识别函数，不全，没有pasv等的识别，暂时没有使用
 */
static int identify_ftp_data(struct flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len) {
  UNUSED(payload);
  UNUSED(payload_len);
  uint16_t s_port = 0, d_port = 0;
  s_port = ntohs(flow->tuple.inner.port_src);
  d_port = ntohs(flow->tuple.inner.port_dst);

  if (s_port == FTP_DATA_PORT || d_port == FTP_DATA_PORT)
    flow->real_protocol_id = PROTOCOL_FTP_DATA;

  return PROTOCOL_FTP_DATA;
}

static void flow_ftp_data_finish(struct flow_info *flow) {
  struct ftp_session *session = (struct ftp_session *)flow->app_session;
  if (!session)
    return;

  // 在data的conv超时时，将自己的文件进行关闭
  if (session->data_fp) {
    fclose(session->data_fp);
    session->data_fp = NULL;
  }
  // 添加control conversation建立的调试信息
  char src_ip[64] = {0};
  char dst_ip[64] = {0};
  get_ipstring(flow->tuple.inner.ip_version, flow->tuple.inner.ip_src, src_ip, 64);
  get_ipstring(flow->tuple.inner.ip_version, flow->tuple.inner.ip_dst, dst_ip, 64);

  DPI_LOG(DPI_LOG_TRACE, "[FTP_DATA_FINISH] Flow Ftp Tuple: %s:%d -> %s:%d (proto=%d)", src_ip, ntohs(flow->tuple.inner.port_src),
      dst_ip, ntohs(flow->tuple.inner.port_dst), flow->tuple.inner.proto);
  struct conversation_value *control_conv = NULL;
  // 如果是由ftp_c创建的的流
  if (session->control_conv_tuple.proto) {
    DPI_LOG(DPI_LOG_TRACE, "[FTP_DATA_FINISH] Data flow created by FTP_CONTROL, looking for control conversation");
    control_conv = find_conversation(&session->control_conv_tuple, 0);
    if (control_conv) {
      DPI_LOG(DPI_LOG_TRACE, "[FTP_DATA_FINISH] Found control conversation, updating data session info");
      struct ftp_session *control_session = control_conv->conv_session;
      if (control_session) {
        // 通过查找control_conv_tuple，获得到的conv_session，从中的proto_filename的表中找到自己端口所对应的文件名与trans_cmd
        uint16_t data_port = ntohs(flow->tuple.inner.port_src);
        bool     found_port_mapping = false;

        DPI_LOG(DPI_LOG_TRACE, "[FTP_DATA_FINISH] Searching for data port %d in control session proto_filename table", data_port);
        for (int i = 0; i < MAX_FTP_PORT_FILENAME_NUM; i++) {
          if (control_session->proto_filename[i].dataPort == data_port) {
            DPI_LOG(DPI_LOG_TRACE, "[FTP_DATA_FINISH] Found matching port %d at index %d, filename: %s, trans_cmd: %s", data_port,
                i, control_session->proto_filename[i].storpath, control_session->proto_filename[i].trans_cmd);
            found_port_mapping = true;

            // 更新到以自己端口创建的data_conv中
            if (strlen(control_session->proto_filename[i].storpath) > 0) {
              strncpy(session->storpath, control_session->proto_filename[i].storpath, sizeof(session->storpath));
              DPI_LOG(DPI_LOG_TRACE, "[FTP_DATA_FINISH] Updated data session storpath: %s", session->storpath);

              // 从文件名中提取文件类型
              const char *postfix = strrchr(control_session->proto_filename[i].storpath, '.');
              if (postfix) {
                strncpy(session->filetype, postfix + 1, sizeof(session->filetype));
                DPI_LOG(DPI_LOG_TRACE, "[FTP_DATA_FINISH] Updated data session filetype: %s", session->filetype);
              }
            }

            // 复制传输命令
            if (strlen(control_session->proto_filename[i].trans_cmd) > 0) {
              // 这里可以根据需要将trans_cmd保存到session的某个字段中
              DPI_LOG(DPI_LOG_TRACE, "[FTP_DATA_FINISH] Trans command: %s", control_session->proto_filename[i].trans_cmd);
            }

            break;
          }
        }

        if (!found_port_mapping) {
          DPI_LOG(DPI_LOG_TRACE, "[FTP_DATA_FINISH] No matching port found in control session proto_filename table");
        }
        // 查找以自己flow中ip端口作为tuple，查找conv，将自身的session更新到以自身为key的data_conv中
        struct conversation_tuple data_tuple;
        memset(&data_tuple, 0, sizeof(data_tuple));
        data_tuple.proto = IPPROTO_TCP;
        data_tuple.port_src = flow->tuple.inner.port_src;
        data_tuple.port_dst = flow->tuple.inner.port_dst;
        memcpy(&data_tuple.ip_src, &flow->tuple.inner.ip_src, sizeof(data_tuple.ip_src));
        memcpy(&data_tuple.ip_dst, &flow->tuple.inner.ip_dst, sizeof(data_tuple.ip_dst));
        char timeout_src_ip[64] = {0};
        char timeout_dst_ip[64] = {0};
        get_ip4string(timeout_src_ip, 64, data_tuple.ip_src.ip4);
        get_ip4string(timeout_dst_ip, 64, data_tuple.ip_dst.ip4);
        DPI_LOG(DPI_LOG_TRACE, "[FTP_DATA_FINISH] Find Self Conv Tuple: %s:%d -> %s:%d (proto=%d)", timeout_src_ip,
            ntohs(data_tuple.port_src), timeout_dst_ip, ntohs(data_tuple.port_dst), data_tuple.proto);
        struct conversation_value *data_conv = find_conversation(&data_tuple, 0);
        if (data_conv && data_conv->conv_session) {
          struct ftp_session *data_session = data_conv->conv_session;
          // 将自身的session信息更新到以自身为key的data_conv中
          strncpy(data_session->filepath, session->filepath, sizeof(data_session->filepath));
          strncpy(data_session->storpath, session->storpath, sizeof(data_session->storpath));
          strncpy(data_session->filetype, session->filetype, sizeof(data_session->filetype));
          data_session->real_len = session->real_len;
          data_session->total_len = session->total_len;
          ftp_creat_session_record(flow, data_session);
          DPI_LOG(DPI_LOG_TRACE, "[FTP_DATA_FINISH] Updated data conversation with self session info");
        } else {
          DPI_LOG(DPI_LOG_TRACE, "[FTP_DATA_FINISH] Data conversation not found for self tuple");
          ftp_creat_session_record(flow, session);
          struct conversation_value *conv = ftp_find_or_create_conversation(flow, session, &data_tuple);
        }
      }
    } else {
      DPI_LOG(DPI_LOG_TRACE, "[FTP_DATA_FINISH] Control conversation not found");
    }
  } else {
    // 如果没有control_conv_tuple，则写log
    DPI_LOG(DPI_LOG_INFO, "[FTP_DATA_FINISH] No control_conv_tuple found, this is not a flow created by FTP_CONTROL");
    DPI_LOG(DPI_LOG_INFO, "[FTP_DATA_FINISH] Writing log for data flow without control conversation");
    write_ftp_control_log(flow, FLOW_DIR_SRC2DST, session);
    return;
  }

  return;
}

int dissect_ftp_data_miss(struct flow_info *flow, uint8_t direction, uint32_t miss_len) {
  struct ftp_session *session = flow->app_session;
  if (!session) {
    return 0;
  }

  session->total_len += miss_len;
  return 0;
}

static int dissect_ftp_data_rsm(struct flow_info *flow, uint8_t direction, const uint8_t *payload, uint32_t payload_len) {
  struct ftp_session *session;
  /*一般情况下，此处app_session为ftp_control拷贝进入的session*/
  if (flow->app_session == NULL) {
    flow->app_session = dpi_malloc(sizeof(struct ftp_session));
    if (flow->app_session == NULL) {
      DPI_LOG(DPI_LOG_WARNING, "malloc failed");
      return PKT_OK;
    }
    memset(flow->app_session, 0, sizeof(struct ftp_session));
    session = (struct ftp_session *)flow->app_session;
  }
  session = (struct ftp_session *)flow->app_session;

  session->creatime = time(NULL);
  struct ftp_session        *s = NULL;
  struct conversation_value *control_conv = NULL;
  /*从conv_session中获取ftp_control的信息更新到ftp_data的session中*/
  /*这一步是为了拿到信息处理文件名*/
  if (session->control_conv_tuple.proto) {
    control_conv = find_conversation(&session->control_conv_tuple, 0);
  } else {
    struct conversation_tuple tuple;
    memset(&tuple, 0, sizeof(tuple));
    tuple.proto = IPPROTO_TCP;
    if (direction == FLOW_DIR_DST2SRC) {
      memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_src, sizeof(tuple.ip_dst));
      memcpy(&tuple.ip_src, &flow->tuple.inner.ip_dst, sizeof(tuple.ip_dst));
      tuple.port_src = flow->tuple.inner.port_dst;
      tuple.port_dst = flow->tuple.inner.port_src;
    } else {
      tuple.port_src = flow->tuple.inner.port_src;
      tuple.port_dst = flow->tuple.inner.port_dst;
      memcpy(&tuple.ip_dst, &flow->tuple.inner.ip_dst, sizeof(tuple.ip_dst));
      memcpy(&tuple.ip_src, &flow->tuple.inner.ip_src, sizeof(tuple.ip_src));
    }
    control_conv = find_conversation(&tuple, 0);
  }
  if (control_conv) {
    control_conv->createtime = g_config.g_now_time;
    s = control_conv->conv_session;
    ftp_update_conversion_session_data(session, s);
  }
  FILE *fp = NULL;
  if (!session->data_fp && g_config.ftp_content) {
    const char *postfix;

    // 设置客户端端口信息用于日志输出
    if (ntohs(flow->tuple.inner.port_src) == 20) {
      // PORT模式：服务端20端口连接客户端端口
      session->ftp_port = flow->tuple.inner.port_dst;
    } else if (ntohs(flow->tuple.inner.port_dst) == 20) {
      // PORT模式：客户端端口连接服务端20端口
      session->ftp_port = flow->tuple.inner.port_src;
    } else {
      // PASV模式：客户端端口通常较小
      session->ftp_port = (ntohs(flow->tuple.inner.port_src) < ntohs(flow->tuple.inner.port_dst)) ? flow->tuple.inner.port_src
                                                                                                  : flow->tuple.inner.port_dst;
    }

    DPI_LOG(DPI_LOG_TRACE, "[FTP_DATA_RSM] Data flow: src=%d, dst=%d, client_port=%d", ntohs(flow->tuple.inner.port_src),
        ntohs(flow->tuple.inner.port_dst), ntohs(session->ftp_port));

    // 如果当前没打开文件，查找control_conv_tuple的conv，进行文件名的匹配
    bool filename_found = false;
    if (session->control_conv_tuple.proto && control_conv && control_conv->conv_session) {
      struct ftp_session *control_session = control_conv->conv_session;
      uint16_t            data_port = ntohs(flow->tuple.inner.port_src);

      DPI_LOG(DPI_LOG_TRACE, "[FTP_DATA_RSM] Searching for data port %d in control session proto_filename table", data_port);
      for (int i = 0; i < MAX_FTP_PORT_FILENAME_NUM; i++) {
        if (control_session->proto_filename[i].dataPort == data_port) {
          DPI_LOG(DPI_LOG_TRACE, "[FTP_DATA_RSM] Found matching port %d at index %d, filename: %s", data_port, i,
              control_session->proto_filename[i].storpath);
          if (strlen(control_session->proto_filename[i].storpath) > 0) {
            // 找到了直接填入后缀
            postfix = strrchr(control_session->proto_filename[i].storpath, '.');
            if (postfix) {
              strncpy(session->filetype, postfix + 1, sizeof(session->filetype));
              strncpy(session->storpath, control_session->proto_filename[i].storpath, sizeof(session->storpath));
              DPI_LOG(DPI_LOG_TRACE, "[FTP_DATA_RSM] Got filetype from control session: %s, storpath: %s", session->filetype,
                  session->storpath);
              filename_found = true;
              break;
            }
          }
        }
      }
    }

    // 如果没匹配到则进行内容识别填充后缀
    if (!filename_found) {
      DPI_LOG(DPI_LOG_TRACE, "[FTP_DATA_RSM] No filename found in control session, using content detection");
      // 使用detect_file_type函数进行内容检测识别文件类型
      if (!detect_file_type((char *)payload, payload_len, session->filetype, sizeof(session->filetype))) {
        if (dpi_is_utf8((char *)payload, payload_len) > 0) {
          strncpy(session->filetype, "text", COMMON_SOME_TYPE);
        } else {
          strncpy(session->filetype, "bin", COMMON_SOME_TYPE);  // 默认为二进制文件
        }
      }
      DPI_LOG(DPI_LOG_TRACE, "[FTP_DATA_RSM] Detected filetype from content: %s", session->filetype);
    }
    char tmp[32] = {0};
    get_special_filename(NULL, "ftp", session->filetype, session->filepath, sizeof(session->filepath), 1);
    session->data_fp = fopen(session->filepath, "w");
  }
  fp = session->data_fp;
  if (fp) {
    fwrite(payload, payload_len, 1, fp);
    fp = NULL;
    if (control_conv) {
      struct ftp_session *s = control_conv->conv_session;
      if (s) {
        strncpy(s->filetype, session->filetype, sizeof(s->filetype));
        strncpy(s->filepath, session->filepath, sizeof(s->filepath));
        s->total_len += payload_len;
        session->real_len += payload_len;
        s->real_len = session->real_len;
      }
    }
  }
  struct conversation_tuple data_tuple;
  memset(&data_tuple, 0, sizeof(data_tuple));
  data_tuple.proto = flow->tuple.inner.proto;
  data_tuple.port_src = flow->tuple.inner.port_src;
  data_tuple.port_dst = flow->tuple.inner.port_dst;
  memcpy(&data_tuple.ip_src, &flow->tuple.inner.ip_src, sizeof(data_tuple.ip_src));
  memcpy(&data_tuple.ip_dst, &flow->tuple.inner.ip_dst, sizeof(data_tuple.ip_dst));
  char src_ip[64] = {0};
  char dst_ip[64] = {0};
  get_ip4string(src_ip, 64, data_tuple.ip_src.ip4);
  get_ip4string(dst_ip, 64, data_tuple.ip_dst.ip4);

  //将decode挂在flow上
  struct conversation_value *data_conv = find_conversation(&data_tuple, 0);
  if (data_conv) {
    data_conv->createtime = g_config.g_now_time;
  }
  return 0;
}

extern struct decode_t decode_ftp_c;
extern struct decode_t decode_ftp_u;

static int init_ftp_dissector(struct decode_t *decode) {
  dpi_register_proto_schema(ftp_field_array, EM_FTP_MAX, "ftp");
  session_protocol_st_size[PROTOCOL_FTP_DATA] = sizeof(struct ftp_session);
  register_tbl_array(TBL_LOG_FTP_CONTROL, 1, "ftp", NULL);

  decode_on_port_tcp(g_config.ftp_port, &decode_ftp_c);
  decode_on_port_tcp(FTP_CONTROL_PORT, &decode_ftp_c);
  decode_on_port_tcp(2121, &decode_ftp_c);

  map_fields_info_register(ftp_field_array, PROTOCOL_FTP_CONTROL, EM_FTP_MAX, "ftp");

  return 0;
}

static int init_ftp_dissector_u(struct decode_t *decode) {
  decode_on_port_tcp(FTP_DATA_PORT, &decode_ftp_u);
  return 0;
}

static int control_destroy(struct decode_t *decode) { return 0; }

struct decode_t decode_ftp_c = {
    .name = "ftp_control",
#ifdef DPI_SDT_ZDY
    .identify_type = DPI_IDENTIFY_PORT_CONTENT,
#endif
    .decode_initial = init_ftp_dissector,
    .pkt_identify = identify_ftp_control,
    .pkt_arrive = dissect_ftp_control,
    .pkt_dissect = dissect_ftp_control_rsm,
    .pkt_miss = dissect_ftp_data_miss,
    .flow_finish = flow_ftp_control_finish,
    .decode_destroy = control_destroy,
};

struct decode_t decode_ftp_u = {
    .name = "ftp_data",
#ifdef DPI_SDT_ZDY
    .identify_type = DPI_IDENTIFY_PORT_CONTENT,
#endif
    .decode_initial = init_ftp_dissector_u,
    .pkt_identify = identify_ftp_data,
    .pkt_dissect = dissect_ftp_data_rsm,
    .pkt_miss = dissect_ftp_data_miss,
    .flow_finish = flow_ftp_data_finish,
    .decode_destroy = control_destroy,
};