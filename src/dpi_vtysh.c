/****************************************************************************************
 * 文 件 名 : dpi_vtysh.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/07/06
编码: wangy            2018/07/06
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <stdio.h>
#include <string.h>
#include <sys/types.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <time.h>
#include <sys/time.h>
#include <unistd.h>

#include <rte_timer.h>
#include <rte_cycles.h>
#include <rte_mempool.h>
#include <rte_ethdev.h>

#include <libevent/event2/bufferevent.h>
#include <libevent/event2/listener.h>
#include <libevent/event2/buffer.h>

#include <glib.h>

#include "dpi_flow.h"
#include "dpi_vtysh_msg.h"
#include "dpi_detect.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "sdt_action_out.h"
#include "dpi_metrics.h"
#include <yaSdxWatch/dpi_statistics.h>
#define MAX_CLIENT 6
#define MAX_LINE 1024

extern struct rte_hash *g_sdt_hash_db;
extern struct mac_forward_cnt_t mac_forward_cnt[20]; //默认最大10个，Task最大10个

extern struct global_config g_config;
extern struct protocol_record p_record;

extern struct work_process_data flow_thread_info[MAX_FLOW_THREAD_NUM];
extern struct traffic_stats stat_dpdk[DEV_MAX_NUM][TRAFFIC_NUM];
extern const char *protocol_name_array[PROTOCOL_MAX];
extern struct rte_mempool *tcp_reassemble_mempool;

extern struct rte_hash *conversation_hash_exact;
extern struct rte_hash *conversation_hash_no_addr2;
extern struct rte_hash *conversation_hash_no_port2;

extern struct tbl_log_file tbl_log_array[TBL_LOG_MAX];
extern uint64_t log_total[TBL_LOG_MAX][TRAFFIC_NUM];
extern uint64_t g_inc_flow[TRAFFIC_NUM];
extern DpiMetrics g_metrics;
uint64_t packet_hit[2];


rte_atomic64_t aging_fail_pkts;
rte_atomic64_t flow_hash_fail_pkts;
rte_atomic64_t drop_pkts;
rte_atomic64_t drop_bytes;
rte_atomic64_t receive_pkts;
rte_atomic64_t receive_bytes;

rte_atomic64_t app_match_fail_pkts;


rte_atomic64_t tbl_fail_pkts;
rte_atomic64_t tbl_fail_bytes;


rte_atomic64_t dpi_fail_pkts;
rte_atomic64_t rsm_fail_get;
rte_atomic64_t flow_fail_get;
rte_atomic64_t log_256k_fail;

rte_atomic64_t sdt_pcap_out_pkts;
rte_atomic64_t sdt_pcap_success_pkts;
rte_atomic64_t sdt_pcap_do_pkt_dump;
rte_atomic64_t sdt_event_success_pkts;
rte_atomic64_t sdt_pcap_fail_pkts;
rte_atomic64_t sdt_event_fail_pkts;
rte_atomic64_t sdt_syslog_fail_pkts;
rte_atomic64_t sdt_syslog_success_pkts;



rte_atomic64_t sdt_pcap_out_forward_pkts;
rte_atomic64_t sdt_pcap_out_forward_ok_pkts;
rte_atomic64_t sdt_pcap_out_forward_fail_pkts;

void *socket_main(void *args);
time_t rule_update;
static const char *cal_bytes_speed(uint64_t speed)
{
    static char res[64] = {0};

    res[0] = 0;
    if (speed > 1000 * 1000 * 1000)
        snprintf(res, sizeof(res), "%.2lf Gb/s", (double)speed / (1000 * 1000 * 1000));
    else if (speed > 1000 * 1000)
        snprintf(res, sizeof(res), "%.2lf Mb/s", (double)speed / (1000 * 1000));
    else if (speed > 1000)
        snprintf(res, sizeof(res), "%.2lf Kb/s", (double)speed / (1000));
    else
        snprintf(res, sizeof(res), "%.2lf b/s", (double)speed);

    return res;
}

static const char *cal_bytes_speed1(uint64_t speed)
{
    static char res[64] = {0};

    res[0] = 0;
    if (speed > 1000 * 1000 * 1000)
        snprintf(res, sizeof(res), "%.2lf Gb/s", (double)speed / (1000 * 1000 * 1000));
    else if (speed > 1000 * 1000)
        snprintf(res, sizeof(res), "%.2lf Mb/s", (double)speed / (1000 * 1000));
    else if (speed > 1000)
        snprintf(res, sizeof(res), "%.2lf Kb/s", (double)speed / (1000));
    else
        snprintf(res, sizeof(res), "%.2lf b/s", (double)speed);

    return res;
}

static const char *cal_pkts_speed(uint32_t speed)
{
    static char res[64] = {0};

    res[0] = 0;
    if (speed > 1000 * 1000 * 1000)
        snprintf(res, sizeof(res), "%.2lf Gpps", (double)speed / (1000 * 1000 * 1000));
    else if (speed > 1000 * 1000)
        snprintf(res, sizeof(res), "%.2lf Mpps", (double)speed / (1000 * 1000));
    else if (speed > 1000)
        snprintf(res, sizeof(res), "%.2lf Kpps", (double)speed / (1000));
    else
        snprintf(res, sizeof(res), "%.2lf pps", (double)speed);

    return res;
}

static const char *cal_pkts_speed1(uint32_t speed)
{
    static char res[64] = {0};

    res[0] = 0;
    if (speed > 1000 * 1000 * 1000)
        snprintf(res, sizeof(res), "%.2lf Gpps", (double)speed / (1000 * 1000 * 1000));
    else if (speed > 1000 * 1000)
        snprintf(res, sizeof(res), "%.2lf Mpps", (double)speed / (1000 * 1000));
    else if (speed > 1000)
        snprintf(res, sizeof(res), "%.2lf Kpps", (double)speed / (1000));
    else
        snprintf(res, sizeof(res), "%.2lf pps", (double)speed);

    return res;
}

#if 0
static const char *cal_bytes(uint64_t bytes)
{
    static char res[64] = {0};

    res[0] = 0;
    if (bytes > 1024 * 1024 * 1024)
        snprintf(res, sizeof(res), "%.2lf GB", (double)bytes / (1024 * 1024 * 1024));
    else if (bytes > 1024 * 1024)
        snprintf(res, sizeof(res), "%.2lf MB", (double)bytes / (1024 * 1024));
    else if (bytes > 1024)
        snprintf(res, sizeof(res), "%.2lf KB", (double)bytes / (1024));
    else
        snprintf(res, sizeof(res), "%.2lf B", (double)bytes);

    return res;
}
#endif

void update_global_time(void);
void update_global_time(void)
{
    struct timeval tv;
    if (gettimeofday(&tv, NULL))
        return;

    g_config.g_now_time_usec = (uint64_t)tv.tv_sec * 1000000 + tv.tv_usec;

    time_t tm = tv.tv_sec;
    if(tm > g_config.g_now_time){
        g_config.g_now_time = tm;   // updating timestamp only if nowtime is bigger than the last time
        g_config.timeout_index = tm % TIMEOUT_MAX;
        timet_to_datetime(g_config.g_now_time, g_config.time_str, 32);
    }

}

void update_protocol_record(int flag);
void update_protocol_record(int flag)
{
    int i = PROTOCOL_UNKNOWN;
    off_t currpos;
    struct tm* ret = NULL;
    struct tm lastdate;
    struct tm currdate;
    char record_line[64] = { 0 };
    char num[64] = { 0 };

    time_t now_time = time(NULL);
    if(flag)
        goto ppp;

    if (p_record.last_record_time + g_config.record_update_time < now_time)
    {
        ret = localtime_r(&now_time, &currdate);
        if (NULL == ret)
            return;

        ret = localtime_r(&p_record.last_record_time, &lastdate);
        if (NULL == ret)
            return;

        if (ftruncate(p_record.fd_min, 0) != 0)
        {
            DPI_LOG(DPI_LOG_WARNING, "recording per min file truncate fail, may cause some bugs.");
            return;
        }

        currpos = lseek(p_record.fd_min, 0, SEEK_SET);
        if (-1 == currpos)
            return;

        for (i = PROTOCOL_UNKNOWN; i < PROTOCOL_MAX; i++)
        {
            memset(record_line, 0, 64);
            strcpy(record_line, p_record.proto_name[i]);
            strcat(record_line, " : ");
            snprintf(num, 64, "%lu", p_record.record_permin[i]);
            strcat(record_line, num);
            memset(num, 0, 64);
            if (unlikely(p_record.record_permin_circle[i] > 0))
            {
                // 基本不会执行
                snprintf(num, 64, " + %lu * UINT64_MAX", p_record.record_permin_circle[i]);
                strcat(record_line, num);
                memset(num, 0, 64);
            }
            write(p_record.fd_min, record_line, strlen(record_line));
            write(p_record.fd_min, "\n", 1);
        }
ppp:
        if (ftruncate(p_record.fd_day, 0) != 0)
        {
            DPI_LOG(DPI_LOG_WARNING, "recording per day file truncate fail, may cause some bugs.");
            return;
        }

        currpos = lseek(p_record.fd_day, 0, SEEK_SET);
        if (-1 == currpos)
            return;

        for (i = PROTOCOL_UNKNOWN; i < PROTOCOL_MAX; i++)
        {
            memset(record_line, 0, 64);
            strcpy(record_line, p_record.proto_name[i]);
            strcat(record_line, " : ");
            snprintf(num, 64, "%lu", p_record.record_perday[i]);
            strcat(record_line, num);
            memset(num, 0, 64);
            if (unlikely(p_record.record_perday_circle[i] > 0))
            {
                // 基本不会执行
                snprintf(num, 64, " + %lu * UINT64_MAX", p_record.record_perday_circle[i]);
                strcat(record_line, num);
                memset(num, 0, 64);
            }
            write(p_record.fd_day, record_line, strlen(record_line));
            write(p_record.fd_day, "\n", 1);
        }

        if(flag)
            return;

        pthread_mutex_lock(&p_record.record_mutex);

        for (i = PROTOCOL_UNKNOWN; i < PROTOCOL_MAX; i++)
        {
            p_record.record_permin[i] = 0;
            p_record.record_permin_circle[i] = 0;
        }

        if ((currdate.tm_year == lastdate.tm_year && currdate.tm_mon == lastdate.tm_mon && currdate.tm_mday > lastdate.tm_mday) ||
            (currdate.tm_year == lastdate.tm_year && currdate.tm_mon > lastdate.tm_mon) ||
            (currdate.tm_year > lastdate.tm_year))
        {
            for (i = PROTOCOL_UNKNOWN; i < PROTOCOL_MAX; i++)
            {
                p_record.record_perday[i] = 0;
                p_record.record_perday_circle[i] = 0;
            }
        }

        pthread_mutex_unlock(&p_record.record_mutex);

        p_record.last_record_time = now_time;
    }

    return;
}

static void send_str_client(struct bufferevent *bev, const char *str, int len)
{
    bufferevent_write(bev, str, len);
}

static void _show_thread_info(char *msg, int len)
{
    int idx = 0;
    unsigned int thread_id;
    uint32_t flow_count_total = 0;
    uint64_t pkts_count_total = 0;
    uint64_t bytes_count_total = 0;

    for (thread_id = 0; thread_id < g_config.dissector_thread_num; thread_id++) {
        flow_count_total += flow_thread_info[thread_id].stats.flow_count;
        pkts_count_total += flow_thread_info[thread_id].stats.raw_packet_count;
        bytes_count_total += flow_thread_info[thread_id].stats.total_wire_bytes;

        if (idx < len)
            idx += snprintf(msg + idx, len - idx, "thread %2u has %10u flows, received %20llu pkts , %25llu Bytes\n",
                    thread_id, flow_thread_info[thread_id].stats.flow_count,
                    (long long unsigned int)flow_thread_info[thread_id].stats.raw_packet_count,
                    (long long unsigned int)flow_thread_info[thread_id].stats.total_wire_bytes);
    }
    if (idx < len)
        idx += snprintf(msg + idx, len - idx, "total     has %10u flows, received %20llu pkts , %25llu Bytes\n",
                flow_count_total, (long long unsigned int)pkts_count_total, (long long unsigned int)bytes_count_total);
}

static void _show_flow_detail_info(char *msg, int len)
{
    int i;
    int idx = 0;
    unsigned int thread_id;
    double percent;
    uint64_t flow_count_total = 0;
    uint64_t flow_detail[PROTOCOL_MAX] = {0};

    for (thread_id = 0; thread_id < g_config.dissector_thread_num; thread_id++) {
        for (i = 0; i < PROTOCOL_MAX; i++) {
            flow_detail[i] += flow_thread_info[thread_id].stats.flow_stats[i];

        }
        flow_count_total += flow_thread_info[thread_id].stats.flow_count;
    }

    for (i = 0; i < PROTOCOL_MAX; i++) {
        if (flow_count_total == 0)
            percent = 0;
        else
            percent = (double)flow_detail[i] * 100 /flow_count_total;
        if (idx < len && percent)
            idx += snprintf(msg + idx, len - idx, "%-12s protocol flow num is :%lu;   %.2lf%% percent\n",
                    protocol_name_array[i], flow_detail[i], percent);
    }
    if (idx < len)
        idx += snprintf(msg + idx, len - idx, "%-12s protocol flow num is :%lu\n", "TOTAL", flow_count_total);
}

static void _show_flow_total_info(char *msg, int len)
{
    int i;
    int idx = 0;
    double percent;
    double percent_pkts;
    double percent_bytes;
    unsigned int thread_id;
    uint64_t flow_count_total = 0;
    uint64_t pkts_count_total = 0;
    uint64_t bytes_count_total = 0;

    uint64_t flow_detail[PROTOCOL_MAX] = {0};
    uint64_t flow_detail_pkts[PROTOCOL_MAX] = {0};
    uint64_t flow_detail_bytes[PROTOCOL_MAX] = {0};

    for (thread_id = 0; thread_id < g_config.dissector_thread_num; thread_id++) {
        pkts_count_total += flow_thread_info[thread_id].stats.raw_packet_count;
        bytes_count_total += flow_thread_info[thread_id].stats.total_wire_bytes;
        for (i = 0; i < PROTOCOL_MAX; i++) {
            flow_detail[i] += flow_thread_info[thread_id].stats.flow_stats_total[i];
            flow_detail_pkts[i] += flow_thread_info[thread_id].stats.flow_stats_total_pkts[i];
            flow_detail_bytes[i] += flow_thread_info[thread_id].stats.flow_stats_total_bytes[i];
            flow_count_total += flow_thread_info[thread_id].stats.flow_stats_total[i];
        }
    }

    idx = snprintf(msg , len,
            "%32s %16s %16s %16s %16s %16s %16s\n",
            "PROTOCOL_NAME", "NUM", "PERCENT", "PKTS", "PERCENT", "BYTES", "PERCEMT");

    for (i = 0; i < PROTOCOL_MAX; i++) {
        if (flow_count_total == 0)
            percent = 0;
        else
            percent = (double)flow_detail[i] * 100 /flow_count_total;

        if (pkts_count_total == 0)
            percent_pkts = 0;
        else
            percent_pkts = (double)flow_detail_pkts[i] * 100 /pkts_count_total;

        if (bytes_count_total == 0)
            percent_bytes = 0;
        else
            percent_bytes = (double)flow_detail_bytes[i] * 100 /bytes_count_total;

        if (idx < len)
                idx += snprintf(msg + idx, len - idx,
                "%32s %16lu %15.2lf%% %16lu %15.2lf%% %16lu %15.2lf%%\n",
                protocol_name_array[i], flow_detail[i], percent,
                flow_detail_pkts[i], percent_pkts,
                flow_detail_bytes[i], percent_bytes);

    }
    if (idx < len)
        idx += snprintf(msg + idx, len - idx, "%-12s protocol flow num is :%lu; pkts is :%lu;  bytes is %lu \n", "TOTAL",
                flow_count_total, pkts_count_total, bytes_count_total);
}

static void _clean_flow_total_info(void)
{
    int i;
    unsigned int thread_id;
    for (thread_id = 0; thread_id < g_config.dissector_thread_num; thread_id++) {
        for (i = 0; i < PROTOCOL_MAX; i++) {
            memset(&flow_thread_info[thread_id].stats, 0, sizeof(flow_thread_info[thread_id].stats));
            /*
            flow_thread_info[thread_id].stats.flow_stats_total[i] = 0;
            flow_thread_info[thread_id].stats.flow_stats_total_pkts[i] = 0;
            flow_thread_info[thread_id].stats.flow_stats_total_bytes[i] = 0;
            flow_thread_info[thread_id].stats.raw_packet_count = 0;
            flow_thread_info[thread_id].stats.total_wire_bytes = 0;*/
        }
    }
    rte_atomic64_set(&receive_bytes, 0);
    rte_atomic64_set(&drop_bytes, 0);
    rte_atomic64_set(&receive_pkts, 0);
    rte_atomic64_set(&drop_pkts, 0);
    return;
}

static void _show_tbl_log_info(char *msg, int len)
{
    int i;
    int idx = 0;
    uint64_t total = 0;
    uint64_t total_speed = 0;

    for (i = 0; i < TBL_LOG_MAX; i++) {
        total += log_total[i][0];
        total_speed += log_total[i][1] - log_total[i][2];
        if (idx < len)
            idx += snprintf(msg + idx, len - idx, "%-12s protocol tbl log num is :%lu, speed is %lu/s\n",
                    tbl_log_array[i].protoname, log_total[i][0], log_total[i][1] - log_total[i][2]);
    }
    if (idx < len)
        idx += snprintf(msg + idx, len - idx, "%-12s protocol flow num is :%lu, speed is %lu/s", "TOTAL", total, total_speed);
}

static void _clean_tbl_log_info(void)
{
    int i, j;

    for (i = 0; i < TBL_LOG_MAX; i++) {
        for (j = 0; j < TRAFFIC_NUM; j++) {
            log_total[i][j] = 0;
        }
    }

    return;
}

static const char *_show_conversation_hash(struct rte_hash *hash, const char *name)
{
    int num = 0;
    unsigned int idle_iter = 0;
    const void *next_key;
    void *next_data;

    static char result[256];

    while (rte_hash_iterate(hash, &next_key, &next_data, &idle_iter) >= 0) {
        num++;
    }
    snprintf(result, sizeof(result), "%s hash num is :%u\n", name, num);
    return result;
}
extern GHashTable *g_conversation_hash[];

static void _show_flow_hash_info(char *msg, int len)
{
    unsigned int i = 0;
    unsigned int idle_iter;
    int idx = 0;
    int num;
    const void *next_key;
    void *next_data;
    int total_num = 0;
    for (i = 0; i < g_config.dissector_thread_num; i++) {
        num = 0;
        idle_iter = 0;
        struct work_process_data *process = &flow_thread_info[i];
        //while (rte_hash_iterate(process->hash, &next_key, &next_data, &idle_iter) >= 0) {
        //    num++;
        //}
        num  = dpi_flow_hash_size(process->hash);
        total_num += num;

        if (idx < len)
            idx += snprintf(msg + idx, len - idx, "%-12u thread flow hash num is :%u\n", i, num);
    }
    if (idx < len)
        idx += snprintf(msg + idx, len - idx, "%-12s thread flow hash num is :%u\n", "TOTAL", total_num);
    char name_buff[COMMON_FILE_NAME]={0};
    snprintf(name_buff,COMMON_FILE_NAME,"conversation_hash_exact_%d",g_config.socketid);
    const char *res = _show_conversation_hash(conversation_hash_exact, name_buff);
    if (idx < len)
        idx += snprintf(msg + idx, len - idx, "%s", res);

    memset(name_buff,0,COMMON_FILE_NAME);
    snprintf(name_buff,COMMON_FILE_NAME,"conversation_hash_no_addr2_%d",g_config.socketid);
    res = _show_conversation_hash(conversation_hash_no_addr2, name_buff);
    if (idx < len)
        idx += snprintf(msg + idx, len - idx, "%s", res);

    memset(name_buff,0,COMMON_FILE_NAME);
    snprintf(name_buff,COMMON_FILE_NAME,"conversation_hash_no_port2_%d",g_config.socketid);
    res = _show_conversation_hash(conversation_hash_no_port2, name_buff);
    if (idx < len)
        idx += snprintf(msg + idx, len - idx, "%s", res);

    for (i = 0; i < g_config.flow_conv_thread_num; i++) {
        long table_size = g_hash_table_size(g_conversation_hash[i]);
        idx += snprintf(msg + idx, len - idx, "%d            flow_conv_thread_num is :%lu", i, table_size);
    }
}

struct _msg
{
    char *msg;
    int idx;
    int len;
};

static void
walk_cb(struct rte_mempool *mp, void *userdata)
{
    struct _msg *detail_msg = (struct _msg *)userdata;
    uint32_t in_use_num = rte_mempool_in_use_count(mp);
    uint32_t free_num = rte_mempool_avail_count(mp);

    if (detail_msg->idx < detail_msg->len)
        detail_msg->idx += snprintf(detail_msg->msg + detail_msg->idx, detail_msg->len - detail_msg->idx,
                "%32s %16u %16u %16u %16u %16llu %16llu %16llu\n",
                mp->name, mp->elt_size,
                mp->size, in_use_num, free_num,
                (unsigned long long)mp->elt_size * mp->size, (unsigned long long)mp->elt_size * in_use_num, (unsigned long long)mp->elt_size * free_num);
}

static void _show_all_mempool_detail_info(char *msg, int len)
{
    struct _msg detail_msg;
    detail_msg.msg = msg;
    detail_msg.len = len;

    detail_msg.idx = snprintf(detail_msg.msg , detail_msg.len,
            "%32s %16s %16s %16s %16s %16s %16s %16s\n",
            "MEMPOOL_NAME", "ELEMENT_SIZE", "TOTAL_NUM", "IN_USE_NUM", "FREE_NUM", "TOTAL_SIZE", "IN_USE_SIZE", "FREE_SIZE");

    rte_mempool_walk(walk_cb, &detail_msg);

    memcpy(msg, detail_msg.msg, len);
}

extern int tbl_log_queue_num;
static void _show_all_ring_detail_info(char *msg, int len)
{
    unsigned int i;
    int idx = 0;

    idx = snprintf(msg , len,
            "%32s %16s %16s %16s\n",
            "RING_NAME", "TOTAL_NUM", "IN_USE_NUM", "FREE_NUM");

    for (i = 0; i < g_config.dissector_thread_num; i++) {
        char packet_ring_name[64] = {0};
        snprintf(packet_ring_name, sizeof(packet_ring_name), "packet_ring_%d_%d",g_config.socketid, i);
        struct rte_ring *r = rte_ring_lookup(packet_ring_name);
        if (r == NULL) {
            DPI_LOG(DPI_LOG_WARNING, "error while lookup packet ring");
        }

        if (idx < len) {
            idx += snprintf(msg + idx, len - idx, "%32s %16u %16u %16u\n",
                            packet_ring_name,
                            rte_ring_get_capacity(r),
                            rte_ring_count(r),
                            rte_ring_free_count(r));
        }
    }


    for (i = 0; i < (unsigned)g_config.app_match_thread_num; i++) {
        char ring_name[64] = {0};
        snprintf(ring_name, sizeof(ring_name), "app_match_ring_%d_%d",g_config.socketid, i);
        struct rte_ring *r = rte_ring_lookup(ring_name);
        if (r == NULL) {
            DPI_LOG(DPI_LOG_WARNING, "error while lookup packet ring");
        }

        if (idx < len) {
            idx += snprintf(msg + idx, len - idx, "%32s %16u %16u %16u\n",
                            ring_name,
                            rte_ring_get_capacity(r),
                            rte_ring_count(r),
                            rte_ring_free_count(r));
        }
    }

    for (i = 0; i < (unsigned)g_config.log_thread_num; i++) {
        char ring_name[64] = {0};
        snprintf(ring_name, sizeof(ring_name), "tbl_ring_%d_%d",g_config.socketid, i);
        struct rte_ring *r = rte_ring_lookup(ring_name);
        if (r == NULL) {
            DPI_LOG(DPI_LOG_WARNING, "error while lookup packet ring");
        }

        if (idx < len) {
            idx += snprintf(msg + idx, len - idx, "%32s %16u %16u %16u\n",
                            ring_name,
                            rte_ring_get_capacity(r),
                            rte_ring_count(r),
                            rte_ring_free_count(r));
        }
    }

    for (i = 0; i < (unsigned)g_config.sdt_out_thead_num; i++) {
        char ring_name[64] = {0};
        snprintf(ring_name, sizeof(ring_name), "sdtout_ring_%d_%d",g_config.socketid, i);
        struct rte_ring *r = rte_ring_lookup(ring_name);
        if (r == NULL) {
            DPI_LOG(DPI_LOG_WARNING, "error while lookup packet ring");
        }

        if (idx < len) {
            idx += snprintf(msg + idx, len - idx, "%32s %16u %16u %16u\n",
                            ring_name,
                            rte_ring_get_capacity(r),
                            rte_ring_count(r),
                            rte_ring_free_count(r));
        }
    }

    for (i = 0; i < (unsigned)g_config.flow_aging_thread_num; i++) {
    // for (i = 0; i < (unsigned)1; i++) {
    char ring_name[64] = {0};
    snprintf(ring_name, sizeof(ring_name), "flow_aging_ring_%d_%d",g_config.socketid, i);
    struct rte_ring *r = rte_ring_lookup(ring_name);
    if (r == NULL) {
        DPI_LOG(DPI_LOG_WARNING, "error while flow aging ring");
        continue;
    }

    if (idx < len) {
        idx += snprintf(msg + idx, len - idx, "%32s %16u %16u %16u\n",
                        ring_name,
                        rte_ring_get_capacity(r),
                        rte_ring_count(r),
                        rte_ring_free_count(r));
    }
    }
    for (i = 0; i < (unsigned)g_config.flow_conv_thread_num; i++) {
    // for (i = 0; i < (unsigned)1; i++) {
    char ring_name[64] = {0};
    snprintf(ring_name, sizeof(ring_name), "flow_conv_ring_%d_%d",g_config.socketid, i);
    struct rte_ring *r = rte_ring_lookup(ring_name);
    if (r == NULL) {
        DPI_LOG(DPI_LOG_WARNING, "error while flow aging ring");
    }

    if (idx < len) {
        idx += snprintf(msg + idx, len - idx, "%32s %16u %16u %16u\n",
                        ring_name,
                        rte_ring_get_capacity(r),
                        rte_ring_count(r),
                        rte_ring_free_count(r));
    }
    }
    return;
}

static void _show_inc_flow(char *msg, int len)
{
    int i;
    int idx = 0;

    for (i = 1; i < TRAFFIC_NUM; i++) {

        if (idx < len)
            idx += snprintf(msg + idx, len - idx, "%d seconds ago, flow increase speed is %lu/s\n",
                    TRAFFIC_NUM - i,
                    g_inc_flow[TRAFFIC_NUM - i - 1] - g_inc_flow[TRAFFIC_NUM - i]);
    }
    if (idx < len)
        idx += snprintf(msg + idx, len - idx, "total create flow num is %lu\n",
                g_inc_flow[0]);
}

static void _show_dev_traffic_speed_info(char *msg, int len)
{
#if 0
    uint16_t idx_port;
    int i;
    int idx = 0;
    unsigned int thread_id;
#ifdef _DPI_DPDK_17
    unsigned nb_ports = rte_eth_dev_count();
#else
    unsigned nb_ports = rte_eth_dev_count_avail();
#endif
    struct traffic_stats total[TRAFFIC_NUM];
    memset(&total, 0, sizeof(total));

    for (idx_port = 0; idx_port < nb_ports && idx_port < DEV_MAX_NUM; idx_port++) {
        struct rte_eth_dev *dev;
        dev = &rte_eth_devices[idx_port];
        for (i = 1; i < TRAFFIC_NUM; i++) {
            uint64_t missed_pkts = stat_dpdk[idx_port][TRAFFIC_NUM - i - 1].imissed -
                                   stat_dpdk[idx_port][TRAFFIC_NUM - i].imissed;
            uint64_t error_pkts  = stat_dpdk[idx_port][TRAFFIC_NUM - i - 1].ierrors -
                                   stat_dpdk[idx_port][TRAFFIC_NUM - i].ierrors;
            uint64_t total_pkts  = stat_dpdk[idx_port][TRAFFIC_NUM - i - 1].ipkts -
                                   stat_dpdk[idx_port][TRAFFIC_NUM - i].ipkts;
            uint64_t total_bytes = stat_dpdk[idx_port][TRAFFIC_NUM - i - 1].ibytes -
                                   stat_dpdk[idx_port][TRAFFIC_NUM - i].ibytes;

            uint64_t tmp_total = missed_pkts + error_pkts + total_pkts;
            double missed_per;
            double error_per;
            if (tmp_total == 0) {
                missed_per = 0;
                error_per  = 0;
            }
            else {
                missed_per = missed_pkts * 100.0 / tmp_total;
                error_per  = error_pkts * 100.0 / tmp_total;
            }

            if (idx < len)
                idx += snprintf(msg + idx, len - idx, "%s : %d seconds ago, speed is %s, %s, (miss, error) pkts (%lu, %lu) percent is (%.2lf%%, %.2lf%%)\n",
                        dev->device->name,
                        TRAFFIC_NUM - i,
                        cal_bytes_speed(8 * total_bytes),
                        cal_pkts_speed(total_pkts),
                        missed_pkts, error_pkts,
                        missed_per, error_per
                        );

        }
        idx += snprintf(msg + idx, len - idx, "\n");
    }

    for (idx_port = 0; idx_port < nb_ports && idx_port < DEV_MAX_NUM; idx_port++) {
        struct rte_eth_dev *dev;
        dev = &rte_eth_devices[idx_port];

        uint64_t missed_pkts = stat_dpdk[idx_port][0].imissed;
        uint64_t error_pkts  = stat_dpdk[idx_port][0].ierrors;
        uint64_t tmp_total   = missed_pkts + error_pkts + stat_dpdk[idx_port][0].ipkts;
        double missed_per;
        double error_per;
        if (tmp_total == 0) {
            missed_per = 0;
            error_per = 0;
        }
        else {
            missed_per = missed_pkts * 100.0 / tmp_total;
            error_per  = error_pkts * 100.0 / tmp_total;
        }

        if (idx < len)
            idx += snprintf(msg + idx, len - idx, "%s :total (miss, error) pkts (%lu, %lu) percent is (%.2lf%%, %.2lf%%)\n",
                    dev->device->name,
                    missed_pkts, error_pkts,
                    missed_per, error_per);

    }
#endif
}

static void _show_traffic_speed_info(char *msg, int len)
{
#if 1
    uint16_t idx_port;
    int i;
    int idx = 0;
    unsigned int thread_id;
#ifdef _DPI_DPDK_17
    unsigned nb_ports = rte_eth_dev_count();
#else
    unsigned nb_ports = rte_eth_dev_count_avail();
#endif
    struct traffic_stats total[TRAFFIC_NUM];
    uint64_t bytes_total[TRAFFIC_NUM];
    uint64_t pkts_total[TRAFFIC_NUM];
    uint64_t miss_total[TRAFFIC_NUM];
    uint64_t error_total[TRAFFIC_NUM];
    memset(&total, 0, sizeof(total));
    memset(&bytes_total, 0, sizeof(bytes_total));
    memset(&pkts_total, 0, sizeof(pkts_total));
    memset(&miss_total, 0, sizeof(miss_total));
    memset(&error_total, 0, sizeof(error_total));

    for (i = 0; i < TRAFFIC_NUM; i++) {
        for (idx_port = 0; idx_port < nb_ports && idx_port < DEV_MAX_NUM; idx_port++) {
            pkts_total[i]  += stat_dpdk[idx_port][i].ipkts;
            bytes_total[i] += stat_dpdk[idx_port][i].ibytes;
            miss_total[i]  += stat_dpdk[idx_port][i].imissed;
            error_total[i] += stat_dpdk[idx_port][i].ierrors;
        }
    }

    for (thread_id = 0; thread_id < g_config.dissector_thread_num; thread_id++) {
        for (i = 0; i < TRAFFIC_NUM; i++) {
            total[i].ipkts  += flow_thread_info[thread_id].stats.traffic[i].ipkts;
            total[i].ibytes += flow_thread_info[thread_id].stats.traffic[i].ibytes;
        }
    }

    for (i = 1; i < TRAFFIC_NUM; i++) {
        uint64_t missed_pkts = miss_total[TRAFFIC_NUM - i - 1] - miss_total[TRAFFIC_NUM - i];
        uint64_t error_pkts = error_total[TRAFFIC_NUM - i - 1] - error_total[TRAFFIC_NUM - i];
        uint64_t total_pkts = pkts_total[TRAFFIC_NUM - i - 1] - pkts_total[TRAFFIC_NUM - i];
        uint64_t tmp_total = missed_pkts + error_pkts + total_pkts;
        double missed_per;
        double error_per;
        if (tmp_total == 0) {
            missed_per = 0;
            error_per = 0;
        }
        else {
            missed_per = (double)missed_pkts * 100 / tmp_total;
            error_per = error_pkts * 100.0 / tmp_total;
        }

        if (idx < len){
            if(g_config.show_traffic_speed_all){
                idx += snprintf(msg + idx, len - idx,
                    "%d seconds ago, speed is %s, %s, (miss, error) pkts (%lu, %lu) percent is (%.2lf%%, %.2lf%%), detect speed is %s, %s\n",
                    TRAFFIC_NUM - i,
                    cal_bytes_speed(8 * (bytes_total[TRAFFIC_NUM - i - 1] - bytes_total[TRAFFIC_NUM - i])),
                    cal_pkts_speed(total_pkts),
                    missed_pkts, error_pkts,
                    missed_per, error_per,
                    cal_bytes_speed1(8 * (total[TRAFFIC_NUM - i - 1].ibytes - total[TRAFFIC_NUM - i].ibytes)),
                    cal_pkts_speed1(total[TRAFFIC_NUM - i - 1].ipkts - total[TRAFFIC_NUM - i].ipkts));
            }else{
                missed_pkts = 0;
                error_pkts  = 0;
                missed_per  = 0;
                error_per   = 0;
                idx += snprintf(msg + idx, len - idx,
                      "%d seconds ago, speed is %s, %s, (miss, error) pkts (%lu, %lu) percent is (%.2lf%%, %.2lf%%)\n",
                      TRAFFIC_NUM - i,
                      cal_bytes_speed(8 * (bytes_total[TRAFFIC_NUM - i - 1] - bytes_total[TRAFFIC_NUM - i])),
                      cal_pkts_speed(total_pkts),
                      missed_pkts, error_pkts,
                      missed_per, error_per);
            }
        }
    }

    uint64_t missed_pkts = miss_total[0];
    uint64_t error_pkts = error_total[0];
    uint64_t tmp_total = missed_pkts + error_pkts + pkts_total[0];
    double missed_per;
    double error_per;
    if (tmp_total == 0) {
        missed_per = 0;
        error_per  = 0;
    }
    else {
        missed_per = missed_pkts * 100.0 / tmp_total;
        error_per  = error_pkts * 100.0 / tmp_total;
    }
    if (idx < len){
        if(g_config.show_traffic_speed_all){
            idx += snprintf(msg + idx, len - idx, "dev receive %llu Bytes, %llu pkts, (miss, error) pkts (%lu, %lu) percent is (%.2lf%%, %.2lf%%)\n",
                (long long unsigned int)bytes_total[0], (long long unsigned int)pkts_total[0], missed_pkts, error_pkts, missed_per, error_per);
            idx += snprintf(msg + idx, len - idx, "dpi receive %llu Bytes, %llu pkts; detected %llu Bytes, %llu pkts\n",
                (long long unsigned int)rte_atomic64_read(&receive_bytes), (long long unsigned int)rte_atomic64_read(&receive_pkts),
                (long long unsigned int)total[0].ibytes, (long long unsigned int)total[0].ipkts);
            idx += snprintf(msg + idx, len - idx, "drop %llu Bytes, %llu pkts\n",
                (long long unsigned int)rte_atomic64_read(&drop_bytes), (long long unsigned int)rte_atomic64_read(&drop_pkts));
        }else{
            missed_pkts = 0;
            error_pkts  = 0;
            missed_per  = 0;
            error_per   = 0;
            idx += snprintf(msg + idx, len - idx,
                   "total receive %llu Bytes, %llu pkts, (miss, error) pkts (%lu, %lu) percent is (%.2lf%%, %.2lf%%)\n",
                   (long long unsigned int)bytes_total[0],
                   (long long unsigned int)pkts_total[0],
                   missed_pkts, error_pkts, missed_per, error_per);
        }
    }
#endif
}

static void _show_fail_info(char *msg, int len)
{
        int idx = 0;
        unsigned port;
#ifdef _DPI_DPDK_17
        unsigned nb_ports = rte_eth_dev_count();
#else
        unsigned nb_ports = rte_eth_dev_count_avail();
#endif
        unsigned long long forward_pkts = 0,
                   missed_pkts  = 0,
                   error_pkts   = 0,
                   dpi_receive, dpi_fail, tbl_pkts, tbl_bytes;

        unsigned long long sdt_match_fail_pkts=0;
        unsigned long long sdt_do_pkt_pcap=0;
		unsigned long long pcap_out_forward_pkts = 0;
		unsigned long long pcap_out_forward_ok_pkts = 0;
		unsigned long long pcap_out_forward_fail_pkts = 0;

        unsigned long long pcap_success_pkts=0, event_success_pkts=0, pcap_out_pkts=0;
        unsigned long long pcap_fail_pkts=0, event_fail_pkts=0;
        struct rte_eth_stats stat_info;

        char buff[64];
        struct tm *time_info = localtime(&rule_update);
        strftime(buff, sizeof(buff), "%Y-%m-%d %H:%M:%S", time_info);

        for(port=0; port < nb_ports; port++){
            if(rte_eth_stats_get(port, &stat_info) == 0){
                forward_pkts += stat_info.ipackets;
                missed_pkts  += stat_info.imissed;
                error_pkts   += stat_info.ierrors;
            }
        }

        dpi_receive = rte_atomic64_read(&receive_pkts);
        dpi_fail    = rte_atomic64_read(&dpi_fail_pkts);
        tbl_pkts    = rte_atomic64_read(&tbl_fail_pkts);
        tbl_bytes   = rte_atomic64_read(&tbl_fail_bytes);

        sdt_match_fail_pkts= rte_atomic64_read(&app_match_fail_pkts);

        pcap_out_pkts       = rte_atomic64_read(&sdt_pcap_out_pkts);
        pcap_success_pkts   = rte_atomic64_read(&sdt_pcap_success_pkts);
        sdt_do_pkt_pcap     = rte_atomic64_read(&sdt_pcap_do_pkt_dump);
        event_success_pkts  = rte_atomic64_read(&sdt_event_success_pkts);
        pcap_fail_pkts    = rte_atomic64_read(&sdt_pcap_fail_pkts);
        event_fail_pkts   = rte_atomic64_read(&sdt_event_fail_pkts);

		pcap_out_forward_pkts = rte_atomic64_read(&sdt_pcap_out_forward_pkts);
		pcap_out_forward_ok_pkts = rte_atomic64_read(&sdt_pcap_out_forward_ok_pkts);
		pcap_out_forward_fail_pkts = rte_atomic64_read(&sdt_pcap_out_forward_fail_pkts);

        idx += snprintf(msg+idx, len-idx, "DEV      : total receive %llupkts,\ttotal missed  %llupkts,\ttotal errors  %llupkts, fail percent  %lf%%\n",
                            forward_pkts, missed_pkts, error_pkts, forward_pkts + missed_pkts + error_pkts ==  0 ? 0 : (missed_pkts + error_pkts) * 100.0 / (forward_pkts + missed_pkts + error_pkts));
        if(idx < len)
            idx += snprintf(msg+idx, len-idx, "DPI      : total receive %llupkts,\tfail enqueue  %llupkts,\tfail percent  %lf%%\n",
                                dpi_receive, dpi_fail, dpi_receive ? dpi_fail * 100.0 / dpi_receive : 0);

        //if(idx < len && rule_update)
        //    idx += snprintf(msg+idx, len-idx, "更新时间 : %s\n", buff);

        if(idx < len)
            idx += snprintf(msg+idx, len-idx, "SDT MATCH: enqueue fail %llu \n",sdt_match_fail_pkts);

        if(idx < len)
            idx += snprintf(msg+idx, len-idx, "ACL      : hit %zu pkts, total %zu pkts\n", packet_hit[1], packet_hit[0]);

        if(idx < len)
            idx += snprintf(msg+idx, len-idx, "SDT      : do_pcap [%llu]pkts, success pcap[%llu]pkts,out[%llu] fail pcap[%llu]pkts,success event[%llu]pkts,fail event[%llu]pkts\n",
                                              sdt_do_pkt_pcap, pcap_success_pkts,pcap_out_pkts,pcap_fail_pkts,event_success_pkts,event_fail_pkts);
		if (idx < len)
			idx += snprintf(msg+idx, len-idx, "SDT      : forward  out[%llu]pkts,success [%llu]pkts,fail [%llu]pkts\n",
                                               pcap_out_forward_pkts, pcap_out_forward_ok_pkts, pcap_out_forward_fail_pkts);

        for(int i =0; i< 20; i++)
        {
            if(mac_forward_cnt[i].cnt)
            {
                char buff[32];
                memset(buff, 0, sizeof(buff));
                bintoMAC(mac_forward_cnt[i].mac, 6, buff, sizeof(buff));
                if (idx < len)
                    idx += snprintf(msg+idx, len-idx, "forward  : MAC %s forward %zu\n", buff, mac_forward_cnt[i].cnt);
            }
        }
        if(idx < len)
            idx += snprintf(msg+idx, len-idx, "RSM      : fail  get      %ld\n",  rte_atomic64_read(&rsm_fail_get));

        if(idx < len)
            idx += snprintf(msg+idx, len-idx, "Flow     : fail  get      %ld\n",  rte_atomic64_read(&flow_fail_get));

        if(idx < len)
            idx += snprintf(msg+idx, len-idx, "Flow_Hash : fail  get      %ld\n",  rte_atomic64_read(&flow_hash_fail_pkts));

        if(idx < len)
            idx += snprintf(msg+idx, len-idx, "Flow_Aging_EnQueue: fail  get %ld\n",  rte_atomic64_read(&aging_fail_pkts));

        if(idx < len)
            idx += snprintf(msg+idx, len-idx, "256K LOG : fail  get      %ld\n",  rte_atomic64_read(&log_256k_fail));
        return;
}

static void _show_current_traffic_speed(char *msg, int len)
{
    int i;
    uint16_t idx_port;
    uint64_t bytes_total[TRAFFIC_NUM];
#ifdef _DPI_DPDK_17
    unsigned nb_ports = rte_eth_dev_count();
#else
    unsigned nb_ports = rte_eth_dev_count_avail();
#endif
    memset(&bytes_total, 0, sizeof(bytes_total));

    for (i = 0; i < TRAFFIC_NUM; i++) {
        for (idx_port = 0; idx_port < nb_ports && idx_port < DEV_MAX_NUM; idx_port++) {
            bytes_total[i] += stat_dpdk[idx_port][i].ibytes;
        }
    }
    snprintf(msg, len, "%lu", 8 * (bytes_total[0] - bytes_total[1]));
}

static void _show_flow_timeout(char *msg, int len)
{
    int idx = 0;

    if (idx < len)
        idx += snprintf(msg + idx, len - idx, "tcp  timeout is %u seconds\n", g_config.tcp_flow_timeout);
    if (idx < len)
        idx += snprintf(msg + idx, len - idx, "udp  timeout is %u seconds\n", g_config.udp_flow_timeout);
    if (idx < len)
        idx += snprintf(msg + idx, len - idx, "sctp timeout is %u seconds\n", g_config.sctp_flow_timeout);
}

static void _set_tcp_timeout(uint16_t timeout, char *msg, int len)
{
    int idx = 0;
    uint16_t old = g_config.tcp_flow_timeout;

    if (timeout >= TIMEOUT_MAX) {
        if (idx < len)
            idx += snprintf(msg + idx, len - idx, "tcp timeout max num is %d seconds", TIMEOUT_MAX - 1);
        return;
    }
    if (timeout > 0)
        g_config.tcp_flow_timeout = timeout;

    if (idx < len)
        idx += snprintf(msg + idx, len - idx, "tcp old timeout is %u seconds, now is %u seconds\n", old, g_config.tcp_flow_timeout);
}

static void _set_udp_timeout(uint16_t timeout, char *msg, int len)
{
    int idx = 0;
    uint16_t old = g_config.udp_flow_timeout;
    if (timeout >= TIMEOUT_MAX) {
        if (idx < len)
            idx += snprintf(msg + idx, len - idx, "tcp timeout max num is %d seconds", TIMEOUT_MAX - 1);
        return;
    }
    if (timeout > 0)
        g_config.udp_flow_timeout = timeout;

    if (idx < len)
        idx += snprintf(msg + idx, len - idx, "udp old timeout is %u seconds, now is %u seconds\n", old, g_config.udp_flow_timeout);
}

static void _set_sctp_timeout(uint16_t timeout, char *msg, int len)
{
    int idx = 0;
    uint16_t old = g_config.sctp_flow_timeout;
    if (timeout >= TIMEOUT_MAX) {
        if (idx < len)
            idx += snprintf(msg + idx, len - idx, "tcp timeout max num is %d seconds", TIMEOUT_MAX - 1);
        return;
    }
    if (timeout > 0)
        g_config.sctp_flow_timeout = timeout;

    if (idx < len)
        idx += snprintf(msg + idx, len - idx, "sctp old timeout is %u seconds, now is %u seconds\n", old, g_config.sctp_flow_timeout);
}

static void _show_flow_identify_pkt_num(char *msg, int len)
{
    int idx = 0;

    if (idx < len)
        idx += snprintf(msg + idx, len - idx, "tcp  identify pkts num is %u\n", g_config.tcp_identify_pkt_num);
    if (idx < len)
        idx += snprintf(msg + idx, len - idx, "udp  identify pkts num is %u\n", g_config.udp_identify_pkt_num);
    if (idx < len)
        idx += snprintf(msg + idx, len - idx, "sctp identify pkts num is %u\n", g_config.sctp_identify_pkt_num);
}

static void _set_tcp_identify_pkt_num(uint16_t num, char *msg, int len)
{
    int idx = 0;
    uint16_t old = g_config.tcp_identify_pkt_num;

    if (num > 0)
        g_config.tcp_identify_pkt_num = num;

    if (idx < len)
        idx += snprintf(msg + idx, len - idx, "tcp old identify pkt num is %u, now is %u\n", old, g_config.tcp_identify_pkt_num);
}

static void _set_udp_identify_pkt_num(uint16_t num, char *msg, int len)
{
    int idx = 0;
    uint16_t old = g_config.udp_identify_pkt_num;

    if (num > 0)
        g_config.udp_identify_pkt_num = num;

    if (idx < len)
        idx += snprintf(msg + idx, len - idx, "udp old identify pkt num is %u, now is %u\n", old, g_config.udp_identify_pkt_num);
}

static void _set_sctp_identify_pkt_num(uint16_t num, char *msg, int len)
{
    int idx = 0;
    uint16_t old = g_config.sctp_identify_pkt_num;

    if (num > 0)
        g_config.sctp_identify_pkt_num = num;

    if (idx < len)
        idx += snprintf(msg + idx, len - idx, "sctp old identify pkt num is %u, now is %u\n", old, g_config.sctp_identify_pkt_num);
}

static void _set_protocol_identify(const char *recv_str, char *msg, int len, int _set)
{
    int idx = 0;
    const char *tmp;

    if (_set == 0)
        tmp = "disable";
    else
        tmp = "enable";

    if (strcmp(recv_str, "HTTP") == 0)
        g_config.protocol_switch[PROTOCOL_HTTP] = _set;
    else if (strcmp(recv_str, "DNS") == 0)
        g_config.protocol_switch[PROTOCOL_DNS] = _set;
    else if (strcmp(recv_str, "L2TP") == 0)
        g_config.protocol_switch[PROTOCOL_L2TP] = _set;
    else if (strcmp(recv_str, "SSL") == 0)
        g_config.protocol_switch[PROTOCOL_SSL] = _set;
    else if (strcmp(recv_str, "PPTP") == 0)
        g_config.protocol_switch[PROTOCOL_PPTP] = _set;
    else if (strcmp(recv_str, "FTP_CONTROL") == 0)
        g_config.protocol_switch[PROTOCOL_FTP_CONTROL] = _set;
    else if (strcmp(recv_str, "SMTP") == 0)
        g_config.protocol_switch[PROTOCOL_MAIL_SMTP] = _set;
    else if (strcmp(recv_str, "POP") == 0)
        g_config.protocol_switch[PROTOCOL_MAIL_POP] = _set;
    else if (strcmp(recv_str, "IMAP") == 0)
        g_config.protocol_switch[PROTOCOL_MAIL_IMAP] = _set;
    else if (strcmp(recv_str, "SIP") == 0)
        g_config.protocol_switch[PROTOCOL_SIP] = _set;
    else if (strcmp(recv_str, "TELNET") == 0)
        g_config.protocol_switch[PROTOCOL_TELNET] = _set;
    else if (strcmp(recv_str, "SSH") == 0)
        g_config.protocol_switch[PROTOCOL_SSH] = _set;
    else if (strcmp(recv_str, "TFTP") == 0)
        g_config.protocol_switch[PROTOCOL_TFTP] = _set;
    else if (strcmp(recv_str, "RIP") == 0)
        g_config.protocol_switch[PROTOCOL_RIP] = _set;
    else if (strcmp(recv_str, "CLASSICSTUN") == 0)
        g_config.protocol_switch[PROTOCOL_CLASSICSTUN] = _set;
    else if (strcmp(recv_str, "STUN") == 0)
        g_config.protocol_switch[PROTOCOL_STUN] = _set;
    else if (strcmp(recv_str, "MYSQL") == 0)
        g_config.protocol_switch[PROTOCOL_STUN] = _set;
    else if (strcmp(recv_str, "TDS") == 0)
        g_config.protocol_switch[PROTOCOL_STUN] = _set;
    else if (strcmp(recv_str, "WEIXIN") == 0)
        g_config.protocol_switch[PROTOCOL_STUN] = _set;
    else if (strcmp(recv_str, "RADIUS") == 0)
        g_config.protocol_switch[PROTOCOL_STUN] = _set;
    else if (strcmp(recv_str, "DTLS") == 0)
        g_config.protocol_switch[PROTOCOL_STUN] = _set;
    else if (strcmp(recv_str, "H323") == 0)
        g_config.protocol_switch[PROTOCOL_STUN] = _set;
    else if (strcmp(recv_str, "SMB") == 0)
        g_config.protocol_switch[PROTOCOL_SMB] = _set;

    if (idx < len)
        idx += snprintf(msg + idx, len - idx, "%s protocol %s success", tmp, recv_str);

}

static void _disable_protocol_identify(const char *recv_str, char *msg, int len)
{
    _set_protocol_identify(recv_str, msg, len, 0);
}

static void _enable_protocol_identify(const char *recv_str, char *msg, int len)
{
    _set_protocol_identify(recv_str, msg, len, 1);
}

static void _show_protocol_identify(char *msg, int len)
{
    int idx = 0;
    int i;

    for (i = 0; i < PROTOCOL_MAX; i++) {
        if (g_config.protocol_switch[i]) {
            if (idx < len)
                idx += snprintf(msg + idx, len - idx, "%s enable\n", protocol_name_array[i]);
        } else {
            if (idx < len)
                idx += snprintf(msg + idx, len - idx, "%s disable\n", protocol_name_array[i]);
        }
    }
}

static void _show_log_level(char *msg, int len)
{
    int idx = 0;
    char level[32] = {0};
    switch (g_config.log_output_level) {
        case DPI_LOG_DEBUG:
            strncpy(level, "DEBUG", sizeof(level));
            break;

        case DPI_LOG_WARNING:
            strncpy(level, "WARNING", sizeof(level));
            break;

        case DPI_LOG_ERROR:
            strncpy(level, "ERROR", sizeof(level));
            break;

        default:
            break;
    }

    if (idx < len)
        idx += snprintf(msg + idx, len - idx, "system log level is %s\n", level);
}

static void _set_log_level(const char *recv_str, char *msg, int len)
{
    int idx = 0;
    int old = g_config.log_output_level;
    char level[32] = {0};
    switch (old) {
        case DPI_LOG_DEBUG:
            strncpy(level, "DEBUG", sizeof(level));
            break;

        case DPI_LOG_WARNING:
            strncpy(level, "WARNING", sizeof(level));
            break;

        case DPI_LOG_ERROR:
            strncpy(level, "ERROR", sizeof(level));
            break;

        default:
            break;
    }

    if (strcmp(recv_str, "DEBUG") == 0)
        g_config.log_output_level = DPI_LOG_DEBUG;
    else if (strcmp(recv_str, "WARNING") == 0)
        g_config.log_output_level = DPI_LOG_WARNING;
    else if (strcmp(recv_str, "ERROR") == 0)
        g_config.log_output_level = DPI_LOG_ERROR;
    else
        return;

    if (idx < len)
        idx += snprintf(msg + idx, len - idx, "system log old level is %s, now level is %s\n", level, recv_str);

}

static void dpi_stop_rcv_pkts(void)
{
    g_config.start_offline_dissect=1;
    //g_config.stop_rcv_pkts = 1;
}

static void dpi_start_rcv_pkts(void)
{
    g_config.start_offline_dissect=0;
    //g_config.stop_rcv_pkts = 0;
}

static void dpi_exit(void)
{
    unsigned int thread_id;

    g_config.exit = 1;

    for (thread_id = 0; thread_id < g_config.dissector_thread_num; thread_id++) {
        do_all_flow_free(thread_id);
    }

    exit(0);
}

/*
uint64_t _get_now_time(void)
{
    uint64_t cur_tsc = rte_rdtsc();
    uint64_t hz = rte_get_tsc_hz();

    return (cur_tsc / hz);
}
*/

//add by xuxn 统计http解析post的总次数、成功率、失败率
extern volatile uint64_t post_total;    //http 解析.POST文件 总记录条数
extern volatile uint64_t post_success;    //http 解析.POST文件 解析成功的记录条数
static void _show_http_post_info(char *msg, int len)
{
    int idx = 0;
    float succ_rate = 0;
    float fail_rate = 0;
    uint64_t tmp_total = post_total;
    uint64_t tmp_succ = post_success;
    //失败次数
    uint64_t post_fail = tmp_total - tmp_succ;
    if(tmp_total != 0)
    {
        //成功率
        succ_rate = tmp_succ / (tmp_total*1.0);
        //失败率
        fail_rate = post_fail / (tmp_total*1.0);
    }

    if(idx < len)
    {
        int n = snprintf(msg + idx, len - idx, "\nsucc_rate: %llu/%llu == %.2f\nfail_rate: %llu/%llu == %.2f\n",
                            (long long unsigned)tmp_succ, (long long unsigned)tmp_total, succ_rate,
                            (long long unsigned)post_fail,(long long unsigned)tmp_total, fail_rate);
        if(n > 0) idx += n;
    }
}

// extern struct rte_eth_dev rte_eth_devices[RTE_MAX_ETHPORTS];
static void _show_eth_dev_info(char *msg, int len)
{
#if 0
    int ret = 0;
    unsigned i = 0;
    ret += snprintf(msg+ret, len-ret, "%16s %16s %8s %12s %16s %16s %16s %8s %24s\n", "ETH_DEV_INDEX", "BUS_NODE_NAME", "PORT_ID", "NUMA_NODE"
                    , "DRIVER_NAME", "RX_QUEUE_NUM", "TX_QUEUE_NUM", "MTU", "RX_MBUF_ALLOC_FAILED");

    for (i = 0; i < RTE_MAX_ETHPORTS; i++) {
        if (rte_eth_devices[i].state != RTE_ETH_DEV_ATTACHED)
            continue;
        ret += snprintf(msg+ret, len-ret, "%16u %16s %8u %12u %16s %16u %16u %8u %24lu\n"
                , i, rte_eth_devices[i].data->name, rte_eth_devices[i].data->port_id
                , rte_eth_devices[i].data->numa_node, rte_eth_devices[i].device->driver->name, rte_eth_devices[i].data->nb_rx_queues
                , rte_eth_devices[i].data->nb_tx_queues, rte_eth_devices[i].data->mtu, rte_eth_devices[i].data->rx_mbuf_alloc_failed);
    }
#endif
}

static const char* get_rss_type(uint16_t portid)
{
    switch(g_config.rss_use_type[portid])
    {
    case RSS_USE_CONF:
        return "RSS_USE_CONF";
    case RSS_USE_FILTER_CTRL:
        return "RSS_USE_FILTER_CTRL";
    case RSS_USE_CUSTOM:
        return "RSS_USE_CUSTOM";
    default:
        return "UNKNOW_RSS_TYPE";
    }
}

static void _show_port_rss_info(char *msg, int len)
{
#if 0
    int ret = 0;
    unsigned i = 0;
    ret += snprintf(msg+ret, len-ret, "%16s %16s %24s\n", "PORT_ID", "DRIVER_NAME", "RSS_TYPE");

    for (i = 0; i < RTE_MAX_ETHPORTS; i++) {
        if (rte_eth_devices[i].state != RTE_ETH_DEV_ATTACHED)
            continue;
        ret += snprintf(msg+ret, len-ret, "%16u %16s %24s\n", rte_eth_devices[i].data->port_id
                , rte_eth_devices[i].device->driver->name, get_rss_type(rte_eth_devices[i].data->port_id));
    }
#endif
}


//int dpi_module_unit_test(const char *cmd, char *msg, int len);

static void _dpi_module_test(const char *recv_str, char *msg, int len)
{
    int idx = 0;

    //dpi_module_unit_test(recv_str, msg, len);

}

static void _show_rules_info(char *msg, int len)
{
    int i;
    int cnt=0;
    int ret = 0;


    sdt_out_status *value;
    char           *rule_key;
    uint32_t       iter = 0;
    int            retval=0;
    while (rte_hash_iterate(g_sdt_hash_db, (void *) &rule_key,
            (void *) &value, &iter) >= 0)
    {
        if(ret+256>MSG_MAX_LEN){
            ret+=snprintf(msg+ret, len-ret, "\n##still have,but no space left\n");
            break;
        }
        ret+=snprintf(msg+ret, len-ret, "%16s %16s %16s %16s %16s %16s %16s %16s %16s %16s\n",
                                        "unitid", "task_id", "group_id", "rule_id",
                                        "event_time", "max_event_Num", "event_max_time",
                                        "pcap_time",  "pcap_max_size", "pcap_max_time");
        ret+=snprintf(msg+ret, len-ret, "%s %s %s %16u %16u %16u %16u %16u %16lu %16u\n",
            value->match_result->unitID,
            value->match_result->taskID,
            value->match_result->groupID,
            value->match_result->ruleID,
            value->event_status.event_time,
            value->event_status.max_event_num,
            value->event_status.max_time,
            value->pcap_status.pcap_time,
            value->pcap_status.max_pcap_size,
            value->pcap_status.max_time
        );
        ret+=snprintf(msg+ret, len-ret, "%16s %16s %16s %16s %16s\n",
            "report_cnt", "match_hits", "match_pkts",
            "match_bytes", "match_flows");
        ret+=snprintf(msg+ret, len-ret, "%16u %16u %16lu %16lu %16u\n",
            value->statistics_current.rule_report_cnt,
            value->statistics_current.rule_match_hits,
            value->statistics_current.rule_match_pkts,
            value->statistics_current.rule_match_bytes,
            value->statistics_current.rule_match_flows);
        ret+=snprintf(msg+ret, len-ret, "\n\n");
    }


    if (ret == 0){
        ret+=snprintf(msg+ret, len-ret, "no rules acting\n");
    }
}

static void _show_rules_match(char *msg, int len)
{
    int i;
    int cnt=0;
    int ret = 0;

    sdt_out_status *value;
    uint32_t       *rule_key;
    uint32_t       iter = 0;
    int            retval=0;


    if(g_config.sdt_record_match_rule){
        ret+=snprintf(msg+ret, len-ret, "%16s %16s %16s %16s %16s\n",
                "rule_id",
                "match_hits", "match_flows",
                "match_pkts", "match_bytes");
        while (rte_hash_iterate(g_sdt_hash_db, (void *) &rule_key,
                (void *) &value, &iter) >= 0)
        {
            if(value->statistics_current.rule_match_hits<=0){
                continue;
            }

            if(ret+256>MSG_MAX_LEN){
                break;
            }
            ret+=snprintf(msg+ret, len-ret, "%16d %16u %16u %16lu %16lu\n",
                value->match_result->ruleID,
                value->statistics_current.rule_match_hits,
                value->statistics_current.rule_match_flows,
                value->statistics_current.rule_match_pkts,
                value->statistics_current.rule_match_bytes);
        }
    }else{
        ret+=snprintf(msg+ret, len-ret, "%16s %16s\n",
                "rule_id",
                "match_hits");
        while (rte_hash_iterate(g_sdt_hash_db, (void *) &rule_key,
                (void *) &value, &iter) >= 0)
        {
            if(value->statistics_current.rule_match_hits<=0){
                continue;
            }

            if(ret+256>MSG_MAX_LEN){
                break;
            }
            ret+=snprintf(msg+ret, len-ret, "%16d %16u\n",
                value->match_result->ruleID,
                value->statistics_current.rule_match_hits);
            ret+=snprintf(msg+ret, len-ret, "\n");
        }
    }
}

static void _show_rules_id(int rule_id,char *msg, int len)
{
    int i;
    int cnt = 0;
    int ret = 0;

    sdt_out_status *value;
    uint32_t       *rule_key;
    uint32_t       iter = 0;
    int            retval=0;

    if(rte_hash_count(g_sdt_hash_db)<rule_id){
        ret+=snprintf(msg+ret, len-ret, "not exist rule_id:%d\n",rule_id);
        return;
    }

    while (rte_hash_iterate(g_sdt_hash_db, (void *) &rule_key,
            (void *) &value, &iter) >= 0)
    {
        if(rule_id==value->match_result->ruleID){
            ret+=snprintf(msg+ret, len-ret, "%16s %16s %16s %16s %16s %16s %16s\n",
                                "rule_id",
                                "event_time", "max_event_Num", "event_max_time",
                                "pcap_time","pcap_max_size","pcap_max_time");
            ret+=snprintf(msg+ret, len-ret, "%16d %16u %16u %16u %16u %16lu %16u\n",
                value->match_result->ruleID,
                value->event_status.event_time,
                value->event_status.max_event_num,
                value->event_status.max_time,
                value->pcap_status.pcap_time,
                value->pcap_status.max_pcap_size,
                value->pcap_status.max_time
            );
            ret+=snprintf(msg+ret, len-ret, "%16s %16s %16s %16s %16s\n",
                "report_cnt", "match_hits", "match_pkts",
                "match_bytes", "match_flows");
            ret+=snprintf(msg+ret, len-ret, "%16u %16u %16lu %16lu %16u\n",
                value->statistics_current.rule_report_cnt,
                value->statistics_current.rule_match_hits,
                value->statistics_current.rule_match_pkts,
                value->statistics_current.rule_match_bytes,
                value->statistics_current.rule_match_flows);
            ret+=snprintf(msg+ret, len-ret, "\n\n");
            break;
        }
    }

    if (ret == 0){
        ret+=snprintf(msg+ret, len-ret, "not find rule id:%d\n",rule_id);
    }
}

static int do_server_statistic(struct bufferevent *sockfd, const char *str, unsigned int len)
{
    char msg[MSG_MAX_LEN];
    char argv[32];
    int rcv_len;
    uint16_t value;
    int  iv;
    int type = *(const int *)&str[0];

    if (len < sizeof(int))
        return -1;
    switch(type) {
        case MSG_SHOW_THREAD_INFO:
            _show_thread_info(msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

        case MSG_SHOW_FLOW_DETAIL_INFO:
            _show_flow_detail_info(msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

        case MSG_SHOW_FLOW_TOTAL_INFO:
            _show_flow_total_info(msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

        case MSG_SHOW_FLOW_INC_INFO:
            _show_inc_flow(msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

        case MSG_CLEAN_FLOW_TOTAL_INFO:
            _clean_flow_total_info();
            send_str_client(sockfd, "OK", strlen("OK"));
            break;

        case MSG_SHOW_FLOW_HASH_INFO:
            _show_flow_hash_info(msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

        case MSG_SHOW_MEMPOOL_DETAIL_INFO:
            _show_all_mempool_detail_info(msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

        case MSG_SHOW_RING_DETAIL_INFO:
            _show_all_ring_detail_info(msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

        case MSG_SHOW_TRAFFIC_SPEED_INFO:
            _show_traffic_speed_info(msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

        case MSG_SHOW_CURRENT_TRAFFIC_SPEED:
            _show_current_traffic_speed(msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

        case MSG_SHOW_DEV_TRAFFIC_SPEED_INFO:
            _show_dev_traffic_speed_info(msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

        case MSG_SHOW_FLOW_TIMEOUT:
            _show_flow_timeout(msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

        case MSG_SHOW_FLOW_IDENTIFY_PKT_NUM:
            _show_flow_identify_pkt_num(msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

        case MSG_SHOW_FAIL_INFO:
            _show_fail_info(msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

        case MSG_SET_TCP_TIMEOUT:
            value = *(const uint16_t *)&str[4];
            _set_tcp_timeout(value, msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

        case MSG_SET_UDP_TIMEOUT:
            value = *(const uint16_t *)&str[4];
            _set_udp_timeout(value, msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

        case MSG_SET_SCTP_TIMEOUT:
            value = *(const uint16_t *)&str[4];
            _set_sctp_timeout(value, msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

        case MSG_SET_TCP_IDENTIFY_PKT_NUM:
            value = *(const uint16_t *)&str[4];
            _set_tcp_identify_pkt_num(value, msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

        case MSG_SET_UDP_IDENTIFY_PKT_NUM:
            value = *(const uint16_t *)&str[4];
            _set_udp_identify_pkt_num(value, msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

        case MSG_SET_SCTP_IDENTIFY_PKT_NUM:
            value = *(const uint16_t *)&str[4];
            _set_sctp_identify_pkt_num(value, msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

        case MSG_DISABLE_PROTOCOL_IDENTIFY:
            rcv_len = len - sizeof(int) > 31 ? 31 : len - sizeof(int);
            strncpy(argv, str + sizeof(int), rcv_len);
            argv[rcv_len] = 0;
            _disable_protocol_identify(argv, msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

        case MSG_ENABLE_PROTOCOL_IDENTIFY:
            rcv_len = len - sizeof(int) > 31 ? 31 : len - sizeof(int);
            strncpy(argv, str + sizeof(int), rcv_len);
            argv[rcv_len] = 0;
            _enable_protocol_identify(argv, msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

        case MSG_SHOW_PROTOCOL_IDENTIFY:
            _show_protocol_identify(msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

        case MSG_DISABLE_CONVERSATION_IDENTIFY:
            g_config.conversation_switch = 0;
            send_str_client(sockfd, "disable conversation identify", strlen("disable conversation identify"));
            break;

        case MSG_ENABLE_CONVERSATION_IDENTIFY:
            g_config.conversation_switch = 1;
            send_str_client(sockfd, "enable conversation identify", strlen("enable conversation identify"));
            break;

        case MSG_SHOW_LOG_LEVEL:
            _show_log_level(msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

        case MSG_SET_LOG_LEVEL:
            rcv_len = len - sizeof(int) > 31 ? 31 : len - sizeof(int);
            strncpy(argv, str + sizeof(int), rcv_len);
            argv[rcv_len] = 0;
            _set_log_level(argv, msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

        case MSG_STOP_RCV_PKTS:
            send_str_client(sockfd, "stop rcv pkts", strlen("stop rcv pkts"));
            dpi_stop_rcv_pkts();
            break;

        case MSG_START_RCV_PKTS:
            send_str_client(sockfd, "start rcv pkts", strlen("start rcv pkts"));
            dpi_start_rcv_pkts();
            break;

        case MSG_SHOW_TBL_LOG_INFO:
            _show_tbl_log_info(msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;
        case MSG_CLEAN_TBL_LOG_INFO:
            _clean_tbl_log_info();
            send_str_client(sockfd, "OK", strlen("OK"));
            break;

        //add by xuxn
        case MSG_SHOW_HTTP_POST_COUNT:
            _show_http_post_info(msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;

        case MSG_SHOW_ETH_DEV_INFO:
            _show_eth_dev_info(msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;
        case MSG_SHOW_PORT_RSS_INFO:
            _show_port_rss_info(msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;
        case MSG_SHOW_RULES_INFO:
            _show_rules_info(msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;
        case MSG_SHOW_RULES_MATCH:
            _show_rules_match(msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;
       case MSG_SHOW_RULES_ID:
            iv = *(const int *)&str[4];
            _show_rules_id(iv, msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
            break;
        case MSG_NORMAL_EXIT:
            send_str_client(sockfd, "dpi exit", strlen("dpi exit"));
            dpi_exit();
            break;
        case MSG_DPI_TEST_MODULE:
            rcv_len = len - sizeof(int) > 31 ? 31 : len - sizeof(int);
            strncpy(argv, str + sizeof(int), rcv_len);
            argv[rcv_len] = 0;
            _dpi_module_test(argv, msg, MSG_MAX_LEN);
            send_str_client(sockfd, msg, strlen(msg));
        default:
            break;
    }
    return 0;
}

/////////////////////////////
struct context_t
{
    struct      event_base       *base;
    struct      evconnlistener   *listener;
    struct      event            *timer;
    struct      timeval           tv;
    int         index;
    uint64_t    prev_tsc;
};

int enevt_error(int event)
{
    switch(event)
    {
        case BEV_EVENT_CONNECTED:
            return 0;
        case BEV_EVENT_ERROR:
        case BEV_EVENT_EOF:
        case BEV_EVENT_READING:
        case BEV_EVENT_WRITING:
        case BEV_EVENT_TIMEOUT:
        default:
            return 1;
    }
    return 1;
}

void
cb_read(struct bufferevent *bev, void *user)
{
    char                  buff[1024];
    size_t len = bufferevent_read(bev, buff, sizeof(buff));
    do_server_statistic(bev, buff, len);
}

void
cb_event(struct bufferevent *bev, short what, void *connection_)
{
    int                      error = 0;
    int                      length_input = 0;
    int                      length_output = 0;

    error |= enevt_error(what & BEV_EVENT_CONNECTED);
    error |= enevt_error(what & BEV_EVENT_ERROR    );
    error |= enevt_error(what & BEV_EVENT_EOF      );
    error |= enevt_error(what & BEV_EVENT_READING  );
    error |= enevt_error(what & BEV_EVENT_WRITING  );
    error |= enevt_error(what & BEV_EVENT_TIMEOUT  );
    if(error)
    {
        length_input = evbuffer_get_length(bufferevent_get_input(bev));
        if(length_input)
        {
            cb_read(bev, NULL);
        }
        bufferevent_free(bev);
    }
}

static void
cb_accept(struct evconnlistener *listener, int client_fd, struct sockaddr *sa, int salen, void *_ctx)
{
    struct context_t    *ctx        = _ctx;
    struct bufferevent  *bev        = NULL;

    bev = bufferevent_socket_new(ctx->base, client_fd, BEV_OPT_CLOSE_ON_FREE|BEV_OPT_DEFER_CALLBACKS);
    bufferevent_setcb (bev, cb_read, NULL, cb_event, NULL);
    bufferevent_enable(bev, EV_READ|EV_WRITE);
}

static int
chunli_get_time(char *s, int sl)
{
    time_t        time_curr;
    struct   tm  *time_info;

    time(&time_curr);
    time_info = localtime(&time_curr);
    return strftime(s, sl, "%Y-%m-%d %H:%M:%S", time_info);
}

//extern uint64_t hash_index[10];
extern uint64_t write_tbl_log_cnt;
void
cb_timer(evutil_socket_t fd, short what, void *_context)
{
    struct context_t *ctx = _context;
    uint64_t cycle, cur_tsc, diff_tsc;
    ctx->index++;
    cycle = rte_get_timer_hz();   // 一个时钟周期
    cur_tsc  = rte_rdtsc();
    diff_tsc = cur_tsc - ctx->prev_tsc;

    if (diff_tsc > cycle * g_metrics.tick)
    {
        ctx->prev_tsc = cur_tsc;
        dpi_metrics_mbuf();
        dpi_metrics_flow();
    }

    update_global_time();
    update_protocol_record(0);

    if(1==g_config.work_mode_flag){
        rte_timer_manage();
    }

    //debug
    //for(int i = 0; i < 10; i++)
    //{
    //    printf("hash_index[%u]=%zu\n", i, hash_index[i]);
    //}
    //print("\n");

    //再来一次
    evtimer_add(ctx->timer, &ctx->tv);
}


static struct context_t _ctx;

int socket_event_loop(void)
{
    struct context_t *ctx = &_ctx;
    memset(ctx, 0, sizeof(struct context_t));

    int                    socklen = 0;
    struct sockaddr_in     sin;

    //初始化
    ctx->prev_tsc   = rte_rdtsc();

    ctx->base = event_base_new();

    /**** timer ****/
    ctx->tv.tv_sec  = 0;
    ctx->tv.tv_usec = 100*1000; //每间隔0.1秒执行1次
    ctx->timer      = evtimer_new(ctx->base, cb_timer, ctx);
    evtimer_add(ctx->timer, &ctx->tv);

    /**** set_sockaddr  ****/
    socklen = sizeof(sin);
    memset(&sin, 0, socklen);

    sin.sin_port           = htons(g_config.vtysh_socket_port);
    sin.sin_addr.s_addr    = htonl(INADDR_ANY);
    sin.sin_family         = AF_INET;

    /**** register accept  callback ****/
    ctx->listener = evconnlistener_new_bind(ctx->base, cb_accept, ctx,
            LEV_OPT_CLOSE_ON_FREE|LEV_OPT_CLOSE_ON_EXEC|LEV_OPT_REUSEABLE|LEV_OPT_THREADSAFE,
            1024, (struct sockaddr*)&sin, socklen);

    /**** Event Process ****/
    event_base_dispatch(ctx->base);

    /***** FREE ************/
    evconnlistener_free(ctx->listener);
    evtimer_del(ctx->timer);
    event_free (ctx->timer);
    event_base_free (ctx->base);
    ctx->base = NULL;
    return 0;
}

/*
*管理线程的主要工作：
    1，更新全局时间戳
    2，每秒的timer统计函数调用
    3，socket监听，处理消息
*/
void *socket_main(void *args)
{
    socket_event_loop();
    log_trace("socket_main线程退出");
    return NULL;
}

void socket_main_stop(void)
{
    if (_ctx.base)
    {
        event_base_loopbreak(_ctx.base);
    }
}
