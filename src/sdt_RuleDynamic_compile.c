#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <inttypes.h>
#include <errno.h>
#include <netinet/ip.h>
#include <netinet/ip6.h>
#include <arpa/inet.h>

#include <rte_acl.h>
#include <rte_hash.h>
#include <rte_mempool.h>
#include <rte_common.h>
#include <rte_errno.h>

#include <glib.h>

#include <libsdtacl/yasdtacl.h>
#include "sdt_RuleDynamic.h"

#define printf(...)

enum {
    IP6_PROTO,
    IP6_SRC0,
    IP6_SRC1,
    IP6_SRC2,
    IP6_SRC3,
    IP6_DST0,
    IP6_DST1,
    IP6_DST2,
    IP6_DST3,
    IP6_SRCP,
    IP6_DSTP,
    IP6_NUM
};

enum {
    IP4_PROTO,
    IP4_SRC,
    IP4_DST,
    IP4_SRCP,
    IP4_DSTP,
    IP4_NUM
};

RTE_ACL_RULE_DEF(acl4_rules, IP4_NUM);

RTE_ACL_RULE_DEF(acl6_rules, IP6_NUM);

static struct rte_acl_field_def ip4_defs[IP4_NUM] = {
    {
        .type           = RTE_ACL_FIELD_TYPE_BITMASK,
        .size           = sizeof(uint8_t),
        .field_index    = IP4_PROTO,
        .input_index    = IP4_PROTO,
        .offset         = offsetof(struct pkt_tuple_t, proto),
    },
    {
        .type           = RTE_ACL_FIELD_TYPE_MASK,
        .size           = sizeof(uint32_t),
        .field_index    = IP4_SRC,
        .input_index    = IP4_SRC,
        .offset         = offsetof(struct pkt_tuple_t, src),
    },
    {
        .type           = RTE_ACL_FIELD_TYPE_MASK,
        .size           = sizeof(uint32_t),
        .field_index    = IP4_DST,
        .input_index    = IP4_DST,
        .offset         = offsetof(struct pkt_tuple_t, dst),
    },
    {
        .type           = RTE_ACL_FIELD_TYPE_RANGE,
        .size           = sizeof(uint16_t),
        .field_index    = IP4_SRCP,
        .input_index    = IP4_SRCP,
        .offset         = offsetof(struct pkt_tuple_t, sport),
    },
    {
        .type           = RTE_ACL_FIELD_TYPE_RANGE,
        .size           = sizeof(uint16_t),
        .field_index    = IP4_DSTP,
        .input_index    = IP4_SRCP,
        .offset         = offsetof(struct pkt_tuple_t, dport),
    },

};

static struct rte_acl_field_def ip6_defs[IP6_NUM] = {
    {
        .type           = RTE_ACL_FIELD_TYPE_BITMASK,
        .size           = sizeof(uint8_t),
        .field_index    = IP6_PROTO,
        .input_index    = IP6_PROTO,
        .offset         = offsetof(struct pkt_tuple_t, proto),
    },
    {
        .type           = RTE_ACL_FIELD_TYPE_MASK,
        .size           = sizeof(uint32_t),
        .field_index    = IP6_SRC0,
        .input_index    = IP6_SRC0,
        .offset         = offsetof(struct pkt_tuple_t, src[0]),
    },
    {
        .type           = RTE_ACL_FIELD_TYPE_MASK,
        .size           = sizeof(uint32_t),
        .field_index    = IP6_SRC1,
        .input_index    = IP6_SRC1,
        .offset         = offsetof(struct pkt_tuple_t, src[1]),
    },
    {
        .type           = RTE_ACL_FIELD_TYPE_MASK,
        .size           = sizeof(uint32_t),
        .field_index    = IP6_SRC2,
        .input_index    = IP6_SRC2,
        .offset         = offsetof(struct pkt_tuple_t, src[2]),
    },
    {
        .type           = RTE_ACL_FIELD_TYPE_MASK,
        .size           = sizeof(uint32_t),
        .field_index    = IP6_SRC3,
        .input_index    = IP6_SRC3,
        .offset         = offsetof(struct pkt_tuple_t, src[3]),
    },
    {
        .type           = RTE_ACL_FIELD_TYPE_MASK,
        .size           = sizeof(uint32_t),
        .field_index    = IP6_DST0,
        .input_index    = IP6_DST0,
        .offset         = offsetof(struct pkt_tuple_t, dst[0]),
    },
    {
        .type           = RTE_ACL_FIELD_TYPE_MASK,
        .size           = sizeof(uint32_t),
        .field_index    = IP6_DST1,
        .input_index    = IP6_DST1,
        .offset         = offsetof(struct pkt_tuple_t, dst[1]),
    },
    {
        .type           = RTE_ACL_FIELD_TYPE_MASK,
        .size           = sizeof(uint32_t),
        .field_index    = IP6_DST2,
        .input_index    = IP6_DST2,
        .offset         = offsetof(struct pkt_tuple_t, dst[2]),
    },
    {
        .type           = RTE_ACL_FIELD_TYPE_MASK,
        .size           = sizeof(uint32_t),
        .field_index    = IP6_DST3,
        .input_index    = IP6_DST3,
        .offset         = offsetof(struct pkt_tuple_t, dst[3]),
    },
    {
        .type           = RTE_ACL_FIELD_TYPE_RANGE,
        .size           = sizeof(uint16_t),
        .field_index    = IP6_SRCP,
        .input_index    = IP6_SRCP,
        .offset         = offsetof(struct pkt_tuple_t, sport),
    },
    {
        .type           = RTE_ACL_FIELD_TYPE_RANGE,
        .size           = sizeof(uint16_t),
        .field_index    = IP6_DSTP,
        .input_index    = IP6_SRCP,
        .offset         = offsetof(struct pkt_tuple_t, dport),
    },
};

struct rte_acl_ctx* dynamic_dpdk_flex_acl_ipv4_build(const struct acl4_rules *rules, uint32_t rules_nb)
{
    char s[PATH_MAX];
    struct rte_acl_param acl_param;
    struct rte_acl_config acl_build_param;
    struct rte_acl_ctx *ctx = NULL;
    static int index = 0;
    if (NULL == ctx) {
        memset(&acl_param, 0, sizeof(acl_param));
        snprintf(s, sizeof(s), "dynamic_acl4_%u", index++);//仅用于区别 Name
        acl_param.name = s;
        acl_param.socket_id = SOCKET_ID_ANY;
        acl_param.rule_size = RTE_ACL_RULE_SZ(IP4_NUM);
        acl_param.max_rule_num = rules_nb;
        ctx = rte_acl_create(&acl_param);
        if (ctx == NULL) {
            printf("Failed to create ACL context\n");
            printf("ERROR in file:%s +%u\n", __FILE__, __LINE__);
            return NULL;
        }
    } else {
        rte_acl_reset_rules(ctx);
    }

    int rc = rte_acl_add_rules(ctx, (const struct rte_acl_rule *)rules, rules_nb);
    if (rc != 0) {
        printf("add ipv4 acl rules failed\n");
        rte_acl_free(ctx);
        printf("ERROR in file:%s +%u\n", __FILE__, __LINE__);
        return NULL;
    }

    memset(&acl_build_param, 0, sizeof(acl_build_param));
    acl_build_param.num_categories = RTE_ACL_MAX_CATEGORIES;
    acl_build_param.num_fields = IP4_NUM;
    rte_memcpy(&acl_build_param.defs, ip4_defs, sizeof(ip4_defs));
    if (rte_acl_build(ctx, &acl_build_param) != 0) {
        printf("Failed to build ACL trie\n");
        printf("ERROR in file:%s +%u\n", __FILE__, __LINE__);
        rte_acl_free(ctx);
        return NULL;
    }
    return ctx;
}

struct rte_acl_ctx* dynamic_dpdk_flex_acl_ipv6_build(const struct acl6_rules *rules, uint32_t rules_nb)
{
    char s[PATH_MAX];
    struct rte_acl_param acl_param;
    struct rte_acl_config acl_build_param;
    struct rte_acl_ctx *ctx = NULL;
    static int index = 0;
    if (NULL == ctx) {
        memset(&acl_param, 0, sizeof(acl_param));
        snprintf(s, sizeof(s), "dynamic_acl6_%u", index++);//仅用于区别 Name
        acl_param.name = s;
        acl_param.socket_id = SOCKET_ID_ANY;
        acl_param.rule_size = RTE_ACL_RULE_SZ(IP6_NUM);
        acl_param.max_rule_num = rules_nb;
        ctx = rte_acl_create(&acl_param);
        if (ctx == NULL) {
            printf("Failed to create ACL context\n");
            printf("ERROR in file:%s +%u\n", __FILE__, __LINE__);
            return NULL;
        }
    } else {
        rte_acl_reset_rules(ctx);
    }

    int rc = rte_acl_add_rules(ctx, (const struct rte_acl_rule *)rules, rules_nb);
    if (rc < 0) {
        printf("add ipv6 acl rules failed\n");
        printf("ERROR in file:%s +%u\n", __FILE__, __LINE__);
        rte_acl_free(ctx);
        return NULL;
    }

    memset(&acl_build_param, 0, sizeof(acl_build_param));
    acl_build_param.num_categories = RTE_ACL_MAX_CATEGORIES;
    acl_build_param.num_fields = IP6_NUM;
    rte_memcpy(&acl_build_param.defs, ip6_defs, sizeof(ip6_defs));
    if (rte_acl_build(ctx, &acl_build_param) != 0) {
        printf("Failed to build ACL trie\n");
        printf("ERROR in file:%s +%u\n", __FILE__, __LINE__);
        rte_acl_free(ctx);
        return NULL;
    }
    return ctx;
}

static int dynamic_parse_proto_in_acl(uint8_t proto, uint8_t mask, struct rte_acl_field *field)
{
    field[0].value.u8       = proto;
    field[0].mask_range.u8  = mask;
    return 0;
}

static int dynamic_parse_ipv4_addr_in_acl(const uint32_t*ip_4, uint8_t mask,struct rte_acl_field *field)
{
    field[0].value.u32      = ntohl(ip_4[0]);
    field[0].mask_range.u32 = mask;
    return 0;
}

static int dynamic_parse_ipv6_addr_in_acl(const uint32_t *ip_6, uint8_t mask, struct rte_acl_field field[4])
{
    field[0].value.u32      = ntohl(ip_6[0]);
    field[0].mask_range.u32 = (mask > 32) ? 32 : mask;
    mask                    = (mask > 32) ? (mask - 32) : 0;

    field[1].value.u32      = ntohl(ip_6[1]);
    field[1].mask_range.u32 = (mask > 32) ? 32 : mask;
    mask                    = (mask > 32) ? (mask - 32) : 0;

    field[2].value.u32      = ntohl(ip_6[2]);
    field[2].mask_range.u32 = (mask > 32) ? 32 : mask;
    mask                    = (mask > 32) ? (mask - 32) : 0;

    field[3].value.u32      = ntohl(ip_6[3]);
    field[3].mask_range.u32 = (mask > 32) ? 32 : mask;
    mask                    = (mask > 32) ? (mask - 32) : 0;
    return 0;
}

static int dynamic_parse_port_in_acl(uint16_t low, uint16_t high, struct rte_acl_field *field)
{
    field[0].value.u16      = low;
    field[0].mask_range.u16 = high;
    return 0;
}

void dynamic_show_sdt_acl(const struct sdt_acl_t *r, FILE *f)
{
    int af = 0;
    if(AF_INET  == r->af)  af = 4;
    if(AF_INET6 == r->af)  af = 6;

    char ips[32];
    char ipd[32];
    memset(ips, 0, sizeof(ips));
    memset(ipd, 0, sizeof(ipd));

    inet_ntop(r->af, r->src, ips, sizeof(ips));
    inet_ntop(r->af, r->dst, ipd, sizeof(ipd));

    char ports[32];
    if(r->srcport_min == r->srcport_max)            {snprintf(ports, sizeof(ports), "%u", r->srcport_max);}
    else
    if(0==r->srcport_min && 65535==r->srcport_max)  {snprintf(ports, sizeof(ports), "*");}
    else
    if(r->srcport_min != r->srcport_max)            {snprintf(ports, sizeof(ports), "[%u:%u]", r->srcport_min, r->srcport_max);}


    char portd[32];
    if(r->dstport_min == r->dstport_max)            {snprintf(portd, sizeof(portd), "%u", r->dstport_max);}
    else
    if(0==r->dstport_min && 65535==r->dstport_max)  {snprintf(portd, sizeof(portd), "*");}
    else
    if(r->dstport_min != r->dstport_max)            {snprintf(portd, sizeof(portd), "[%u:%u]", r->dstport_min, r->dstport_max);}

    fprintf(f, "%08X %u@%s/%u:%s -> %s/%u:%s CM %u\n",
            r->userdata, r->ip_proto, ips, r->sip_mask, ports, ipd, r->dip_mask,  portd, r->category_mask);
}

//规则格式 转换
int dynamic_convert_rule_to_dpdk(const struct sdt_acl_t * rule, struct rte_acl_rule *v)
{
    struct rte_acl_field *fields = v->field;

    if (rule->af == AF_INET6) {
        dynamic_parse_proto_in_acl(rule->ip_proto, rule->proto_mask, fields+IP6_PROTO);   /* ip proto */
        dynamic_parse_ipv6_addr_in_acl(rule->src, rule->sip_mask, fields+IP6_SRC0);       /* srcIp */
        dynamic_parse_ipv6_addr_in_acl(rule->dst, rule->dip_mask, fields+IP6_DST0);       /* dstIp */
        dynamic_parse_port_in_acl(rule->srcport_min, rule->srcport_max, fields+IP6_SRCP); /* src port */
        dynamic_parse_port_in_acl(rule->dstport_min, rule->dstport_max, fields+IP6_DSTP); /* dst port */
    } else if (rule->af == AF_INET) {
        dynamic_parse_proto_in_acl(rule->ip_proto, rule->proto_mask, fields+IP4_PROTO);   /* ip proto*/
        dynamic_parse_ipv4_addr_in_acl(rule->src, rule->sip_mask, fields+IP4_SRC);        /* srcIp */
        dynamic_parse_ipv4_addr_in_acl(rule->dst, rule->dip_mask, fields+IP4_DST);        /* dstIp */
        dynamic_parse_port_in_acl(rule->srcport_min, rule->srcport_max, fields+IP4_SRCP); /* src port */
        dynamic_parse_port_in_acl(rule->dstport_min, rule->dstport_max, fields+IP4_DSTP); /* dst port */
    }

    v->data.userdata        = rule->userdata;       /* 规则命中之后返回值 */
    v->data.category_mask   = rule->category_mask;  /* 规则分类 */
    v->data.priority        = rule->priority % RTE_ACL_MAX_PRIORITY; // 不可过大/过小
    return 0;
}



struct rte_acl_ctx* dynamic_rule_build_ipv4_flex(struct sdt_acl_t *acls[], int num)
{
    if(0 == num)
    {
        return NULL;
    }

    //构造内存空间
    struct acl4_rules *rte_acl_rules = calloc(num, sizeof(struct acl4_rules));
    memset(rte_acl_rules, 0, num * sizeof(struct acl4_rules));

    //转换
    for(int i = 0; i < num; i++)
    {
        struct sdt_acl_t *rule = acls[i];
        //dynamic_show_sdt_acl(rule, stdout);
        dynamic_convert_rule_to_dpdk(rule, (struct rte_acl_rule *)(rte_acl_rules+i));
    }

    //编译
    struct rte_acl_ctx *ctx = dynamic_dpdk_flex_acl_ipv4_build((const struct acl4_rules *)rte_acl_rules, num);

    //释放内存空间
    free(rte_acl_rules);

    if (NULL == ctx)
    {
        printf("ERROR in file:%s +%u\n", __FILE__, __LINE__);
        return NULL;
    }

    return ctx;
}

struct rte_acl_ctx* dynamic_rule_build_ipv6_flex(struct sdt_acl_t *acls[], int num)
{
    if(0 == num)
    {
        return NULL;
    }

    //构造内存空间
    struct acl6_rules *rte_acl_rules = calloc(num, sizeof(struct acl6_rules));
    memset(rte_acl_rules, 0, num * sizeof(struct acl6_rules));

    //转换
    for(int i = 0; i < num; i++)
    {
        struct sdt_acl_t *rule = acls[i];
        //dynamic_show_sdt_acl(rule, stdout);
        dynamic_convert_rule_to_dpdk(rule, (struct rte_acl_rule *)(rte_acl_rules+i));
    }

    //编译
    struct rte_acl_ctx *ctx = dynamic_dpdk_flex_acl_ipv6_build((const struct acl6_rules *)rte_acl_rules, num);

    //释放内存空间
    free(rte_acl_rules);

    if (NULL == ctx)
    {
        printf("ERROR in file:%s +%u\n", __FILE__, __LINE__);
        return NULL;
    }

    return ctx;
}

int dynamic_rule_acl_match(const struct rte_acl_ctx *ctx, const struct pkt_tuple_t *pkt, uint32_t *match_result, int result_size)
{
    if(NULL == ctx)
    {
        return 0;
    }

    if(unlikely(result_size < RTE_ACL_MAX_CATEGORIES))
    {
        printf("ERROR in file:%s +%u\n", __FILE__, __LINE__);
        return 0;
    }

    memset(match_result, 0, sizeof(uint32_t) * result_size);
    int ret = rte_acl_classify_alg(ctx, (const uint8_t**)&pkt, (uint32_t*)match_result, 1, RTE_ACL_MAX_CATEGORIES, RTE_ACL_CLASSIFY_DEFAULT);
    if (ret < 0)
    {
        printf("ERROR in file:%s +%u\n", __FILE__, __LINE__);
        return -1;
    }

    return 0;
}
