/****************************************************************************************
 * 文 件 名 : dpi_coap.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: huangzw              2025/09/02
编码: huangzw            2025/09/02
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <netinet/in.h>
#include <stdio.h>
#include <glib.h>
#include <arpa/inet.h>
#include <rte_mempool.h>
#include <yaProtoRecord/precord_schema.h>

#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_tbl_log.h"
#include "dpi_typedefs.h"
#include "dpi_common.h"
#include "dpi_pschema.h"

#define COAP_VERSION_MASK                           0xC0
#define COAP_TYPE_MASK                              0x30
#define COAP_TOKEN_LEN_MASK                         0x0F
#define COAP_BLOCK_MFLAG_MASK                       0x08
#define COAP_BLOCK_SIZE_MASK                        0x07
#define COAP_OBJECT_SECURITY_NON_COMPRESSED_MASK    0x80
#define COAP_OBJECT_SECURITY_EXPAND_MASK            0x40
#define COAP_OBJECT_SECURITY_SIGNATURE_MASK         0x20
#define COAP_OBJECT_SECURITY_KID_CONTEXT_MASK       0x10
#define COAP_OBJECT_SECURITY_KID_MASK               0x08
#define COAP_OBJECT_SECURITY_PIVLEN_MASK            0x07


#define COAP_OPT_IF_MATCH       1
#define COAP_OPT_URI_HOST       3
#define COAP_OPT_ETAG           4
#define COAP_OPT_IF_NONE_MATCH      5
#define COAP_OPT_OBSERVE        6   /* core-observe-16 */
#define COAP_OPT_URI_PORT       7
#define COAP_OPT_LOCATION_PATH      8
#define COAP_OPT_URI_PATH       11
#define COAP_OPT_CONTENT_TYPE       12
#define COAP_OPT_MAX_AGE        14
#define COAP_OPT_URI_QUERY      15
#define COAP_OPT_ACCEPT         17
#define COAP_OPT_LOCATION_QUERY     20
#define COAP_OPT_OBJECT_SECURITY    21  /* value used in OSCORE plugtests */
#define COAP_OPT_BLOCK2         23  /* core-block-10 */
#define COAP_OPT_BLOCK_SIZE     28  /* core-block-10 */
#define COAP_OPT_BLOCK1         27  /* core-block-10 */
#define COAP_OPT_PROXY_URI      35
#define COAP_OPT_PROXY_SCHEME       39
#define COAP_OPT_SIZE1          60


extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;
gpointer white_filter(const char* servername, uint16_t len, GHashTable *whitelist_table);

typedef struct _value_string {
    uint32_t      value;
    const char*   strptr;
} value_string;

static inline const char* value2String(size_t value, const value_string *KVList)
{
    while(KVList->strptr)
    {
        if(value ==  KVList->value)
            return KVList->strptr;
        KVList++;
    }
    return "";
}

static const value_string vals_opt_type[] = {
    { COAP_OPT_IF_MATCH,       "If-Match" },
    { COAP_OPT_URI_HOST,       "Uri-Host" },
    { COAP_OPT_ETAG,           "Etag" },
    { COAP_OPT_IF_NONE_MATCH,  "If-None-Match" },
    { COAP_OPT_URI_PORT,       "Uri-Port" },
    { COAP_OPT_LOCATION_PATH,  "Location-Path" },
    { COAP_OPT_URI_PATH,       "Uri-Path" },
    { COAP_OPT_CONTENT_TYPE,   "Content-Format" },
    { COAP_OPT_MAX_AGE,        "Max-age" },
    { COAP_OPT_URI_QUERY,      "Uri-Query" },
    { COAP_OPT_ACCEPT,         "Accept" },
    { COAP_OPT_LOCATION_QUERY, "Location-Query" },
    { COAP_OPT_OBJECT_SECURITY,"Object-Security" },
    { COAP_OPT_PROXY_URI,      "Proxy-Uri" },
    { COAP_OPT_PROXY_SCHEME,   "Proxy-Scheme" },
    { COAP_OPT_SIZE1,          "Size1" },
    { COAP_OPT_OBSERVE,        "Observe" },
    { COAP_OPT_BLOCK2,         "Block2" },
    { COAP_OPT_BLOCK1,         "Block1" },
    { COAP_OPT_BLOCK_SIZE,     "Block Size" },
    { 0, NULL },
};

struct coap_option_range_t {
    uint32_t type;
    uint32_t min;
    uint32_t max;
} coi[] = {
    { COAP_OPT_IF_MATCH,        0,   8 },
    { COAP_OPT_URI_HOST,        1, 255 },
    { COAP_OPT_ETAG,            1,   8 },
    { COAP_OPT_IF_NONE_MATCH,   0,   0 },
    { COAP_OPT_URI_PORT,        0,   2 },
    { COAP_OPT_LOCATION_PATH,   0, 255 },
    { COAP_OPT_URI_PATH,        0, 255 },
    { COAP_OPT_CONTENT_TYPE,    0,   2 },
    { COAP_OPT_MAX_AGE,         0,   4 },
    { COAP_OPT_URI_QUERY,       1, 255 },
    { COAP_OPT_ACCEPT,          0,   2 },
    { COAP_OPT_LOCATION_QUERY,  0, 255 },
    { COAP_OPT_OBJECT_SECURITY, 0, 255 },
    { COAP_OPT_PROXY_URI,       1,1034 },
    { COAP_OPT_PROXY_SCHEME,    1, 255 },
    { COAP_OPT_SIZE1,           0,   4 },
    { COAP_OPT_OBSERVE,         0,   3 },
    { COAP_OPT_BLOCK2,          0,   3 },
    { COAP_OPT_BLOCK1,          0,   3 },
    { COAP_OPT_BLOCK_SIZE,      0,   4 },
};

static const value_string vals_ctype[] = {
    {  0, "text/plain; charset=utf-8" },
    { 40, "application/link-format" },
    { 41, "application/xml" },
    { 42, "application/octet-stream" },
    { 47, "application/exi" },
    { 50, "application/json" },
    { 60, "application/cbor" },
    { 1542, "application/vnd.oma.lwm2m+tlv" },
    { 1543, "application/vnd.oma.lwm2m+json" },
    { 11542, "application/vnd.oma.lwm2m+tlv" },
    { 11543, "application/vnd.oma.lwm2m+json" },
    { 0, NULL },
};

static const value_string vals_ttype_short[] = {
    { 0, "CON" },
    { 1, "NON" },
    { 2, "ACK" },
    { 3, "RST" },
    { 0, NULL },
};

static const value_string vals_code[] = {
    { 0, "Empty Message" },

    /* method code */
    { 1, "GET" },
    { 2, "POST" },
    { 3, "PUT" },
    { 4, "DELETE" },
    { 5, "FETCH" },     /* RFC8132 */
    { 6, "PATCH" },     /* RFC8132 */
    { 7, "iPATCH" },    /* RFC8132 */

    /* response code */
    {  65, "2.01 Created" },
    {  66, "2.02 Deleted" },
    {  67, "2.03 Valid" },
    {  68, "2.04 Changed" },
    {  69, "2.05 Content" },
    {  95, "2.31 Continue" },
    { 128, "4.00 Bad Request" },
    { 129, "4.01 Unauthorized" },
    { 130, "4.02 Bad Option" },
    { 131, "4.03 Forbidden" },
    { 132, "4.04 Not Found" },
    { 133, "4.05 Method Not Allowed" },
    { 134, "4.06 Not Acceptable" },
    { 136, "4.08 Request Entity Incomplete" },  /* core-block-10 */
    { 137, "4.09 Conflict" },           /* RFC8132 */
    { 140, "4.12 Precondition Failed" },
    { 141, "4.13 Request Entity Too Large" },
    { 143, "4.15 Unsupported Content-Format" },
    { 150, "4.22 Unprocessable Entity" },       /* RFC8132 */
    { 160, "5.00 Internal Server Error" },
    { 161, "5.01 Not Implemented" },
    { 162, "5.02 Bad Gateway" },
    { 163, "5.03 Service Unavailable" },
    { 164, "5.04 Gateway Timeout" },
    { 165, "5.05 Proxying Not Supported" },

    { 0, NULL },
};


struct coap_info
{
    uint8_t req_flag;
    uint8_t req_type;
    uint8_t req_code;
    char req_option_host[256];
    uint16_t req_option_port;
    char req_path[512];
    char req_uri[512];
    char req_option_proxy_uri[256];
    char req_option_proxy_scheme[256];
    char req_option_content_format[256];
    uint8_t resp_type;
    uint8_t resp_code;
    char resp_option_location_path[256];
    char resp_option_location_query[256];
    char resp_option_content_format[256];
};

typedef enum  _coap_index_em{
    EM_COAP_REQ_TYPE,
    EM_COAP_REQ_CODE,
    EM_COAP_REQ_OPTION_HOST,
    EM_COAP_REQ_OPTION_PORT,
    EM_COAP_REQ_PATH,
    EM_COAP_REQ_URI,
    EM_COAP_REQ_OPTION_PROXY_URI,
    EM_COAP_REQ_OPTION_PROXY_SCHEME,
    EM_COAP_REQ_OPTION_CONTENT_FORMAT,
    EM_COAP_RESP_TYPE,
    EM_COAP_RESP_CODE,
    EM_COAP_RESP_OPTION_LOCATION_PATH,
    EM_COAP_RESP_OPTION_LOCATION_QUERY,
    EM_COAP_RESP_OPTION_CONTENT_FORMAT,

    EM_COAP_MAX
}coap_index_em;


static dpi_field_table  coap_field_array[] = {
    DPI_FIELD_D(EM_COAP_REQ_TYPE,                            YA_FT_UINT8,            "coap_req_type"),
    DPI_FIELD_D(EM_COAP_REQ_CODE,                            YA_FT_STRING,           "coap_req_code"),
    DPI_FIELD_D(EM_COAP_REQ_OPTION_HOST,                     YA_FT_STRING,           "coap_req_option_host"),
    DPI_FIELD_D(EM_COAP_REQ_OPTION_PORT,                     YA_FT_UINT16,           "coap_req_option_port"),
    DPI_FIELD_D(EM_COAP_REQ_PATH,                            YA_FT_STRING,           "coap_req_path"),
    DPI_FIELD_D(EM_COAP_REQ_URI,                             YA_FT_STRING,           "coap_req_uri"),
    DPI_FIELD_D(EM_COAP_REQ_OPTION_PROXY_URI,                YA_FT_STRING,           "coap_req_option_proxy_uri"),
    DPI_FIELD_D(EM_COAP_REQ_OPTION_PROXY_SCHEME,             YA_FT_STRING,           "coap_req_option_proxy_scheme"),
    DPI_FIELD_D(EM_COAP_REQ_OPTION_CONTENT_FORMAT,           YA_FT_STRING,           "coap_req_option_content_format"),
    DPI_FIELD_D(EM_COAP_RESP_TYPE,                           YA_FT_UINT8,            "coap_resp_type"),
    DPI_FIELD_D(EM_COAP_RESP_CODE,                           YA_FT_STRING,           "coap_resp_code"),
    DPI_FIELD_D(EM_COAP_RESP_OPTION_LOCATION_PATH,           YA_FT_STRING,           "coap_resp_option_location_path"),
    DPI_FIELD_D(EM_COAP_RESP_OPTION_LOCATION_QUERY,          YA_FT_STRING,           "coap_resp_option_location_query"),
    DPI_FIELD_D(EM_COAP_RESP_OPTION_CONTENT_FORMAT,          YA_FT_STRING,           "coap_resp_option_content_format"),
};





/*
*coap的识别
*/
void identify_coap(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len)
{
    if (g_config.protocol_switch[PROTOCOL_COAP] == 0)
        return;

    uint16_t s_port = 0, d_port = 0;

    if(flow->tuple.inner.proto == IPPROTO_UDP) {
        s_port = ntohs(flow->tuple.inner.port_src);
        d_port = ntohs(flow->tuple.inner.port_dst);
    } else {
        DPI_ADD_PROTOCOL_TO_BITMASK(flow->excluded_protocol_bitmask, PROTOCOL_COAP);
        return;
    }

    if(s_port == 5683 || d_port == 5683)
        flow->real_protocol_id = PROTOCOL_COAP;

    return;

}


static int write_coap_log(struct flow_info *flow, int direction, void *field_info, SdtMatchResult *match_result)
{
    int idx = 0,i=0;
    struct tbl_log *log_ptr;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return PKT_OK;
    }

    struct coap_info *info=(struct coap_info *)field_info;
    if(!info){
        return PKT_DROP;
    }

    char str[512];
    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, match_result);
    player_t *layer = precord_layer_put_new_layer(log_ptr->record, "coap");

    for(i=0; i<EM_COAP_MAX;i++){
        switch(i) {
        case EM_COAP_REQ_TYPE:
	    if (info->req_flag) {
	        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->req_type);
	    } else
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL,1);
	    break;
        case EM_COAP_REQ_CODE:
	    if (info->req_flag) {
	        const char *req_code_str = value2String(info->req_code, vals_code);
	        if(req_code_str){
                    write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, coap_field_array[i].type, (const uint8_t *)req_code_str, strlen(req_code_str));
	        }
	    } else
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL,1);
	    break;
        case EM_COAP_REQ_OPTION_HOST:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, coap_field_array[i].type, (const uint8_t *)info->req_option_host, strlen(info->req_option_host));
            break;
        case EM_COAP_REQ_OPTION_PORT:
	    if(info->req_option_port) {
                 write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, coap_field_array[i].type, NULL, info->req_option_port);
	    } else
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL,1);
            break;
        case EM_COAP_REQ_PATH:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, coap_field_array[i].type, (const uint8_t *)info->req_path, strlen(info->req_path));
            break;
        case EM_COAP_REQ_URI:
	    if(info->req_uri[0] != '\0') {
	        memset(str, 0, sizeof(str));
	        snprintf(str, sizeof(str), "%s?%s", info->req_path, info->req_uri);
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, coap_field_array[i].type, (const uint8_t *)str, strlen(str));
	    } else
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, coap_field_array[i].type, (const uint8_t *)info->req_path, strlen(info->req_path));
            break;
        case EM_COAP_REQ_OPTION_PROXY_URI:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, coap_field_array[i].type, (const uint8_t *)info->req_option_proxy_uri, strlen(info->req_option_proxy_uri));
            break;
        case EM_COAP_REQ_OPTION_PROXY_SCHEME:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, coap_field_array[i].type, (const uint8_t *)info->req_option_proxy_scheme, strlen(info->req_option_proxy_scheme));
            break;
        case EM_COAP_REQ_OPTION_CONTENT_FORMAT:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, coap_field_array[i].type, (const uint8_t *)info->req_option_content_format, strlen(info->req_option_content_format));
            break;
        case EM_COAP_RESP_TYPE:
	    if(info->req_flag != 1) {
	        write_one_num_reconds(log_ptr->record, &idx, TBL_LOG_MAX_LEN, info->resp_type);
	    } else
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL,1);
	    break;
        case EM_COAP_RESP_CODE:
	    if(info->req_flag != 1) {
	        const char *resp_code_str = value2String(info->resp_code, vals_code);
	        if(resp_code_str){
                    write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, coap_field_array[i].type, (const uint8_t *)resp_code_str, strlen(resp_code_str));
	        }
	    } else
                write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL,1);
	    break;
        case EM_COAP_RESP_OPTION_LOCATION_PATH:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, coap_field_array[i].type, (const uint8_t *)info->resp_option_location_path, strlen(info->resp_option_location_path));
            break;
        case EM_COAP_RESP_OPTION_LOCATION_QUERY:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, coap_field_array[i].type, (const uint8_t *)info->resp_option_location_query, strlen(info->resp_option_location_query));
            break;
        case EM_COAP_RESP_OPTION_CONTENT_FORMAT:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, coap_field_array[i].type, (const uint8_t *)info->resp_option_content_format, strlen(info->resp_option_content_format));
            break;
        default:
            write_coupler_log(log_ptr->record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL,1);
            break;
        }
    }

    log_ptr->thread_id   = flow->thread_id;
    log_ptr->log_type    = TBL_LOG_COAP;
    log_ptr->log_len     = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;

    if (write_tbl_log(log_ptr) != 1)
    {
        sdt_precord_destroy(log_ptr->record);
        log_ptr->record = NULL;
        rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
    }

    return 1;
}

static uint32_t coap_get_opt_uint(const uint8_t *payload, uint32_t offset, uint32_t length)
{
    switch (length) {
    case 0:
        return 0;
    case 1:
        return (uint32_t)get_uint8_t(payload, offset);
    case 2:
        return (uint32_t)get_uint16_ntohs(payload, offset);
    case 3:
        return (uint32_t)get_uint32_ntohl(payload, offset) >> 8;
    case 4:
        return (uint32_t)get_uint32_ntohl(payload, offset);
    default:
        return -1;
    }
}

static int coap_opt_check(uint8_t opt_num, uint32_t opt_length)
{
    int i;

    for (i = 0; i < (int)(array_length(coi)); i++) {
    if (coi[i].type == opt_num)
        break;
    }
    if (i == (int)(array_length(coi))) {
        //"Invalid Option Number %u", opt_num);
        return -1;
    }
    if (opt_length < coi[i].min || opt_length > coi[i].max) {
        //"Invalid Option Range: %d (%d < x < %d)", opt_length, coi[i].min, coi[i].max);
        return -1;
    }

    return 0;
}


int dissect_coap_options_main(const uint8_t *payload, const uint32_t payload_len, uint32_t offset, uint8_t opt_count, uint8_t *opt_num, uint32_t offset_end, struct coap_info *coinfo)
{
    uint8_t    opt_jump;
    uint32_t   opt_length, opt_length_ext, opt_delta, opt_delta_ext;
    uint32_t   opt_length_ext_off = 0;
    uint8_t    opt_length_ext_len = 0;
    uint32_t   opt_delta_ext_off  = 0;
    uint8_t    opt_delta_ext_len  = 0;
    uint32_t   orig_offset        = offset;
    char       strbuf[56];

    opt_jump = get_uint8_t(payload, offset);
    if (0xff == opt_jump)
        return offset;
    offset += 1;

    /*
     * section 3.1 in coap-17:
     * Option Delta:  4-bit unsigned integer.  A value between 0 and 12
     * indicates the Option Delta.  Three values are reserved for special
     * constructs:
     *
     * 13:  An 8-bit unsigned integer follows the initial byte and
     *      indicates the Option Delta minus 13.
     *
     * 14:  A 16-bit unsigned integer in network byte order follows the
     *      initial byte and indicates the Option Delta minus 269.
     *
     * 15:  Reserved for the Payload Marker.  If the field is set to this
     *      value but the entire byte is not the payload marker, this MUST
     *      be processed as a message format error.
     */
    switch (opt_jump & 0xf0) {
        case 0xd0:
        opt_delta_ext = get_uint8_t(payload, offset);
        opt_delta_ext_off = offset;
        opt_delta_ext_len = 1;
        offset += 1;

        opt_delta = 13;
        opt_delta += opt_delta_ext;
        break;
    case 0xe0:
        opt_delta_ext = coap_get_opt_uint(payload, offset, 2);
        opt_delta_ext_off = offset;
        opt_delta_ext_len = 2;
        offset += 2;

        opt_delta = 269;
        opt_delta += opt_delta_ext;
        break;
    case 0xf0:
        //"end-of-options marker found, but option length isn't 15"
        return -1;
    default:
        opt_delta = ((opt_jump & 0xf0) >> 4);
        break;
    }
    *opt_num += opt_delta;

    /*
     * section 3.1 in coap-17:
     * Option Length:  4-bit unsigned integer.  A value between 0 and 12
     * indicates the length of the Option Value, in bytes.  Three values
     * are reserved for special constructs:
     *
     * 13:  An 8-bit unsigned integer precedes the Option Value and
     *      indicates the Option Length minus 13.
     *
     * 14:  A 16-bit unsigned integer in network byte order precedes the
     *      Option Value and indicates the Option Length minus 269.
     *
     * 15:  Reserved for future use.  If the field is set to this value,
     *      it MUST be processed as a message format error.
     */
    switch (opt_jump & 0x0f) {
    case 0x0d:
        opt_length_ext = get_uint8_t(payload, offset);
        opt_length_ext_off = offset;
        opt_length_ext_len = 1;
        offset += 1;

        opt_length  = 13;
        opt_length += opt_length_ext;
        break;
    case 0x0e:
        opt_length_ext = coap_get_opt_uint(payload, offset, 2);
        opt_length_ext_off = offset;
        opt_length_ext_len = 2;
        offset += 2;

        opt_length  = 269;
        opt_length += opt_length_ext;
        break;
    case 0x0f:
        //"end-of-options marker found, but option delta isn't 15"
        return -1;
    default:
        opt_length = (opt_jump & 0x0f);
        break;
    }
    if (opt_length > offset_end - offset) {
        //"option longer than the package"
        return -1;
    }

    coap_opt_check(*opt_num, opt_length);

    char __str[256];
    switch (*opt_num) {
        case COAP_OPT_CONTENT_TYPE:
        {
            int         ctype = coap_get_opt_uint(payload, offset, opt_length);
            const char *ctype_str = value2String(ctype, vals_ctype);
            if (coinfo->req_flag) {
                if (strlen(ctype_str) > 0) {
                    snprintf(
                        coinfo->req_option_content_format, sizeof(coinfo->req_option_content_format), "%u(%s)", ctype, ctype_str);
                } else {
                    snprintf(
                        coinfo->req_option_content_format, sizeof(coinfo->req_option_content_format), "%u(Unknown Type)", ctype);
                }
            } else {
                if (strlen(ctype_str) > 0) {
                    snprintf(coinfo->resp_option_content_format, sizeof(coinfo->resp_option_content_format), "%u(%s)", ctype,
                        ctype_str);
                } else {
                    snprintf(coinfo->resp_option_content_format, sizeof(coinfo->resp_option_content_format), "%u(Unknown Type)",
                        ctype);
                }
            }
            break;
        }
    case COAP_OPT_PROXY_URI:
        if(coinfo->req_flag) {
	    memcpy(coinfo->req_option_proxy_uri, payload+offset, opt_length>(sizeof(coinfo->req_option_proxy_uri)-1)?(sizeof(coinfo->req_option_proxy_uri)-1):opt_length);
	}
	break;
    case COAP_OPT_PROXY_SCHEME:
        if(coinfo->req_flag) {
	    memcpy(coinfo->req_option_proxy_scheme, payload+offset, opt_length>(sizeof(coinfo->req_option_proxy_scheme)-1)?(sizeof(coinfo->req_option_proxy_scheme)-1):opt_length);
	}
	break;
    case COAP_OPT_URI_HOST:
        if(coinfo->req_flag) {
	   
	    memcpy(coinfo->req_option_host, payload+offset, opt_length>(sizeof(coinfo->req_option_host)-1)?(sizeof(coinfo->req_option_host)-1):opt_length);
	}
	break;
    case COAP_OPT_LOCATION_PATH:
        if(!coinfo->req_flag) {
	    memcpy(coinfo->resp_option_location_path, payload+offset, opt_length>(sizeof(coinfo->resp_option_location_path)-1)?(sizeof(coinfo->resp_option_location_path)-1):opt_length);
	}
	break;
    case COAP_OPT_URI_PORT:
        coinfo->req_option_port = coap_get_opt_uint(payload, offset, opt_length);
	break;
    case COAP_OPT_LOCATION_QUERY:
        if(!coinfo->req_flag){
	    memcpy(coinfo->resp_option_location_query, payload+offset, opt_length>(sizeof(coinfo->resp_option_location_query)-1)?(sizeof(coinfo->resp_option_location_query)-1):opt_length);
	}
	break;
    case COAP_OPT_URI_PATH:
        if(coinfo->req_flag) {
	    memset(__str, 0, sizeof(__str));
	    memcpy(__str, payload+offset, opt_length>(sizeof(__str)-1)?(sizeof(__str)-1):opt_length);
            uint32_t uri_path_len = strlen(coinfo->req_path);
	    if(uri_path_len+strlen(__str) < sizeof(coinfo->req_path)-1) {
	        if(coinfo->req_path[0] == '\0')
	            snprintf(coinfo->req_path+uri_path_len, sizeof(coinfo->req_path) - uri_path_len, "%s", __str);
		else
	            snprintf(coinfo->req_path+uri_path_len, sizeof(coinfo->req_path) - uri_path_len, "/%s", __str);
	    }
	}
	break;
    case COAP_OPT_URI_QUERY:
        if(coinfo->req_flag) {
	    memset(__str, 0, sizeof(__str));
	    memcpy(__str, payload+offset, opt_length>(sizeof(__str)-1)?(sizeof(__str)-1):opt_length);
            uint32_t uri_len = strlen(coinfo->req_uri);
	    if(uri_len+strlen(__str) < sizeof(coinfo->req_uri)-1) {
	        if(coinfo->req_uri[0] == '\0')
	            snprintf(coinfo->req_uri+uri_len, sizeof(coinfo->req_uri) - uri_len, "%s", __str);
		else
	            snprintf(coinfo->req_uri+uri_len, sizeof(coinfo->req_uri) - uri_len, "&%s", __str);
	    }
	}
	break;

    }

    return offset + opt_length;
}


/*
 * options dissector.
 * return offset pointing the next of options. (i.e. the top of the paylaod
 * or the end of the data.
 */
 
int dissect_coap_options(const uint8_t *payload, const uint32_t payload_len, uint32_t offset, uint32_t offset_end, struct coap_info *coinfo)
{
    uint8_t opt_num = 0; 
    int    i;   
    uint8_t endmarker;

    /* loop for dissecting options */
    for (i = 1; offset < offset_end; i++) {
        offset = dissect_coap_options_main(payload, payload_len, offset, i, &opt_num, offset_end, coinfo);
        if ((int)offset == -1)
            return -1;
        if (offset >= offset_end)
            break;
        endmarker = get_uint8_t(payload, offset);
        if (endmarker == 0xff) {
            offset += 1;
            break;
        }
    }

    return offset;
}


int dissect_coap(struct flow_info *flow, int direction, uint32_t seq, const uint8_t *payload, const uint32_t payload_len, uint8_t flag)
{
    UNUSED(flag);
    UNUSED(seq);


    uint32_t offset = 0;
    uint8_t ttype = 0;
    uint8_t token_len = 0;
    uint8_t code = 0, code_class = 0;
    struct coap_info info;

    memset(&info, 0, sizeof(info));

    uint8_t version = (get_uint8_t(payload, offset) & COAP_VERSION_MASK) >> 6;
    ttype = (get_uint8_t(payload, offset) & COAP_TYPE_MASK) >> 4;
    token_len = get_uint8_t(payload, offset) & COAP_TOKEN_LEN_MASK;
    offset += 1;
    code = get_uint8_t(payload, offset);
    offset += 1;
    offset += 2; //mid
    if(code < 8 && code > 0){
        info.req_flag = 1;
	info.req_type = ttype;
	info.req_code = code;
    } else {
	info.resp_type = ttype;
	info.resp_code = code;
    }

    if (token_len > 0)
        offset += token_len;

    offset = dissect_coap_options(payload, payload_len, offset, payload_len, &info);


    write_coap_log(flow, direction, &info, NULL);

    return PKT_OK;
}


static void init_coap_dissector(void)
{
    dpi_register_proto_schema(coap_field_array, EM_COAP_MAX, "coap");

    port_add_proto_head(IPPROTO_UDP, 5683, PROTOCOL_COAP);
    udp_detection_array[PROTOCOL_COAP].proto = PROTOCOL_COAP;
    udp_detection_array[PROTOCOL_COAP].identify_func = identify_coap;
    udp_detection_array[PROTOCOL_COAP].dissect_func = dissect_coap;
    DPI_SAVE_AS_BITMASK(udp_detection_array[PROTOCOL_COAP].excluded_protocol_bitmask, PROTOCOL_COAP);


    map_fields_info_register(coap_field_array, PROTOCOL_COAP, EM_COAP_MAX, "coap");
    return;
}


static __attribute((constructor)) void before_init_coap(void){
    register_tbl_array(TBL_LOG_COAP, 0, "coap", init_coap_dissector);
}


