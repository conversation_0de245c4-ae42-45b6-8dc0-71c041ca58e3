//Design By chunli

#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#include "list.h"
#include "dpi_file_rsm.h"

#ifndef typeof
#define typeof __typeof__
#endif

//#define CHUNLI_DEBUG   // 追踪信息 编译开关
#ifdef CHUNLI_DEBUG
#define DBG(...) (printf(__VA_ARGS__))
#else
#define DBG(...)
#endif

static int lt_16(uint64_t x, uint64_t y) { return ((int16_t)((x) - (y)) < 0);}
static int lt_32(uint64_t x, uint64_t y) { return ((int32_t)((x) - (y)) < 0);}
static int lt_64(uint64_t x, uint64_t y) { return ((int64_t)((x) - (y)) < 0);}

static int gt_16(uint64_t x, uint64_t y) { return ((int16_t)((y) - (x)) < 0);}
static int gt_32(uint64_t x, uint64_t y) { return ((int32_t)((y) - (x)) < 0);}
static int gt_64(uint64_t x, uint64_t y) { return ((int64_t)((y) - (x)) < 0);}

static int le_16(uint64_t x, uint64_t y) { return ((int16_t)((x) - (y)) <= 0);}
static int le_32(uint64_t x, uint64_t y) { return ((int32_t)((x) - (y)) <= 0);}
static int le_64(uint64_t x, uint64_t y) { return ((int64_t)((x) - (y)) <= 0);}

static int ge_16(uint64_t x, uint64_t y) { return ((int16_t)((y) - (x)) <= 0);}
static int ge_32(uint64_t x, uint64_t y) { return ((int32_t)((y) - (x)) <= 0);}
static int ge_64(uint64_t x, uint64_t y) { return ((int64_t)((y) - (x)) <= 0);}
static int eq   (uint64_t x, uint64_t y) { return ((x) == (y));}

struct file_rsm_t
{
    int (*lt)(uint64_t x, uint64_t y);
    int (*le)(uint64_t x, uint64_t y);
    int (*gt)(uint64_t x, uint64_t y);
    int (*ge)(uint64_t x, uint64_t y);
    int (*eq)(uint64_t x, uint64_t y);
    struct   list_head head;
};

struct file_rsm_node_t
{
    struct   list_head node;
    uint64_t            seq;
    uint64_t            len;
};

// 节点 生成器
static struct file_rsm_node_t* file_rsm_node(uint64_t seq, uint64_t len)
{
    struct file_rsm_node_t *node = malloc(sizeof(struct file_rsm_node_t));
    memset(node, 0, sizeof(struct file_rsm_node_t));

    INIT_LIST_HEAD(&node->node);
    node->seq  = seq;
    node->len  = len;
    return node;
}

// PKT insert
static int file_rsm_insert(uint64_t seq, uint64_t len, struct file_rsm_node_t *pos)
{
    struct file_rsm_node_t *new = file_rsm_node(seq, len);
    list_add_tail(&new->node, &pos->node);
    return 0;
}

/*
 * TCP 重组精髓. 分治算法
 *
 * 1. 单包重复(头含头 且 尾含尾)
 * 2. 两包重叠(头超头 或 尾超尾)
 * 3. 多包跨越(头超头 且 尾超尾)
 * 4. SEQ 回滚(精妙处理)
 * 5. 超越 Wireshark TCP 追踪流功能
 */
static int file_rsm_wonderful(struct file_rsm_t *file, uint64_t seq, uint64_t len)
{
    struct list_head       *head        = &file->head;
    struct file_rsm_node_t *pos         = NULL;
    uint32_t                offset      = 0;
    int (*LT)(uint64_t x, uint64_t y)   = file->lt;
    int (*LE)(uint64_t x, uint64_t y)   = file->le;
    int (*GT)(uint64_t x, uint64_t y)   = file->gt;
    int (*GE)(uint64_t x, uint64_t y)   = file->ge;
    int (*EQ)(uint64_t x, uint64_t y)   = file->eq;

    if(len <= 0)
    {
        return 0;
    }

    list_for_each_entry(pos, head, node)
    {
        // 检测到跨越
        if(LT(seq, pos->seq) && GT(seq + len, pos->seq + pos->len))
        {
            DBG("file_rsm 跨越多包 new seq %zu len %zu, pos seq %zu len %zu\n", seq, len, pos->seq, pos->len);
            offset = pos->seq - seq;
            file_rsm_wonderful(file, seq, offset);
            file_rsm_wonderful(file, seq+offset, len-offset);
            break;
        }
        else
        // 新包踩旧包头
        if(LT(seq, pos->seq) && GT(seq + len, pos->seq) && LE(seq + len, pos->seq + pos->len))
        {
            DBG("file_rsm 尾压头 new seq %zu len %zu, pos seq %zu len %zu\n", seq, len, pos->seq, pos->len);
            offset = pos->seq - seq;
            file_rsm_wonderful(file, seq, offset);
            file_rsm_wonderful(file, seq+offset, len - offset);
            break;
        }
        else
        // 新包踩旧包尾
        if(GT(seq + len, pos->seq + pos->len) && LT(seq, pos->seq + pos->len) && GE(seq, pos->seq))
        {
            DBG("file_rsm 头踩尾 new seq %zu len %zu, pos seq %zu len %zu\n", seq, len, pos->seq, pos->len);
            offset = pos->seq + pos->len - seq;
            file_rsm_wonderful(file, seq + offset, len - offset);
            file_rsm_wonderful(file, seq, offset);
            break;
        }
        else
        // 已存在
        if(GE(seq, pos->seq) && LE(seq + len, pos->seq + pos->len))
        {
            DBG("file_rsm 包含丢弃 new seq %zu len %zu, pos seq %zu len %zu\n", seq, len, pos->seq, pos->len);
            break;
        }
        else
        // 期望值, 那就直接更新
        if(EQ(seq, pos->seq + pos->len))
        {
            DBG("file_rsm 值域更新 new seq %zu len %zu, pos seq %zu len %zu\n", seq, len, pos->seq, pos->len);
            pos->len += len;
            break;
        }
        // 比 POS 大, 那就插入
        if(GT(seq, pos->seq))
        {
            DBG("file_rsm 正常插入 new seq %zu len %zu, pos seq %zu len %zu\n", seq, len, pos->seq, pos->len);
            file_rsm_insert(seq, len, pos);
            break;
        }
    }

    //没有找到合适的位置

    //链表为空,最小,插入
    if(list_empty(head) && &pos->node == head)
    {
        DBG("file_rsm 空链插入 new seq %zu len %zu\n", seq, len);
        file_rsm_insert(seq, len, pos);
    }
    else
    //链表非空,最小,插入
    if(!list_empty(head) && &pos->node == head)
    {
        DBG("file_rsm 最小插入 new seq %zu len %zu, pos seq %zu len %zu\n", seq, len, pos->seq, pos->len);
        file_rsm_insert(seq, len, pos);
    }

    return 0;
}

int file_rsm_push(struct file_rsm_t *file, uint64_t seq, uint64_t len)
{
    return file_rsm_wonderful(file, seq, len);
}

static uint64_t file_rsm_head_offset(struct file_rsm_t *file)
{
    struct   list_head    *head    = &file->head;
    struct file_rsm_node_t*pos     = NULL;
    list_for_each_entry_reverse(pos, head, node)
    {
        return pos->seq;
    }
    return 0;
}

uint64_t file_rsm_len(struct file_rsm_t *file)
{
    struct   list_head    *head    = &file->head;
    struct file_rsm_node_t*pos     = NULL;
    uint64_t               exp     = 0; //起始SEQ必须是0
    DBG("初始OFFSET[%zu]\n", exp);
    list_for_each_entry_reverse(pos, head, node)
    {
        if(exp == pos->seq)
        {
            DBG("迭代OFFSET[%zu] len[%zu]\n", pos->seq, pos->len);
            exp = pos->seq + pos->len;
        }
        else
        {
            DBG("残缺OFFSET[%zu]\n", pos->seq);
            break;
        }
    }
    return exp;
}

uint64_t file_rsm_write(struct file_rsm_t *file)
{
    struct   list_head    *head    = &file->head;
    struct file_rsm_node_t*pos     = NULL;
    uint64_t               len     = 0;
    list_for_each_entry_reverse(pos, head, node)
    {
        len += pos->len;
        DBG("file_rsm_write OFFSET[%zu] len[%zu] write[%zu]\n", pos->seq, pos->len, len);
    }
    return len;
}

void file_rsm_free(struct file_rsm_t *file)
{
    struct   list_head     *head   = &file->head;
    struct file_rsm_node_t *pos    = NULL;
    struct file_rsm_node_t *tmp    = NULL;

    list_for_each_entry_safe_reverse(pos, tmp, head, node)
    {
        list_del(&pos->node);
        free(pos);
    }
    free(file);
}

struct file_rsm_t* file_rsm_init(int size)
{
    struct file_rsm_t *rsm = (struct file_rsm_t *)malloc(sizeof(struct file_rsm_t));
    memset(rsm, 0, sizeof(struct file_rsm_t));
    INIT_LIST_HEAD(&rsm->head);

    switch(size)
    {
        case 16:
            rsm->lt = lt_32;
            rsm->le = le_32;
            rsm->gt = gt_32;
            rsm->ge = ge_32;
            rsm->eq = eq;
            break;

        case 32:
            rsm->lt = lt_32;
            rsm->le = le_32;
            rsm->gt = gt_32;
            rsm->ge = ge_32;
            rsm->eq = eq;
            break;

        case 64:
            rsm->lt = lt_64;
            rsm->le = le_64;
            rsm->gt = gt_64;
            rsm->ge = ge_64;
            rsm->eq = eq;
            break;
        default:
            printf("不支持的类型\n");
    }

    DBG("file_rsm_init %p\n", rsm);
    return rsm;
}


