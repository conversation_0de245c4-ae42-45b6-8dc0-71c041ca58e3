#include "dpi_detect.h"
#include "dpi_trailer.h"

//temporarily unused
static void dissect_mcc_mnc(const uint8_t* payload, uint16_t *mcc, uint8_t *mnc)
{
	*mcc = ((payload[0] & 0x0f) * 100) + ((payload[0] >> 4) * 10) + (payload[1] & 0x0f);
	*mnc = ((payload[2] &0x0f) * 10) + (payload[2] > 4);
}

void parse_jl_trailer(struct jl_trailer *trailer, const uint8_t *payload, uint16_t payload_len)
{
	if(payload_len < 4 || (get_uint16_ntohs(payload, 0) != 0xfffe) ||  (payload[2] + 3 > payload_len))
		return;

	uint8_t offset, next_offset;
    uint8_t type, length;
	uint8_t total_length = payload[2];
	
	trailer->base = payload[3] >> 4;
	payload += 3;
	offset = 1;

	trailer->mnc = -1; //初始值,避免混淆

	while(offset  + 2 < total_length){
		type = payload[offset];
		length = payload[offset + 1];
		switch(type){
			case 1: 
			case 2: 
			case 3:
				if(length != 8)
					return;
				else{
					trailer->dev_tag[payload[offset]-1][0] = 1;
					if(type == 1)
						trailer->mnc = (payload[offset+3] >> 3) * 10 + (payload[offset+4] & 0x0f);
					memcpy(trailer->dev_tag[payload[offset]-1] + 1, payload + offset + 2, 8);
				}
				break;
			case 4:
				if(length != 7)
					return;
				else{
					trailer->meid[0] = 1;
					memcpy(trailer->meid + 1, payload + offset + 2, 7);
				}
				break;
			case 5:
				if(length != 4)
					return;
				else{
					trailer->esn[0] = 1;
					memcpy(trailer->esn + 1, payload + offset + 2, 4);
				}
				break;
			case 6:
				memcpy(trailer->apn, payload + offset + 2, DPI_MIN(payload[offset+1], 15));
				break;
			case 7:
			case 8:
			case 9:
				if(length != 7)
					return;
//				dissect_mcc_mnc(payload+offset+2, &trailer->mcc, &trailer->mnc);
				trailer->lac = get_uint16_ntohs(payload, offset+5);
				trailer->undef[payload[offset]-7] = get_uint16_ntohs(payload, offset+7);
				break;
			case 11:
				if(length != 7)
					return;
//				dissect_mcc_mnc(payload+offset+2, &trailer->mcc, &trailer->mnc);
				trailer->cgi_ci = get_uint32_ntohl(payload, offset+5);
                break;
			case 10:
				if(length != 5)
					return;
//				dissect_mcc_mnc(payload+offset+2, &trailer->mcc, &trailer->mnc);
				trailer->undef[3] = get_uint16_ntohs(payload, offset+5);
				break;
			case 12:
				if(length != 6)
					return;
				else{
					trailer->bsid[0] = 1;
					memcpy(trailer->bsid+1, payload + offset + 2, 6);
				}
				break;
		}

        next_offset = offset + length + 2;
        if(next_offset <= offset)
            return;
        offset = next_offset;
	}
	
}
