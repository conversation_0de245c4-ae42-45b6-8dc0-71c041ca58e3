#ifndef _DPI_DCERPC_H_
#define _DPI_DCERPC_H_

#include "dpi_typedefs.h"
#include "dpi_common.h"

// DCERPC协议端口定义
#define DCERPC_PORT_135     135     // RPC Endpoint Mapper
#define DCERPC_PORT_1024    1024    // Dynamic RPC port range start
#define DCERPC_PORT_5000    5000    // Dynamic RPC port range end

// DCERPC数据包类型
#define DCERPC_PKT_REQUEST      0
#define DCERPC_PKT_RESPONSE     2
#define DCERPC_PKT_FAULT        3
#define DCERPC_PKT_BIND         11
#define DCERPC_PKT_BIND_ACK     12
#define DCERPC_PKT_BIND_NAK     13
#define DCERPC_PKT_ALTER_CONTEXT 14
#define DCERPC_PKT_ALTER_CONTEXT_RESP 15
#define DCERPC_PKT_SHUTDOWN     17
#define DCERPC_PKT_CO_CANCEL    18
#define DCERPC_PKT_ORPHANED     19

// DCERPC认证类型
#define DCERPC_AUTH_TYPE_NONE       0
#define DCERPC_AUTH_TYPE_KRB5       1
#define DCERPC_AUTH_TYPE_SPNEGO     9
#define DCERPC_AUTH_TYPE_NTLMSSP    10
#define DCERPC_AUTH_TYPE_SCHANNEL   68

// DCERPC认证级别
#define DCERPC_AUTH_LEVEL_NONE      1
#define DCERPC_AUTH_LEVEL_CONNECT   2
#define DCERPC_AUTH_LEVEL_CALL      3
#define DCERPC_AUTH_LEVEL_PKT       4
#define DCERPC_AUTH_LEVEL_PKT_INTEGRITY 5
#define DCERPC_AUTH_LEVEL_PKT_PRIVACY   6

// DCERPC标志位
#define DCERPC_PFC_FIRST_FRAG   0x01
#define DCERPC_PFC_LAST_FRAG    0x02
#define DCERPC_PFC_PENDING_CANCEL 0x04
#define DCERPC_PFC_RESERVED_1   0x08
#define DCERPC_PFC_CONC_MPX     0x10
#define DCERPC_PFC_DID_NOT_EXECUTE 0x20
#define DCERPC_PFC_MAYBE        0x40
#define DCERPC_PFC_OBJECT_UUID  0x80

// DCERPC字段枚举 - 与lua文件中的to_name对应
typedef enum {
    EM_DCERPC_VERSION = 0,              // dcerpc_version
    EM_DCERPC_PACKET_TYPES,             // dcerpc_packet_types
    EM_DCERPC_AUTH_TYPE,                // dcerpc_auth_type
    EM_DCERPC_SECOND_ADDRESS,           // decrpc_second_address
    EM_DCERPC_AUTH_LEVEL,               // decrpc_auth_level
    EM_DCERPC_OBJECT,                   // decrpc_object
    EM_DCERPC_INTERFACE,                // decrpc_interface
    EM_DCERPC_OPERATION_NUMBER,         // decrpc_operation_number
    EM_DCERPC_ENDPOINT,                 // decrpc_endpoint
    EM_DCERPC_MAX
} dcerpc_enum_index;

// DCERPC头部结构
struct dcerpc_header {
    uint8_t  version;           // 版本号
    uint8_t  version_minor;     // 次版本号
    uint8_t  packet_type;       // 数据包类型
    uint8_t  packet_flags;      // 数据包标志
    uint8_t  drep[4];          // 数据表示格式
    uint16_t frag_length;       // 片段长度
    uint16_t auth_length;       // 认证长度
    uint32_t call_id;          // 调用ID
} __attribute__((packed));

// DCERPC信息结构 - 包含所有需求字段
struct dcerpc_info {
    uint8_t  version_major;     // 主版本号
    uint8_t  version_minor;     // 次版本号
    uint8_t  packet_type;       // 数据包类型
    uint8_t  packet_flags;      // 数据包标志
    uint16_t auth_type;         // 认证类型
    uint16_t auth_level;        // 认证级别
    uint32_t operation_number;  // 操作编号
    char     service_uuid[64];  // 服务UUID
    char     endpoint[128];     // 端点
    char     interface[128];    // 接口UUID
    char     object_uuid[64];   // 对象UUID
    char     second_address[128]; // 第二地址
    uint32_t call_id;          // 调用ID
    uint16_t frag_length;       // 片段长度
    uint16_t auth_length;       // 认证长度
    int      is_request;        // 是否为请求
    int      is_response;       // 是否为响应
};

// 函数声明 - 这些函数在源文件中是静态的，不需要在头文件中声明
// static int dissect_dcerpc(struct flow_info *flow, int direction, uint32_t seq,
//                    const uint8_t *payload, uint32_t payload_len, uint8_t flag);
// static void identify_dcerpc(struct flow_info *flow, const uint8_t *payload, const uint16_t payload_len);

#endif // _DPI_DCERPC_H_
