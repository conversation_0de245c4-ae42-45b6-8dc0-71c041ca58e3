#include <arpa/inet.h>
#include <netinet/in.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include "dpi_common.h"
#include "dpi_conversation.h"
#include "dpi_dcerpc.h"
#include "dpi_detect.h"
#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_pschema.h"
#include "dpi_tbl_log.h"
#include "yaProtoRecord/precord.h"

extern struct rte_mempool *tbl_log_mempool;

static dpi_field_table dcerpc_field_array[] = {
    DPI_FIELD_D(EM_DCERPC_VERSION, YA_FT_STRING, "version"),
    DPI_FIELD_D(EM_DCERPC_PACKET_TYPES, YA_FT_STRING, "packet_types"),
    DPI_FIELD_D(EM_DCERPC_AUTH_TYPE, YA_FT_UINT8, "auth_type"),
    DPI_FIELD_D(EM_DCERPC_SECOND_ADDRESS, YA_FT_STRING, "second_address"),
    DPI_FIELD_D(EM_DCERPC_AUTH_LEVEL, YA_FT_UINT8, "auth_level"),
    DPI_FIELD_D(EM_DCERPC_OBJECT, YA_FT_STRING, "object"),
    DPI_FIELD_D(EM_DCERPC_INTERFACE, YA_FT_STRING, "interface"),
    DPI_FIELD_D(EM_DCERPC_OPERATION_NUMBER, YA_FT_UINT16, "operation_number"),
    DPI_FIELD_D(EM_DCERPC_ENDPOINT, YA_FT_STRING, "endpoint"),
};

// 数据包类型到字符串的映射
static struct int_to_string dcerpc_packet_type_map[] = {
    {DCERPC_PKT_REQUEST, "Request"},
    {DCERPC_PKT_RESPONSE, "Response"},
    {DCERPC_PKT_FAULT, "Fault"},
    {DCERPC_PKT_BIND, "Bind"},
    {DCERPC_PKT_BIND_ACK, "Bind_ack"},
    {DCERPC_PKT_BIND_NAK, "Bind_nak"},
    {DCERPC_PKT_ALTER_CONTEXT, "Alter_context"},
    {DCERPC_PKT_ALTER_CONTEXT_RESP, "Alter_context_resp"},
    {DCERPC_PKT_SHUTDOWN, "Shutdown"},
    {DCERPC_PKT_CO_CANCEL, "Co_cancel"},
    {DCERPC_PKT_ORPHANED, "Orphaned"},
    {0, NULL},
};

// 认证类型到字符串的映射
static struct int_to_string dcerpc_auth_type_map[] = {
    {DCERPC_AUTH_TYPE_NONE, "None"},       {DCERPC_AUTH_TYPE_KRB5, "Kerberos5"},    {DCERPC_AUTH_TYPE_SPNEGO, "SPNEGO"},
    {DCERPC_AUTH_TYPE_NTLMSSP, "NTLMSSP"}, {DCERPC_AUTH_TYPE_SCHANNEL, "SCHANNEL"}, {0, NULL},
};

// 解析DCERPC头部
static int parse_dcerpc_header(const uint8_t *payload, uint32_t payload_len, struct dcerpc_header *header) {
  if (payload_len < sizeof(struct dcerpc_header)) {
    return -1;
  }

  header->version       = payload[0];
  header->version_minor = payload[1];
  header->packet_type   = payload[2];
  header->packet_flags  = payload[3];
  memcpy(header->drep, &payload[4], 4);
  header->frag_length = get_uint16_t(payload, 8);
  header->auth_length = get_uint16_t(payload, 10);
  header->call_id     = get_uint32_t(payload, 12);

  return 0;
}

// 解析UUID
static void parse_uuid(const uint8_t *data, char *uuid_str, size_t uuid_str_len) {
  if (!data || !uuid_str || uuid_str_len < 37) {
    return;
  }

  snprintf(uuid_str, uuid_str_len, "%02x%02x%02x%02x-%02x%02x-%02x%02x-%02x%02x-%02x%02x%02x%02x%02x%02x", data[3],
           data[2], data[1], data[0], data[5], data[4], data[7], data[6], data[8], data[9], data[10], data[11],
           data[12], data[13], data[14], data[15]);
}

// 解析DCERPC绑定请求
static int parse_dcerpc_bind(const uint8_t *payload, uint32_t payload_len, uint32_t offset, struct dcerpc_info *info) {
  if (offset + 16 > payload_len) {
    return -1;
  }

  // 跳过最大传输片段大小和最大接收片段大小
  offset += 4;

  // 跳过关联组ID
  offset += 4;

  // 解析上下文项数量
  uint8_t num_ctx_items = payload[offset];
  offset += 1;

  // 跳过保留字段
  offset += 3;

  // 解析第一个上下文项
  if (offset + 20 <= payload_len) {
    // 跳过上下文ID和传输语法数量
    offset += 4;

    // 解析抽象语法UUID (接口UUID)
    parse_uuid(&payload[offset], info->interface, sizeof(info->interface));
    offset += 16;

    // 解析抽象语法版本
    // uint32_t abstract_syntax_ver = get_uint32_t(payload, offset);
    offset += 4;

    // 解析传输语法UUID
    if (offset + 16 <= payload_len) {
      parse_uuid(&payload[offset], info->service_uuid, sizeof(info->service_uuid));
    }
  }

  return 0;
}

// 解析认证信息
static int parse_dcerpc_auth(const uint8_t *payload, uint32_t payload_len, struct dcerpc_header *header,
                             struct dcerpc_info *info) {
  if (header->auth_length == 0) {
    return 0;  // 没有认证信息
  }

  uint32_t auth_offset = header->frag_length - header->auth_length;
  if (auth_offset + 8 > payload_len) {
    return -1;
  }

  // 解析认证头部
  info->auth_type  = payload[auth_offset];
  info->auth_level = payload[auth_offset + 1];

  return 0;
}

// 写入DCERPC日志
static void write_dcerpc_log(struct flow_info *flow, int direction, struct dcerpc_info *info, void *unused) {
  struct tbl_log *log_ptr = NULL;
  int             idx     = 0;

  UNUSED(unused);
  if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
    DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
    return;
  }

  init_log_ptr_data(log_ptr, flow, EM_TBL_LOG_ID_BY_DEFAULT);
  dpi_precord_new_record(log_ptr->record, NULL, NULL);
  write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, NULL);

  player_t *layer = precord_layer_put_new_layer(log_ptr->record, "dcerpc");

  // 设置字段值 - 实现所有需求字段

  // 1. dcerpc_version - 版本信息 (主版本.次版本)
  char version_str[32];
  snprintf(version_str, sizeof(version_str), "%u.%u", info->version_major, info->version_minor);
  precord_put_to_layer(log_ptr->record, "dcerpc", "version", string, version_str);

  // 2. dcerpc_packet_types - 数据包类型
  const char *packet_type_str = val_to_string(info->packet_type, dcerpc_packet_type_map);
  if (packet_type_str) {
    precord_put_to_layer(log_ptr->record, "dcerpc", "packet_types", string, packet_type_str);
  }

  // 3. dcerpc_auth_type - 认证类型 (uint8类型)
  precord_put_to_layer(log_ptr->record, "dcerpc", "auth_type", uinteger, info->auth_type);

  // 4. decrpc_second_address - 第二地址 (暂时为空，可根据需要实现)
  precord_put_to_layer(log_ptr->record, "dcerpc", "second_address", string, "");

  // 5. decrpc_auth_level - 认证级别 (uint8类型)
  precord_put_to_layer(log_ptr->record, "dcerpc", "auth_level", uinteger, info->auth_level);

  // 6. decrpc_object - 对象UUID (暂时为空，可根据需要实现)
  precord_put_to_layer(log_ptr->record, "dcerpc", "object", string, "");

  // 7. decrpc_interface - 接口UUID
  if (strlen(info->interface) > 0) {
    precord_put_to_layer(log_ptr->record, "dcerpc", "interface", string, info->interface);
  }

  // 8. decrpc_operation_number - 操作编号 (uint16类型)
  precord_put_to_layer(log_ptr->record, "dcerpc", "operation_number", uinteger, info->operation_number);

  // 9. decrpc_endpoint - 端点信息
  char           endpoint_str[128];
  struct in_addr addr;
  addr.s_addr = *(uint32_t *)flow->tuple.inner.ip_dst;
  snprintf(endpoint_str, sizeof(endpoint_str), "%s:%u", inet_ntoa(addr), ntohs(flow->tuple.inner.port_dst));
  precord_put_to_layer(log_ptr->record, "dcerpc", "endpoint", string, endpoint_str);

  // 写入日志
  log_ptr->log_type  = TBL_LOG_DCERPC;
  log_ptr->log_len   = idx;
  log_ptr->thread_id = flow->thread_id;
  log_ptr->flow      = flow;

  if (write_tbl_log(log_ptr) != 1) {
    sdt_precord_destroy(log_ptr->record);
    log_ptr->record = NULL;
    rte_mempool_put(tbl_log_mempool, (void *)log_ptr);
  }
  return;
}

// DCERPC协议识别函数 - 新版接口
static int identify_dcerpc(struct flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len) {
  UNUSED(C2S);

  if (g_config.protocol_switch[PROTOCOL_DCERPC] == 0) {
    return 0;
  }

  if (payload_len < 16) {
    return 0;
  }

  struct dcerpc_header header;
  if (parse_dcerpc_header(payload, payload_len, &header) != 0) {
    return 0;
  }

  // 检查DCERPC版本 (通常是5.0)
  if (header.version != 5) {
    return 0;
  }

  // 检查数据包类型是否有效
  if (header.packet_type > DCERPC_PKT_ORPHANED) {
    return 0;
  }

  // 检查片段长度是否合理
  if (header.frag_length < 16 || header.frag_length > payload_len) {
    return 0;
  }

  // 如果是已知的DCERPC端口或者头部格式正确，则识别为DCERPC
  uint16_t dst_port = ntohs(flow->tuple.inner.port_dst);
  uint16_t src_port = ntohs(flow->tuple.inner.port_src);

  if (dst_port == DCERPC_PORT_135 || src_port == DCERPC_PORT_135 ||
      (dst_port >= DCERPC_PORT_1024 && dst_port <= DCERPC_PORT_5000) ||
      (src_port >= DCERPC_PORT_1024 && src_port <= DCERPC_PORT_5000)) {
    flow->real_protocol_id = PROTOCOL_DCERPC;
    return 1;  // 识别成功
  }

  return 0;  // 未识别
}

// DCERPC协议解析函数 - 新版接口
static int dissect_dcerpc(struct flow_info *flow, uint8_t C2S, const uint8_t *payload, uint32_t payload_len) {
  if (g_config.protocol_switch[PROTOCOL_DCERPC] == 0) {
    return 0;
  }

  if (payload_len < 16) {
    return 0;
  }

  struct dcerpc_header header;
  struct dcerpc_info   info;

  memset(&info, 0, sizeof(info));

  // 解析DCERPC头部
  if (parse_dcerpc_header(payload, payload_len, &header) != 0) {
    return PKT_DROP;
  }

  // 填充信息结构
  info.version_major = header.version;
  info.version_minor = header.version_minor;
  info.packet_type   = header.packet_type;
  info.packet_flags  = header.packet_flags;
  info.call_id       = header.call_id;
  info.frag_length   = header.frag_length;
  info.auth_length   = header.auth_length;

  // 解析认证信息
  parse_dcerpc_auth(payload, payload_len, &header, &info);

  // 根据数据包类型进行特殊处理
  uint32_t offset = 16;  // 跳过基本头部

  switch (header.packet_type) {
    case DCERPC_PKT_BIND:
    case DCERPC_PKT_ALTER_CONTEXT:
      parse_dcerpc_bind(payload, payload_len, offset, &info);
      break;

    case DCERPC_PKT_REQUEST:
      info.is_request = 1;
      // 解析请求中的操作编号
      if (offset + 4 <= payload_len) {
        info.operation_number = get_uint16_t(payload, offset + 2);
      }
      break;

    case DCERPC_PKT_RESPONSE:
      info.is_response = 1;
      break;

    default:
      break;
  }

  // 写入日志 - 使用C2S作为方向
  write_dcerpc_log(flow, C2S, &info, NULL);

  return 0;
}

// 前向声明
extern struct decode_t decode_dcerpc;

// DCERPC协议初始化函数 - 新版接口
static int init_dcerpc_dissector(struct decode_t *decode) {
  // 注册协议字段
  dpi_register_proto_schema(dcerpc_field_array, EM_DCERPC_MAX, "dcerpc");

  // 注册端口
  decode_on_port_tcp(DCERPC_PORT_135, &decode_dcerpc);

  // 注册日志表
  register_tbl_array(TBL_LOG_DCERPC, 0, "dcerpc", NULL);

  // 注册字段映射信息
  map_fields_info_register(dcerpc_field_array, PROTOCOL_DCERPC, EM_DCERPC_MAX, "dcerpc");

  return 0;
}
static void flow_dcerpc_finish(struct flow_info *flow)
{
  return;
}
// 销毁函数
static int dcerpc_destroy(struct decode_t *decode) { return 0; }

// DCERPC decode_t结构体定义
struct decode_t decode_dcerpc = {
    .name = "dcerpc",
#ifdef DPI_SDT_ZDY
    .identify_type = DPI_IDENTIFY_PORT_CONTENT,
#endif
    .decode_initial = init_dcerpc_dissector,
    .pkt_identify   = identify_dcerpc,
    .pkt_dissect    = dissect_dcerpc,
    .flow_finish    = flow_dcerpc_finish,
    .decode_destroy = dcerpc_destroy,

};
