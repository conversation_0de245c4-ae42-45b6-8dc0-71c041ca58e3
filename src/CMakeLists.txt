#set(CMAKE_VERBOSE_MAKEFILE on)
set(LibraryName "dpisdt")
# 暂不使用 -Waddress-of-packed-member 打包成员地址
set(warn_flags "-Wall -Wextra                   \
                -Wno-missing-field-initializers \
                -Wno-unused-parameter           \
                -Wno-unused-variable            \
                -Wno-unused-function            \
                -Wno-unused-but-set-variable    \
                -Wno-cast-qual")

# gcc 版本大于 7.0 的编译参数
if (GCC_VERSION VERSION_GREATER 7)
    set(warn_flags "-Wno-format-truncation      \
                    -Wno-address-of-packed-member")
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11")
endif()

# 显示没有被链接的代码段
#set(CMAKE_EXE_LINKER_FLAGS "-Wl,--gc-sections -Wl,--print-gc-sections")
set(CMAKE_C_FLAGS_RELEASE   "${CMAKE_C_FLAGS_DEBUG} -g")   #relesae 模式带上符号信息
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_DEBUG} -g") #relesae 模式带上符号信息
set(CMAKE_C_FLAGS "-DLOG_USE_COLOR -D_GNU_SOURCE -std=c11 -m64  ${CMAKE_C_FLAGS}  ${warn_flags} -fdiagnostics-color=always")
set(CMAKE_CXX_FLAGS "-DLOG_USE_COLOR -D_GNU_SOURCE -m64 ${CMAKE_CXX_FLAGS}  ${warn_flags}")
set(ENV{PKG_CONFIG_PATH} "$ENV{PKG_CONFIG_PATH}:/usr/local/lib64/pkgconfig")

# 暂时添加,方便分别在新旧两种模式下测试, 测试稳定后删除
add_compile_definitions(DPI_FUTURE_MBUF=0)
add_compile_definitions(DPI_PRINT_DETAILS=0)  #打印详细信息.提供给web页面'插件编译'使用

# 327_ZDY 陕西项目 专用开关 在此
option(ENABLE_SDT_ZDY "complie project as 327 zdy" OFF)
if (ENABLE_SDT_ZDY)
  add_compile_definitions(DPI_SDT_ZDY)
endif()
option(ENABLE_SDT_P327 "complie project as p327" OFF)
if (ENABLE_SDT_P327)
  add_compile_definitions(DPI_SDT_ZDY)
  add_compile_definitions(DPI_SDT_P327)
endif()
option(ENABLE_SDT_YNAO "complie project as ynao" OFF)
if (ENABLE_SDT_YNAO)
  add_compile_definitions(DPI_SDT_YNAO)
  string(APPEND AppName "_tbl")
endif()

option(ENABLE_ARKIME "启用sdxCap支持 Y脑项目" ON)
if (ENABLE_ARKIME)
  add_compile_definitions(ENABLE_ARKIME)
  string(APPEND AppName "_flow")
endif()
option(ENABLE_SMART_NIC "启用智能网卡收包功能, 需要dpdk19,libsmart_nic支持" OFF)

# 内存分配方式  on  dpdk mempool off  malloc
option(ENABLE_DPDK_MEMPOOL "dpdk mempool" ON)
if (ENABLE_DPDK_MEMPOOL)
  add_compile_definitions(DPDK_MEMPOOL)
endif()
include(CheckCXXCompilerFlag)
check_cxx_compiler_flag("-mavx2" COMPILER_SUPPORT_AVX2)
check_cxx_compiler_flag("-msse4.1" COMPILER_SUPPORT_SSE1)
find_package(PkgConfig REQUIRED)
pkg_check_modules(yaSdxWatch REQUIRED IMPORTED_TARGET libyaSdxWatch)
pkg_check_modules(yv_sub REQUIRED IMPORTED_TARGET libyv_sub)
pkg_check_modules(YA_PROTO_RECORD REQUIRED IMPORTED_TARGET libyaProtoRecord)
# pkg_check_modules(YA_FTYPES REQUIRED IMPORTED_TARGET libyaFtypes)
pkg_check_modules(YA_EMAIL REQUIRED IMPORTED_TARGET libyaemail)
pkg_check_modules(xml2 REQUIRED IMPORTED_TARGET libxml-2.0)

if (ENABLE_SMART_NIC)
  add_compile_definitions(ENABLE_SMART_NIC)
  pkg_check_modules(YA_DPDK REQUIRED IMPORTED_TARGET libdpdk-19)
  pkg_check_modules(EXTRA_TARGET REQUIRED IMPORTED_TARGET libsmart_nic )
  list(APPEND pkg_libaries PkgConfig::EXTRA_TARGET)
else()
  pkg_check_modules(YA_DPDK REQUIRED IMPORTED_TARGET libdpdk)
endif()

include_directories(
  ${CMAKE_SOURCE_DIR}/src/scan
  ${CMAKE_SOURCE_DIR}/src/utils
  ${CMAKE_SOURCE_DIR}/src/input
  ${CMAKE_SOURCE_DIR}/src/arkime
  ${CMAKE_SOURCE_DIR}/src
  ${CMAKE_SOURCE_DIR}/include
  ${YA_DPDK_INCLUDE_DIRS}
  /usr/include/glib-2.0
  /usr/lib64/glib-2.0/include
)

add_subdirectory(utils)
add_subdirectory(scan)
add_subdirectory(input)
add_subdirectory(arkime)

list(APPEND SOURCES
    dpi_main.c
    charsets.c
    dpi_ipip.c
    dpi_arp.c
    dpi_jl_trailer.c
    dpi_snmp_2.c
    dpi_ber_ori.c
    dpi_l2tp.c
    dpi_socket.c
    dpi_bgp.c
    dpi_log.c
    dpi_socks.c
    dpi_bits.c
    dpi_mac_pheader.c
    dpi_ssh.c
    dpi_b_tree.c
    dpi_ssl.c
    dpi_statistics.c
    dpi_cdp.c
    dpi_modbus.c
    dpi_syslog.c
    dpi_common.c
    dpi_mysql.c
    dpi_tbl_log.c
    dpi_conversation.c
    dpi_notify.c
    dpi_tcp_reassemble.c
    dpi_cwmp.c
    dpi_ocsp.c
    dpi_tds.c
    dpi_detect.c
    dpi_per.c
    dpi_teamviewer.c
    dpi_dns.c
    dpi_pgsql.c
    dpi_telnet.c
    dpi_err_pcap_dump.c
    dpi_plugin_example.c
    dpi_tftp.c
    dpi_fl_trailer.c
    dpi_thread_timer.c
    dpi_forward_eth.c
    dpi_ppp.c
    dpi_tns.c
    dpi_forward_kafka.c
    dpi_proto_ids.c
    dpi_trailer.c
    dpi_rdp.c
    dpi_tunnel.c
    dpi_ftp.c
    dpi_rt_trailer.c
    dpi_gre.c
    dpi_s7comm.c
    dpi_vnc.c
    dpi_gtp_control.c
    dpi_sctp.c
    dpi_vrrp.c
    dpi_gtp_u.c
    dpi_sdp.c
    dpi_vtysh.c
    dpi_gtpv2_control.c
    dpi_sdt_ipp.c
    dpi_write_trailer.c
    dpi_http.c
    dpi_sdt_ip_udp_tcp.c
    dpi_x509.c
    dpi_hw_default_trailer.c
    dpi_sdt_link.c
    ip2region.c
    dpi_hw_yn_trailer.c
    dpi_sdt_match.c
    post.c
    dpi_hz_trailer.c
    dpi_sdx_common.c
    sdt_action_out.c
    dpi_icmp.c
    dpi_share_header.c
    sdtapp_interface.c
    dpi_high_app_protos.c
    dpi_memory.c
    dpi_http_high_proto.c
    dpi_tll.c
    $<TARGET_OBJECTS:utils>
    $<TARGET_OBJECTS:scan>
    dpi_data_input.c
    dpi_pop_2.c
    dpi_imap_2.c
    dpi_email.c
    dpi_smtp_2.c
    dpi_pschema.c
    dpi_zdy_output.c
    dpi_lua_adapt.c
    dpi_flow_timer.c
    dpi_metrics.c
    dpi_offline.c
    dpi_flow.c
    flow_flood.c
    dpi_327_common.c
    dpi_sdt_eval_field_callback.cpp
    dpi_sdt_ynao_ip_trans.c
    sdt_RuleDynamic.cpp
    sdt_RuleDynamic_compile.c
    dpi_dcerpc.c
    dpi_coap.c
  )

if (ENABLE_SDT_YNAO)
  list(APPEND SOURCES
  dpi_smb_content.c
  dpi_file_rsm.c
  )
  else()
list(APPEND SOURCES
  dpi_smb.c
)
endif()
add_library(${LibraryName}
${SOURCES}
)

set(LIBRARY_OUTPUT_PATH    ${PROJECT_SOURCE_DIR}/lib)

add_executable(${AppName} dpi_entry.c)

if(ENABLE_SMART_NIC)
target_link_libraries(${AppName}
    $<TARGET_OBJECTS:input>
)
endif()

if(ENABLE_ARKIME)
target_link_libraries(${LibraryName}
    $<TARGET_OBJECTS:arkime>
)
endif()
target_compile_definitions(${LibraryName} PRIVATE _GLIBCXX_USE_CXX11_ABI=0)
target_link_directories(${AppName} PRIVATE
  ${CMAKE_SOURCE_DIR}/lib
  /usr/local/lib64
  # /opt/python37/lib
)

list(APPEND pkg_libaries
  ${yv_sub_STATIC_LDFLAGS}
  ${YA_PROTO_RECORD_STATIC_LDFLAGS}
  ${YA_EMAIL_STATIC_LDFLAGS}
  PkgConfig::xml2
)

target_include_directories(${LibraryName} PRIVATE
  ${CMAKE_SOURCE_DIR}/include
  ${CMAKE_SOURCE_DIR}/src
  ${CMAKE_SOURCE_DIR}/src/input
  ${CMAKE_SOURCE_DIR}/src/arkime
  ${CMAKE_SOURCE_DIR}/include/libevent
  /usr/include
)

target_precompile_headers(${LibraryName} PRIVATE ${CMAKE_SOURCE_DIR}/include/pch.h)
target_precompile_headers(${AppName} PRIVATE ${CMAKE_SOURCE_DIR}/include/pch.h)

target_compile_options(${LibraryName} PUBLIC "${dpdk_cflags}")
target_compile_options(${AppName} PUBLIC "${dpdk_cflags}")

target_link_directories(${AppName} PRIVATE
${CMAKE_SOURCE_DIR}/lib
)

target_link_libraries(${AppName}
  -Wl,--whole-archive ${YA_DPDK_STATIC_LDFLAGS} dpisdt -Wl,-Bdynamic -Wl,--no-whole-archive
  ${pkg_libaries}
  yaBasicUtils
  #启用asan需要 启用上级CmakeList中的 EMABLE_MEMORY_CHECK.
  -static-libasan #取消此注释获得静态链接asan.保持注释则获得动态链接asan.
  icuuc
  anl
  yasdt
  yasdtacl
  tcp_rsm
  # yarestful
  glib-2.0
  ssl
  crypto
  maxminddb
  z
  cjson
  yaSdxWatch
  pthread
  iniparser
  rdkafka
  sdx_rdkafka_producer_consumer
  jsoncpp
  curl
  stdc++
  microxml
  dl
  numa
  pcap
  m
  hs
  mongoose
  # PkgConfig::YA_PROTO_RECORD
  line_convert
  lua
  yasdt_plugin_builtin_func
)

target_link_libraries(${AppName} event.a)
target_link_libraries(${AppName} event_core.a)
target_link_libraries(${AppName} event_extra.a)
target_link_libraries(${AppName} event_pthreads.a)
target_link_libraries(${AppName} glib-2.0)

set_target_properties(${AppName} PROPERTIES RUNTIME_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/run)

if(COMPILER_SUPPORT_AVX2)
    target_compile_options(${LibraryName} PRIVATE "-mavx2")
    target_compile_definitions(${AppName} PRIVATE HAS_AVX2=1)
    target_compile_options(${LibraryName} PRIVATE "-mavx2")
    target_compile_definitions(${AppName} PRIVATE HAS_AVX2=1)
elseif(COMPILER_SUPPORT_SSE1)
    target_compile_options(${LibraryName} PRIVATE "-msse4_1")
    target_compile_definitions(${LibraryName} PRIVATE __SSE4_1__=1)
    target_compile_options(${AppName} PRIVATE "-msse4_1")
    target_compile_definitions(${AppName} PRIVATE __SSE4_1__=1)
endif()

# 为可执行文件生成软链接
add_custom_command(TARGET ${AppName} POST_BUILD
  COMMAND ${CMAKE_COMMAND} -E create_symlink $<TARGET_FILE:${AppName}> ${CMAKE_SOURCE_DIR}/run/yaDpiSdt
)

add_custom_target(vtysh
  COMMAND
    make -C ${CMAKE_SOURCE_DIR}/vtysh
)

message("begin dpi_files = ${dpi_files}")
message("begin dpi_dirs = ${dpi_dirs}")

list(APPEND dpi_files
  ${CMAKE_SOURCE_DIR}/etc/config.ini
  ${CMAKE_SOURCE_DIR}/run/GeoLite2-ASN.mmdb
  ${CMAKE_SOURCE_DIR}/run/GeoLite2-City.mmdb
  ${CMAKE_SOURCE_DIR}/run/ip2region.db
  ${CMAKE_SOURCE_DIR}/run/change_log.txt
  ${CMAKE_SOURCE_DIR}/run/web_config.ini
  ${CMAKE_SOURCE_DIR}/run/start.sh
  ${CMAKE_SOURCE_DIR}/run/manuf
  ${CMAKE_SOURCE_DIR}/run/adapt_sdt.lua
  ${CMAKE_SOURCE_DIR}/lib/libline_convert.so
  ${CMAKE_SOURCE_DIR}/run/libyasdt_plugin_builtin_func.so.1.0.1
)

list(APPEND dpi_bins
  ${CMAKE_SOURCE_DIR}/run/start.sh
  ${CMAKE_SOURCE_DIR}/scripts/mount.sh
  ${CMAKE_SOURCE_DIR}/run/vtysh
)

list(APPEND dpi_dirs
  ${CMAKE_SOURCE_DIR}/field
  ${CMAKE_SOURCE_DIR}/run/adapt_lua
  ${CMAKE_SOURCE_DIR}/run/plugin
)

#依赖了其它.so文件, 一起打包进去, 包括:
# libyasdt_plugin_builtin_func.so: 插件内置函数库;
#使用install(CODE) 是为了解决根据软连接找到真正的目标文件
install(CODE "
  file(INSTALL DESTINATION \"\${CMAKE_INSTALL_PREFIX}/${INSTALL_LIBDIR}\"
    USE_SOURCE_PERMISSIONS FOLLOW_SYMLINK_CHAIN FILES
    \"/usr/local/lib64/libyasdt_plugin_builtin_func.so\"
  )
")

include(${CMAKE_SOURCE_DIR}/cmake/Install.cmake)
include(${CMAKE_SOURCE_DIR}/cmake/CPack.cmake)
add_subdirectory(test)
