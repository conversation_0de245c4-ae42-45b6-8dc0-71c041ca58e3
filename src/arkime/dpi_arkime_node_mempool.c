/*
 * Arkime文件位置节点内存池管理器
 *
 * 功能：
 * 1. 为arkime_file_pos_node提供高效的内存分配和回收
 * 2. 减少频繁malloc/free的开销，特别是UDP流的处理
 * 3. 支持线程安全的并发访问
 * 4. 提供节点对分配优化，减少UDP流的分配次数
 */

#include "dpi_arkime_if.h"


// 全局内存池管理器
static struct arkime_node_mempool_manager *g_node_mempool = NULL;

// 外部配置引用
extern struct global_config g_config;

#ifdef DPDK_MEMPOOL

// 节点初始化回调函数
static void arkime_node_init_cb(struct rte_mempool *mp, __attribute__((unused)) void *arg, void *obj, unsigned i) {
  UNUSED(i);
  memset(obj, 0, sizeof(struct arkime_file_pos_node));
}

// 初始化Arkime节点内存池
void dpi_arkime_node_mempool_init(void) {
  if (g_node_mempool != NULL) {
    DPI_LOG(DPI_LOG_WARNING, "Arkime node mempool already initialized");
    return;
  }

  // 分配内存池管理器
  g_node_mempool = malloc(sizeof(struct arkime_node_mempool_manager));
  if (!g_node_mempool) {
    DPI_LOG(DPI_LOG_ERROR, "Failed to allocate arkime node mempool manager");
    exit(-1);
  }

  // 计算内存池大小
  // 考虑因素：
  // - 最大流数量
  // - UDP流比例（假设70%）
  // - 每个UDP流平均2个节点（file_node + pos_node）
  // - TCP流平均10个节点
  // - 安全系数2倍
  uint32_t udp_nodes          = g_config.max_flow_num * 0.7 * 2;
  uint32_t tcp_nodes          = g_config.max_flow_num * 0.3 * 10;
  g_node_mempool->total_nodes = (udp_nodes + tcp_nodes) * 2;

  // 缓存大小设置为每个核心256个节点
  g_node_mempool->cache_size = 256;

  // 生成内存池名称
  snprintf(g_node_mempool->name, sizeof(g_node_mempool->name), "arkime_node_pool_%d", g_config.socketid);

  // 创建DPDK内存池
  g_node_mempool->pool =
      rte_mempool_create(g_node_mempool->name, g_node_mempool->total_nodes, sizeof(struct arkime_file_pos_node),
                         g_node_mempool->cache_size, 0, NULL, NULL, arkime_node_init_cb, NULL, g_config.socketid, 0);

  if (g_node_mempool->pool == NULL) {
    DPI_LOG(DPI_LOG_ERROR, "Cannot create arkime node mempool");
    free(g_node_mempool);
    g_node_mempool = NULL;
    exit(-1);
  }

  // 初始化计数器
  rte_atomic32_init(&g_node_mempool->alloc_count);
  rte_atomic32_init(&g_node_mempool->free_count);

  DPI_LOG(DPI_LOG_INFO, "Arkime node mempool initialized: %u nodes, cache_size=%u", g_node_mempool->total_nodes,
          g_node_mempool->cache_size);
}

// 清理Arkime节点内存池
void dpi_arkime_node_mempool_cleanup(void) {
  if (g_node_mempool == NULL) {
    return;
  }

  // 打印统计信息
  uint32_t alloc_count = rte_atomic32_read(&g_node_mempool->alloc_count);
  uint32_t free_count  = rte_atomic32_read(&g_node_mempool->free_count);

  DPI_LOG(DPI_LOG_INFO, "Arkime node mempool stats: allocated=%u, freed=%u, leaked=%u", alloc_count, free_count,
          alloc_count - free_count);

  // 释放内存池（DPDK会自动处理）
  // rte_mempool_free(g_node_mempool->pool); // DPDK没有这个函数，内存池会在程序退出时自动清理

  free(g_node_mempool);
  g_node_mempool = NULL;
}

// 分配单个节点
struct arkime_file_pos_node *dpi_arkime_node_alloc(void) {
  if (g_node_mempool == NULL) {
    DPI_LOG(DPI_LOG_ERROR, "Arkime node mempool not initialized");
    return NULL;
  }

  void *node;
  if (rte_mempool_get(g_node_mempool->pool, &node) < 0) {
    DPI_LOG(DPI_LOG_WARNING, "Failed to allocate arkime node from mempool");
    return NULL;
  }

  // 增加分配计数
  rte_atomic32_inc(&g_node_mempool->alloc_count);

  return (struct arkime_file_pos_node *)node;
}

// 释放单个节点
void dpi_arkime_node_free(struct arkime_file_pos_node *node) {
  if (g_node_mempool == NULL || node == NULL) {
    return;
  }

  // 清零节点数据
  memset(node, 0, sizeof(struct arkime_file_pos_node));

  // 归还到内存池
  rte_mempool_put(g_node_mempool->pool, node);

  // 增加释放计数
  rte_atomic32_inc(&g_node_mempool->free_count);
}

#else
// 非DPDK环境下的简单实现

void dpi_arkime_node_mempool_init(void) { DPI_LOG(DPI_LOG_INFO, "Arkime node mempool: using malloc/free (DPDK disabled)"); }

void dpi_arkime_node_mempool_cleanup(void) {
  // 无需清理
}

struct arkime_file_pos_node *dpi_arkime_node_alloc(void) {
  struct arkime_file_pos_node *node = malloc(sizeof(struct arkime_file_pos_node));
  if (node) {
    memset(node, 0, sizeof(struct arkime_file_pos_node));
  }
  return node;
}

void dpi_arkime_node_free(struct arkime_file_pos_node *node) {
  if (node) {
    free(node);
  }
}


#endif
