#include <pthread.h>
#include <stdio.h>

#include <rte_acl.h>

#include "sdtapp_interface.h"
#include "jhash.h"

#include <vector>
#include <map>

#include "sdt_RuleDynamic.h"

// 多线程规则反射 互斥锁
static pthread_mutex_t dynMutex = PTHREAD_MUTEX_INITIALIZER;
static pthread_mutex_t uuidMutex = PTHREAD_MUTEX_INITIALIZER;

//记录已经反射过的规则
std::vector<struct sdt_acl_t *> dynamicRulev4;
std::vector<struct sdt_acl_t *> dynamicRulev6;

//记录已经反射过的规则的HASH
std::map<uint32_t, bool> dynamicRuleHash;


//运行在各个匹配执行线程, 一定会竞争
static int
sdt_actionDynamic_push(struct sdt_acl_t *acl)
{
    // 二次检查
    pthread_mutex_lock(&dynMutex);
    uint32_t hash = jhash(acl, sizeof(struct sdt_acl_t), JHASH_INITVAL);
    int var = dynamicRuleHash[hash];
    if(0 == var)
    {
        if(AF_INET == acl->af)
        {
            dynamicRulev4.push_back(acl);
        }
        else
        if(AF_INET6 == acl->af)
        {
            dynamicRulev6.push_back(acl);
        }
        dynamicRuleHash[hash]  =1;
    }
    pthread_mutex_unlock(&dynMutex);
    return 0;
}


extern struct rte_acl_ctx* dynamic_v4_ctx_new[RTE_MAX_LCORE];
extern struct rte_acl_ctx* dynamic_v6_ctx_new[RTE_MAX_LCORE];

#include <string>

std::map<std::string, int> dynamic_key_index;
std::map<int, SDTRuleAction *> dynamic_index_action;

SDTRuleAction *dynamic_rule_by_index(int index)
{
    return dynamic_index_action[index];
}

int dynamic_rule_uuid_get(SDTRuleAction *ruleAction)
{
    std::string key;
    key += ruleAction->unitID;
    key += ruleAction->taskID;
    key += ruleAction->groupID;
    key += std::to_string(ruleAction->ruleID);

    auto find = dynamic_key_index.find(key);
    if(find == dynamic_key_index.end())
    {
        // 二次检查
        pthread_mutex_lock(&uuidMutex);
        int uuid = dynamic_key_index.size()+1;//从1开始
        if(dynamic_key_index.find(key) == dynamic_key_index.end())
        {
            dynamic_key_index[key] = uuid; //insert
            dynamic_index_action[uuid] = ruleAction;
        }
        printf("构建 %s --> %d\n", key.c_str(), uuid);
        pthread_mutex_unlock(&uuidMutex);
        return uuid;
    }
    printf("查询 %s --> %d\n", key.c_str(), find->second);
    return find->second;
}

int SdtApp_dynamic_rule_new(struct sdt_acl_t *acl, void *user)
{
    uint32_t hash = jhash(acl, sizeof(struct sdt_acl_t), JHASH_INITVAL);
    int var = dynamicRuleHash[hash];
    if(var)
    {
        //dynamic_show_sdt_acl(acl, stdout);
        //printf("已经反射过了. 忽略\n");
        return 1;
    }


    //新的
    //dynamic_show_sdt_acl(acl, stdout);
    //printf("动态规则. 允许\n");

    acl->userdata = dynamic_rule_uuid_get((SDTRuleAction *)user);
    sdt_actionDynamic_push(acl);

    struct rte_acl_ctx*rte_acl_ctx_v4 = dynamic_rule_build_ipv4_flex(dynamicRulev4.data(), dynamicRulev4.size());
    struct rte_acl_ctx*rte_acl_ctx_v6 = dynamic_rule_build_ipv6_flex(dynamicRulev4.data(), dynamicRulev4.size());

    //update -- 双指针切换线程安全
    for(int i = 0; i < RTE_MAX_LCORE; i++)
    {
        if(NULL == dynamic_v4_ctx_new[i])
            dynamic_v4_ctx_new[i] = rte_acl_ctx_v4;

        if(NULL == dynamic_v6_ctx_new[i])
            dynamic_v6_ctx_new[i] = rte_acl_ctx_v6;
    }
    return 0;
}
