#ifndef _DPI_FILE_RSM_H_
#define _DPI_FILE_RSM_H_

#ifdef __cplusplus
extern "C" {
#endif

//私有结构
struct file_rsm_t;

//初始化 size_of_offset 支持 16/32/64
struct file_rsm_t* file_rsm_init(int size_of_offset);

//成长
int file_rsm_push(struct file_rsm_t *file, uint64_t offset, uint64_t len);

//析构
void file_rsm_free(struct file_rsm_t *file);

//文件有效长度
uint64_t file_rsm_len(struct file_rsm_t *file);

//文件写入长度
uint64_t file_rsm_write(struct file_rsm_t *file);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
