#ifndef __DPI_PROTO_IDS_H__
#define __DPI_PROTO_IDS_H__


#include <stdio.h>
#include <stdint.h>



enum tbl_log_type {
    TBL_LOG_UNKNOWN,
    TBL_LOG_MAC_PHEADER,
    TBL_LOG_IPP,
    TBL_LOG_IP,
    TBL_LOG_UDP,
    TBL_LOG_TCP,
    TBL_LOG_LINK,
    TBL_LOG_HTTP,
    TBL_LOG_DNS,
    TBL_LOG_FTP_CONTROL,
    TBL_LOG_MAIL_SMTP,
    TBL_LOG_MAIL_ESMTP,
    TBL_LOG_SSL,
    TBL_LOG_MAIL_IMAP,
    TBL_LOG_MAIL_POP,
    TBL_LOG_SIP,
    TBL_LOG_WEIXIN,
    TBL_LOG_TELNET,
    TBL_LOG_SSH,
    TBL_LOG_TFTP,
    TBL_LOG_RIP,
    TBL_LOG_L2TP,
    TBL_LOG_PPTP,
    TBL_LOG_RADIUS,
    TBL_LOG_DTLS,
    TBL_LOG_CLASSICSTUN,
    TBL_LOG_STUN,
    TBL_LOG_MYSQL,
    TBL_LOG_TDS,
    TBL_LOG_H323,
    TBL_LOG_SMB,
    TBL_LOG_GTP_CONTROL,
    TBL_LOG_SNMP,
    TBL_LOG_ISAKMP,
    TBL_LOG_IAX2,
    TBL_LOG_GRE,
    TBL_LOG_ESP,
    TBL_LOG_SDP_L5,
    TBL_LOG_ISUP_L5,
    TBL_LOG_OSPF,
    TBL_LOG_DHCP,
    TBL_LOG_VNC,
    TBL_LOG_LDAP,
    TBL_LOG_CLDAP,
    TBL_LOG_DCERPC,
    TBL_LOG_FLOW,
    TBL_LOG_OCSP,
    TBL_LOG_RTSP,
    TBL_LOG_S1AP,
    TBL_LOG_EIGRP,
    TBL_LOG_CDP,
    TBL_LOG_RTP,
    TBL_LOG_AH,
    TBL_LOG_TURN,
    TBL_LOG_RDP,
    TBL_LOG_SKIP,
    TBL_LOG_X509,
    TBL_LOG_MGCP,
    TBL_LOG_MEGACO,
    TBL_LOG_KERBEROS,
    TBL_LOG_BGP,
    TBL_LOG_MOBILE_LOG,
    TBL_LOG_AH_N,
    TBL_LOG_ESP_N,
    TBL_LOG_LCP,
    TBL_LOG_CHAP,
    TBL_LOG_PAP,
    TBL_LOG_NSPI,
    TBL_LOG_CLOUDSTACK,
    TBL_LOG_LOTUS_NRPC,
    TBL_LOG_TEAMVIEWER,
    TBL_LOG_UDPENCAP,
    TBL_LOG_ICMP,
    TBL_LOG_ANYDESK,
    TBL_LOG_OPENVPN,
    TBL_LOG_RSVP,
    TBL_LOG_JT808,
    TBL_LOG_ISIS,
    TBL_LOG_LDP,
    TBL_LOG_SMPP,
    TBL_LOG_CMPP,
    TBL_LOG_SSDP,
    TBL_LOG_STP,
    TBL_LOG_RTCP,
    TBL_LOG_SCCP,
    TBL_LOG_OICQ,
    TBL_LOG_SFTP,
    TBL_LOG_SGMP,
    TBL_LOG_DNP3,
    TBL_LOG_S7COMM,
    TBL_LOG_IEC104,
    TBL_LOG_SCTP,
    TBL_LOG_FTPS,
    TBL_LOG_PGSQL,
    TBL_LOG_TNS,
    TBL_LOG_MODBUS,
    TBL_LOG_GTP_U,
    TBL_LOG_TEREDO,
    TBL_LOG_IPIP,
    TBL_LOG_TLL,
    TBL_LOG_VRRP,
    TBL_LOG_CFLOW,
    TBL_LOG_IGMP,
    TBL_LOG_DIAMETER,
    TBL_LOG_SHARE_HEADER,
    TBL_LOG_CWMP,
    TBL_LOG_SYSLOG,
    TBL_LOG_SOCKS,
    TBL_LOG_DBBASIC,
    TBL_LOG_ARP,
    TBL_LOG_EMAIL,
    TBL_LOG_COAP,
    TBL_LOG_P327_COMMON,
    TBL_LOG_MAX
};



enum PROTOCOL_TYPE {
    PROTOCOL_UNKNOWN,
    PROTOCOL_MAC_PHEADER,
    PROTOCOL_IPP,
    PROTOCOL_IP,
    PROTOCOL_UDP,
    PROTOCOL_TCP,
    PROTOCOL_LINK,
    PROTOCOL_HTTP,
    PROTOCOL_DNS,
    PROTOCOL_FTP_CONTROL,
    PROTOCOL_FTP_DATA,
    PROTOCOL_MAIL_SMTP,
    PROTOCOL_MAIL_ESMTP,
    PROTOCOL_SSL,
    PROTOCOL_MAIL_IMAP,
    PROTOCOL_MAIL_POP,
    PROTOCOL_SIP,
    PROTOCOL_WEIXIN,
    PROTOCOL_TELNET,
    PROTOCOL_SSH,
    PROTOCOL_TFTP,
    PROTOCOL_RIP,
    PROTOCOL_L2TP,
    PROTOCOL_PPTP,
    PROTOCOL_RADIUS,
    PROTOCOL_DTLS,
    PROTOCOL_CLASSICSTUN,
    PROTOCOL_STUN,
    PROTOCOL_MYSQL,
    PROTOCOL_TDS,
    PROTOCOL_H323,
    PROTOCOL_SMB,
    PROTOCOL_GTP_CONTROL,
    PROTOCOL_SNMP,
    PROTOCOL_ISAKMP,
    PROTOCOL_IAX2,
    PROTOCOL_GRE,
    PROTOCOL_ESP,
    PROTOCOL_SDP_L5,
    PROTOCOL_ISUP_L5,
    PROTOCOL_OSPF,
    PROTOCOL_DHCP,
    PROTOCOL_VNC,
    PROTOCOL_LDAP,
    PROTOCOL_CLDAP,
    PROTOCOL_DCERPC,
    PROTOCOL_FLOW,
    PROTOCOL_OCSP,
    PROTOCOL_RTSP,
    PROTOCOL_S1AP,
    PROTOCOL_EIGRP,
    PROTOCOL_CDP,
    PROTOCOL_RTP,
    PROTOCOL_AH,
    PROTOCOL_TURN,
    PROTOCOL_RDP,
    PROTOCOL_SKIP,
    PROTOCOL_X509,
    PROTOCOL_MGCP,
    PROTOCOL_MEGACO,
    PROTOCOL_KERBEROS,
    PROTOCOL_BGP,
    PROTOCOL_MOBILE_LOG,
    PROTOCOL_AH_N,
    PROTOCOL_ESP_N,
    PROTOCOL_LCP,
    PROTOCOL_CHAP,
    PROTOCOL_PAP,
    PROTOCOL_NSPI,
    PROTOCOL_CLOUDSTACK,
    PROTOCOL_LOTUS_NRPC,
    PROTOCOL_TEAMVIEWER,
    PROTOCOL_UDPENCAP,
    PROTOCOL_ICMP,
    PROTOCOL_ANYDESK,
    PROTOCOL_OPENVPN,
    PROTOCOL_RSVP,
    PROTOCOL_JT808,
    PROTOCOL_ISIS,
    PROTOCOL_LDP,
    PROTOCOL_SMPP,
    PROTOCOL_CMPP,
    PROTOCOL_SSDP,
    PROTOCOL_STP,
    PROTOCOL_RTCP,
    PROTOCOL_SCCP,
    PROTOCOL_OICQ,
    PROTOCOL_SFTP,
    PROTOCOL_SGMP,
    PROTOCOL_DNP3,
    PROTOCOL_S7COMM,
    PROTOCOL_IEC104,
    PROTOCOL_SCTP,
    PROTOCOL_FTPS,
    PROTOCOL_PGSQL,
    PROTOCOL_TNS,
    PROTOCOL_MODBUS,
    PROTOCOL_GTP_U,
    PROTOCOL_TEREDO,
    PROTOCOL_IPIP,
    PROTOCOL_TLL,
    PROTOCOL_VRRP,
    PROTOCOL_CFLOW,
    PROTOCOL_IGMP,
    PROTOCOL_DIAMETER,
    PROTOCOL_SHARE_HEADER,
    PROTOCOL_CWMP,
    PROTOCOL_SYSLOG,
    PROTOCOL_SOCKS,
    PROTOCOL_DBBASIC,
    PROTOCOL_ARP,
    PROTOCOL_EMAIL,
    PROTOCOL_COAP,
    PROTOCOL_P327_COMMON,
    PROTOCOL_MAX
};






typedef void (*call_dissector_init_func)(void);



struct tbl_log_file
{
    enum tbl_log_type type;
    int has_content;
    const char *protoname;
    FILE *fp_tbl[32];
    uint8_t is_empty[32];   // 打开文件是否为空 注意：json会默认写头，如果没写入value值默认也为空文件
    FILE *fp_content;
    unsigned int  content_offset;
    unsigned int  log_num[32];
    unsigned int  timeout_sec[32];
    char filename[32][128];
    call_dissector_init_func init_func;
};



extern struct tbl_log_file tbl_log_array[TBL_LOG_MAX];



#endif
