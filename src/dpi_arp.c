/****************************************************************************************
 * 文 件 名 : dpi_arp.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 * 设计: huangzw       2022/05/05
 * 编码: huangzw       2022/05/06
 * 修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <rte_mbuf.h>

#include "dpi_typedefs.h"
#include "dpi_proto_ids.h"
#include "dpi_common.h"
#include "sdt_ip_protocols.h"
#include "dpi_log.h"
#include "dpi_tbl_log.h"
#include "dpi_dissector.h"
#include "dpi_etypes.h"
#include "dpi_flow.h"
#include <libsdt/libsdt_interface.h>
#include "dpi_sdt_match.h"
#include "sdt_action_out.h"

extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;


/* ARP protocol HARDWARE identifiers. */
#define ARPHRD_NETROM	0		/* from KA9Q: NET/ROM pseudo	*/
#define ARPHRD_ETHER 	1		/* Ethernet 10Mbps		*/
#define	ARPHRD_EETHER	2		/* Experimental Ethernet	*/
#define	ARPHRD_AX25	3		/* AX.25 Level 2		*/
#define	ARPHRD_PRONET	4		/* PROnet token ring		*/
#define	ARPHRD_CHAOS	5		/* Chaosnet			*/
#define	ARPHRD_IEEE802	6		/* IEEE 802.2 Ethernet/TR/TB	*/
#define	ARPHRD_ARCNET	7		/* ARCnet			*/
#define	ARPHRD_HYPERCH	8		/* Hyperchannel			*/
#define	ARPHRD_LANSTAR	9		/* Lanstar			*/
#define	ARPHRD_AUTONET	10		/* Autonet Short Address	*/
#define	ARPHRD_LOCALTLK	11		/* Localtalk			*/
#define	ARPHRD_LOCALNET	12		/* LocalNet (IBM PCNet/Sytek LocalNET) */
#define	ARPHRD_ULTRALNK	13		/* Ultra link			*/
#define	ARPHRD_SMDS	14		/* SMDS				*/
#define ARPHRD_DLCI	15		/* Frame Relay DLCI		*/
#define ARPHRD_ATM	16		/* ATM				*/
#define ARPHRD_HDLC	17		/* HDLC				*/
#define ARPHRD_FIBREC	18		/* Fibre Channel		*/
#define ARPHRD_ATM2225	19		/* ATM (RFC 2225)		*/
#define ARPHRD_SERIAL	20		/* Serial Line			*/
#define ARPHRD_ATM2	21		/* ATM				*/
#define ARPHRD_MS188220	22		/* MIL-STD-188-220		*/
#define ARPHRD_METRICOM	23		/* Metricom STRIP		*/
#define ARPHRD_IEEE1394	24		/* IEEE 1394.1995		*/
#define ARPHRD_MAPOS	25		/* MAPOS			*/
#define ARPHRD_TWINAX	26		/* Twinaxial			*/
#define ARPHRD_EUI_64	27		/* EUI-64			*/
#define ARPHRD_HIPARP	28		/* HIPARP			*/
#define ARPHRD_IP_ARP_ISO_7816_3	29	/* IP and ARP over ISO 7816-3	*/
#define ARPHRD_ARPSEC			30	/* ARPSec			*/
#define ARPHRD_IPSEC_TUNNEL		31	/* IPsec tunnel			*/
#define ARPHRD_INFINIBAND		32	/* InfiniBand			*/
#define ARPHRD_TIA_102_PRJ_25_CAI	33	/* TIA-102 Project 25 CAI	*/
#define ARPHRD_WIEGAND_INTERFACE	34	/* Wiegand Interface		*/
#define ARPHRD_PURE_IP			35	/* Pure IP			*/
#define ARPHDR_HW_EXP1			36	/* HW_EXP1			*/
#define ARPHDR_HFI			37	/* HFI				*/
#define ARPHDR_HW_EXP2			256	/* HW_EXP2			*/

/* Virtual ARP types for non ARP hardware used in Linux cooked mode. */
#define ARPHRD_LOOPBACK	772		/* Loopback */
#define ARPHRD_IPGRE	778		/* GRE over IP */
#define ARPHRD_NETLINK	824		/* netlink */

static const struct int_to_string etype_vals[] = {
	{ ETHERTYPE_IP,                   "IPv4" },
	{ ETHERTYPE_VLAN,                 "802.1Q Virtual LAN" },
	{ ETHERTYPE_ARP,                  "ARP" },
	{ ETHERTYPE_REVARP,               "RARP" },
	{ ETHERTYPE_AARP,                 "AARP" },
	{ ETHERTYPE_IPX,                  "Netware IPX/SPX" },
	{ ETHERTYPE_CDMA2000_A10_UBS,     "CDMA2000 A10 Unstructured byte stream" },
	{ ETHERTYPE_3GPP2,                "CDMA2000 A10 3GPP2 Packet" },
	{ PPP_IPCP,                       "PPP IP Control Protocol" },
	{ PPP_LCP,                        "PPP Link Control Protocol" },
	{ PPP_PAP,                        "PPP Password Authentication Protocol" },
	{ 0, NULL }
};

static const struct int_to_string arp_hrd_vals[] = {
  {ARPHRD_NETROM,             "NET/ROM pseudo"             },
  {ARPHRD_ETHER,              "Ethernet"                   },
  {ARPHRD_EETHER,             "Experimental Ethernet"      },
  {ARPHRD_AX25,               "AX.25"                      },
  {ARPHRD_PRONET,             "ProNET"                     },
  {ARPHRD_CHAOS,              "Chaos"                      },
  {ARPHRD_IEEE802,            "IEEE 802"                   },
  {ARPHRD_ARCNET,             "ARCNET"                     },
  {ARPHRD_HYPERCH,            "Hyperchannel"               },
  {ARPHRD_LANSTAR,            "Lanstar"                    },
  {ARPHRD_AUTONET,            "Autonet Short Address"      },
  {ARPHRD_LOCALTLK,           "Localtalk"                  },
  {ARPHRD_LOCALNET,           "LocalNet"                   },
  {ARPHRD_ULTRALNK,           "Ultra link"                 },
  {ARPHRD_SMDS,               "SMDS"                       },
  {ARPHRD_DLCI,               "Frame Relay DLCI"           },
  {ARPHRD_ATM,                "ATM"                        },
  {ARPHRD_HDLC,               "HDLC"                       },
  {ARPHRD_FIBREC,             "Fibre Channel"              },
  {ARPHRD_ATM2225,            "ATM (RFC 2225)"             },
  {ARPHRD_SERIAL,             "Serial Line"                },
  {ARPHRD_ATM2,               "ATM"                        },
  {ARPHRD_MS188220,           "MIL-STD-188-220"            },
  {ARPHRD_METRICOM,           "Metricom STRIP"             },
  {ARPHRD_IEEE1394,           "IEEE 1394.1995"             },
  {ARPHRD_MAPOS,              "MAPOS"                      },
  {ARPHRD_TWINAX,             "Twinaxial"                  },
  {ARPHRD_EUI_64,             "EUI-64"                     },
  {ARPHRD_HIPARP,             "HIPARP"                     },
  {ARPHRD_IP_ARP_ISO_7816_3,  "IP and ARP over ISO 7816-3" },
  {ARPHRD_ARPSEC,             "ARPSec"                     },
  {ARPHRD_IPSEC_TUNNEL,       "IPsec tunnel"               },
  {ARPHRD_INFINIBAND,         "InfiniBand"                 },
  {ARPHRD_TIA_102_PRJ_25_CAI, "TIA-102 Project 25 CAI"     },
  {ARPHRD_WIEGAND_INTERFACE,  "Wiegand Interface"          },
  {ARPHRD_PURE_IP,            "Pure IP"                    },
  {ARPHDR_HW_EXP1,            "Experimental 1"             },
  {ARPHDR_HFI,                "HFI"                        },
  {ARPHDR_HW_EXP2,            "Experimental 2"             },
  /* Virtual ARP types for non ARP hardware used in Linux cooked mode. */
  {ARPHRD_LOOPBACK,           "Loopback"                   },
  {ARPHRD_IPGRE,              "GRE over IP"                },
  {ARPHRD_NETLINK,            "Netlink"                    },
  {0, NULL                  }
};

enum arp_index_em{
	EM_ARP_HRD_TYPE,
	EM_ARP_PRO_TYPE,
	EM_ARP_HRD_LEN,
	EM_ARP_PRO_LEN,
	EM_ARP_OPCODE,
	EM_ARP_SENDER_MAC,
	EM_ARP_SENDER_IP,
	EM_ARP_TARGET_MAC,
	EM_ARP_TARGET_IP,

	EM_ARP_MAX
};

static dpi_field_table  arp_field_array[] = {
	DPI_FIELD_D(EM_ARP_HRD_TYPE,       EM_F_TYPE_UINT16,	"HardwareType"),
	DPI_FIELD_D(EM_ARP_PRO_TYPE,       EM_F_TYPE_STRING,	"ProtocolType"),
	DPI_FIELD_D(EM_ARP_HRD_LEN,        EM_F_TYPE_UINT8,     "HardwareSize"),
	DPI_FIELD_D(EM_ARP_PRO_LEN,        EM_F_TYPE_UINT8,     "ProtocolSize"),
	DPI_FIELD_D(EM_ARP_OPCODE,         EM_F_TYPE_UINT16,	"Opcode"),
	DPI_FIELD_D(EM_ARP_SENDER_MAC,     EM_F_TYPE_UINT64,	"SenderMAC"),
	DPI_FIELD_D(EM_ARP_SENDER_IP,      EM_F_TYPE_UINT32,	"SenderIP"),
	DPI_FIELD_D(EM_ARP_TARGET_MAC,     EM_F_TYPE_UINT64,	"TargetMAC"),
	DPI_FIELD_D(EM_ARP_TARGET_IP,      EM_F_TYPE_UINT32,	"TargetIP"),
};

struct arp_info{
	uint16_t      hrd_type;
	uint16_t      pro_type;
	uint8_t       hrd_len;
	uint8_t       pro_len;
	uint16_t      opcode;
    uint64_t      sender_mac;
	uint32_t      sender_ip;
    uint64_t      target_mac;
	uint32_t      target_ip;
};

static int _write_arp_log(precord_t * record, struct flow_info *flow, int direction, void *field_info)
{
  int              i;
  int              idx  = 0;
  const uint8_t   *str  = NULL;
  struct arp_info *info = (struct arp_info *)field_info;

  player_t *layer = precord_layer_put_new_layer(record, "arp");

  static dpi_field_table *table = arp_field_array;
  for (i = 0; i < EM_ARP_MAX; i++) {
    switch (i) {
      case EM_ARP_HRD_TYPE:
        write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, table[i].type, NULL, info->hrd_type);
        break;
      case EM_ARP_PRO_TYPE:
        str = (const uint8_t *)val_to_string(info->pro_type, etype_vals);
        write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, table[i].type, str, strlen((const char *)str));
        break;
      case EM_ARP_HRD_LEN:
        write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, table[i].type, NULL, info->hrd_len);
        break;
      case EM_ARP_PRO_LEN:
        write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, table[i].type, NULL, info->pro_len);
        break;
      case EM_ARP_OPCODE:
        write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, table[i].type, NULL, info->opcode);
        break;
      case EM_ARP_SENDER_MAC:
        write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, table[i].type, NULL, info->sender_mac);
        break;
      case EM_ARP_SENDER_IP:
        write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, table[i].type, NULL, info->sender_ip);
        break;
      case EM_ARP_TARGET_MAC:
        write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, table[i].type, NULL, info->target_mac);
        break;
      case EM_ARP_TARGET_IP:
        write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, table[i].type, NULL, info->target_ip);
        break;
      default:
        write_coupler_log(record, &idx, TBL_LOG_MAX_LEN, EM_F_TYPE_EMPTY, NULL, 1);
        break;
    }
  }
    return 0;
}


static int  write_arp_log(struct flow_info *flow, int direction, void *field_info, SdtMatchResult *match_result)
{
    int i;
    int idx = 0;
    struct tbl_log *log_ptr;
    const uint8_t *str = NULL;

    if (rte_mempool_get(tbl_log_mempool, (void **)&log_ptr) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "not enough memory: tbl_log_mempool");
        return -1;
    }

    flow->real_protocol_id=PROTOCOL_ARP;

    init_log_ptr_data(log_ptr, flow,EM_TBL_LOG_ID_BY_DEFAULT);
    dpi_precord_new_record(log_ptr->record, NULL, NULL);
    write_tbl_log_common(flow, direction, log_ptr, &idx, TBL_LOG_MAX_LEN, match_result);

    _write_arp_log(log_ptr->record, flow, direction, field_info);

    log_ptr->thread_id   = flow->thread_id;
    log_ptr->log_type    = TBL_LOG_ARP;
    log_ptr->log_len     = idx;
    log_ptr->content_len = 0;
    log_ptr->content_ptr = NULL;
    log_ptr->proto_id    = PROTOCOL_ARP;
    log_ptr->out_elem    = sdt_rule_hash_db_lookup(match_result);
    log_ptr->out_elem->match_result = match_result;

    dpi_app_match_res_enqueue(log_ptr);
    dpi_tbl_free(log_ptr);
    return 0;
}

int arp_sdx_hit_action(int action, const struct pkt_info  *pkt, struct flow_info *flow, int direction, struct arp_info *info, SdtMatchResult *result, sdt_out_status  *flow_rule_status)
{
        switch(action)
        {
        case SAE_none:
            break;
        case SAE_drop:
            /*删掉缓存节点包*/
            break;
        case SAE_event:
            /*event 输出*/
            //_sdt_handle_event_match(flow, direction, result, flow_rule_status, PROTOCOL_ARP, &sdt_flag);
            break;
        case SAE_report:
            write_arp_log(flow, direction, info, result);
            break;
        case SAE_packetDump:
            sdt_in_pcap(result, pkt);
            break;
        default:
            break;
        }
    return 0;
}

static int dpi_sdt_arp_match(struct flow_info *flow, struct arp_info *info, const struct pkt_info  *pkt, int direction, SdtAclMatchedRuleInfo  *acl_info)
{
    int i;
    SdtMatchResult**match_results    = NULL;
    int            rule_nums        = 0;
    int            ret=PKT_DROP;
    int idx = 0;

    ProtoRecord pRec;
    memset(&pRec, 0, sizeof(ProtoRecord));
    dpi_precord_new_record(pRec.record, NULL, NULL);

    write_shared_header(pRec.record, TBL_LOG_MAX_LEN, flow, direction);

    FieldValue_t  arp_value_list[EM_ARP_MAX];
    memset(arp_value_list, 0, sizeof(arp_value_list));

    //填充 ARP 解析的数据
    _write_arp_log(pRec.record, flow, direction, info);

    // 是否应该从 schema 中获取
    pRec.proto_id      = PROTOCOL_ARP;

    pRec.direction     = direction;

    strcpy(pRec.proto_name,"Ethernet");
    strcpy(pRec.proto_name,"arp");

    for (unsigned acl_index=0; acl_index < acl_info->aclHashCnt; acl_index++)
    {
        uint32_t acl_hash = acl_info->aclHashCode[acl_index];

        match_results=sdtEngine_matchSdtRules_v2(flow->sdt_flow.pEngine, IPFF_ACL_KEYWORD0_RULEBODY1, acl_hash, &pRec, &rule_nums);

        if(match_results && rule_nums>0){
            sdx_match_status_add(flow->thread_id, PROTOCOL_ARP, 1);
            if(1==g_config.match_status_switch){
            }
        }else{
            if(2==g_config.match_status_switch){
            }
            continue;
        }

        for(i=0;i<rule_nums;i++){
            SdtMatchResult *hit_results = match_results[i];
            sdt_out_status  *flow_rule_status=sdt_rule_hash_db_lookup(hit_results);

            for(int idx = 0; idx < CHAR_BIT * (int)sizeof(hit_results->action); idx++)
            {
                uint32_t mask = 1 << idx;
                if(hit_results->action & mask)
                {
                    arp_sdx_hit_action(mask, pkt, flow, direction, info, hit_results, flow_rule_status);
                }
            }

            if(hit_results->action & (1<<8)){ //告警日志
                sdt_in_syslog(flow, hit_results);
            }

            flow_rule_status->flag = 1;
            sdt_statistic_datalink_match_result(hit_results, flow_rule_status, flow->thread_id, (struct pkt_info *)pkt);

            dpi_match_result_log(hit_results, pkt, &pRec);
        }
    }

    precord_destroy(pRec.record);

    return 0;
}


int dpi_dissect_arp(struct flow_info *flow, int direction, const uint8_t *payload, uint32_t payload_len, uint8_t flag)
{
    if(g_config.protocol_switch[PROTOCOL_ARP] == 0)
        return PKT_OK;

    if(payload_len < 28)
        return PKT_DROP;

    struct arp_info info;
    bzero(&info, sizeof(struct arp_info));
    uint32_t offset = 0;

    info.hrd_type = get_uint16_ntohs(payload, offset);
    offset = offset + 2;

    info.pro_type = get_uint16_ntohs(payload, offset);
    offset = offset + 2;

    info.hrd_len = get_uint8_t(payload, offset);
    offset = offset + 1;

    info.pro_len = get_uint8_t(payload, offset);
    offset = offset + 1;

    info.opcode = get_uint16_ntohs(payload, offset);
    offset = offset + 2;
    if(info.opcode == 1)
        direction = 0;
    else
        direction = 1;

    if(g_config._327_common_switch){
        memcpy(&flow->p327_header[direction], &flow->p327_header[0], sizeof(flow->p327_header[0]));
    }
    info.sender_mac = *(uint64_t*)(payload + offset) & 0x0000FFFFFFFFFFFFUL; //抹去高位多余数据
    offset = offset + 6;

    info.sender_ip= get_uint32_t(payload, offset);// 参考附录B.4.1 某筛选还原设备, P77, IP要转小端
    offset = offset + 4;

    info.target_mac = *(uint64_t*)(payload + offset) & 0x0000FFFFFFFFFFFFUL;//抹去高位多余数据;
    offset = offset + 6;

    info.target_ip= get_uint32_t(payload, offset);// 参考附录B.4.1 某筛选还原设备, P77, IP要转小端
    offset = offset + 4;

    SdtAclMatchedRuleInfo  *acl_arp_ret=NULL;
    struct pkt_tuple_t  pkt;
    memset(&pkt, 0, sizeof(struct pkt_tuple_t));
    if(ETH_P_IP==info.pro_type){
        pkt.af=AF_INET;
        flow->ip_version=4;
    }else if(ETH_P_IPV6==info.pro_type){
        pkt.af=AF_INET6;
        flow->ip_version=6;
    }else{
        return PKT_DROP;
    }

    //1 ACL 匹配
    acl_arp_ret=sdtEngine_matchAclRules(flow->sdt_flow.pEngine, &pkt);
    if(acl_arp_ret==NULL){
        return PKT_DROP;
    }
    //如果ACL HASH 没有被命中 -- 拜拜~
    if(0 == acl_arp_ret->aclHashCnt)
    {
        return PKT_DROP;
    }

    // ARP 规则匹配
    dpi_sdt_arp_match(flow, &info, flow->pkt,  direction, acl_arp_ret);

    return PKT_OK;
}



static void init_arp_dissector(void)
{
    dpi_register_proto_schema(arp_field_array,   EM_ARP_MAX,   "arp");

    map_fields_info_register(arp_field_array, PROTOCOL_ARP, EM_ARP_MAX, "arp");

    return;
}

static __attribute((constructor)) void    before_init_arp(void){
    register_tbl_array(TBL_LOG_ARP, 0, "arp", init_arp_dissector);
}

