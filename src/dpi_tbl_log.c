/****************************************************************************************
 * 文 件 名 : dpi_tbl_log.c
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/07/06
编码: wangy            2018/07/06
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/


#include <rte_ring.h>
#include <stdio.h>
#include <unistd.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/time.h>
#include <string.h>
#include <arpa/inet.h>
#include <rte_mempool.h>
#include <errno.h>
#include <maxminddb.h>
#include <stdlib.h>
#include <assert.h>
#include <fcntl.h>
#include <string.h>
#include <time.h>
#include <endian.h>
#include <glib.h>
#include "dpi_proto_ids.h"  // add by liugh
#include "dpi_tbl_log.h"
#include "dpi_log.h"
#include "dpi_common.h"
#include "dpi_trailer.h"
#include "ip2region.h"
#include "cJSON.h"
#include "sdx_rdkafka_producer_consumer.h"
#include "dpi_sdt_link.h"
#include "dpi_detect.h"
#include "dpi_utils.h"
#include "sdt_action_out.h"
#include "dpi_pschema.h"
#include "dpi_zdy_output.h"
#include "dpi_lua_adapt.h"
#include "dpi_flow.h"
#include "dpi_numa.h"
#include "dpi_dpdk_wrapper.h"
#ifdef ENABLE_ARKIME
#include "arkime/dpi_arkime_if.h"
#endif

extern rte_atomic64_t app_match_fail_pkts;
extern rte_atomic64_t tbl_fail_pkts;
extern rte_atomic64_t tbl_fail_bytes;

uint64_t record_new_cnt;
uint64_t record_destroy_cnt;

extern struct global_config g_config;
extern struct rte_mempool *tbl_log_mempool;
extern struct rte_mempool *tbl_log_content_mempool_256k;
extern struct work_process_data flow_thread_info[MAX_FLOW_THREAD_NUM];

extern struct ext_proto_st        *prot_tbl;
extern uint16_t                   prot_tbl_num ;

extern pid_t gettid(void);

YV_kafka_handle_t *g_kafka_handle = NULL;
struct tbl_log_file tbl_log_array[TBL_LOG_MAX];
__thread GHashTable *tbl_log_file_table = NULL;   //用于存放写tbl_log相关信息
struct tbl_log_file plugin_tbl_log_array[TBL_LOG_MAX];

JsonField   g_json_field[PROTOCOL_MAX];         // 根据协议号存储

struct protocol_fields_gather
{
    int        protocol_field_array_num;
    dpi_field_table *protocol_field_array;  // 指向协议字段表
    GHashTable *protocol_hash;        //字段hash表，key-->字段， value-->索引

    int *protocol_reflect_array;           // 存储json映射表中映射客户字段顺，每个内容存储协议字段索引位置；
    int  protocol_reflect_array_num;       // 映射客户字段数量
    char *protocol_reflect_protoname;    // 映射客户协议名称
};

struct protocol_fields_gather manager_map_fields[PROTOCOL_MAX];

int tbl_out_thfunc_signal = 0;



const char *email_heads[] = {
    "MAIL FROM",
    "RCPT TO",
    "Received",
    "Date",
    "From",
    "To",
    "Cc",
    "Bcc",
    "Content-Language",
    "Content-Type",
    "Thread-Index",
    "Subject",
    "Message-ID",
    "X-CM-TRANSID",
    "X-Coremail-Antispam",
    "X-Originating-IP",
    "X-CM-SenderInfo",
    "In-Reply-To",
    "X-Y-GMX-Trusted",
    "X-imss-version",
    "X-imss-result",
    "X-imss-scores",
    "X-imss-settings",
    "Return-Path",
    "X-OriginalArrivalTime",
    "X-Priority",
    "X-GUID",
    "X-Has-Attach",
    "X-Mailer",
    "X-Mimeole",
    "X-Ms-Tnef-Correlator",
    "X-Uidl",
    "X-Authentication-Warning",
    "X-Virus-Scanned",
    "X400-Content-Identifier",
    "X400-Content-Type",
    "X400-Mts-Identifier",
    "X400-Originator",
    "X400-Received",
    "X400-Recipients",
    NULL
};

const char *email_cmds[] = {
    // basic
    "HELO",
    "MAIL FROM",
    "RCPT TO",
    "DATA",
    "RSET",
    "VRFY",
    "NOOP",
    "QUIT",

    // extended
    "EHLO",
    "AUTH",
    "STARTTLS",
    "SIZE",
    "HELP",
    "DSN",
    "TURN",
    "ATRN",
    "ETRN",
    "PIPELINING",
    "CHUNKING",
    "X-EXPS GSSAPI",
    "X-EXPS=LOGIN",
    "X-EXCH50",
    "X-LINK2STATE",

    // other
    "CAPA",
    //    "USER",
    //    "PASS",
    "STAT",
    "LIST",
    "UIDL",
    "RETR",
    "DELE",

    NULL
};

static void tbl_log_debug_print(precord_t * record)
{
    // 遍历各个 layer 的所有字段
    for (player_t *layer = precord_layer_get_first(record);
         layer != NULL;
         layer = precord_layer_get_next(record, layer)
        )
    {
        printf("layer %s\n", precord_layer_get_layer_name(layer));

        for (pfield_t *field = precord_field_get_first_from_layer_of(layer);
             field != NULL;
             field = precord_field_get_next_from_layer_of(layer, field))
        {
            pfield_desc_t *desc  = precord_field_get_fdesc(field);
            ya_fvalue_t   *value = precord_field_get_fvalue(field);

            if (NULL == value)
            {
                printf("fname: %s, fvalue: %s, ftype:%s\n",
                       pfdesc_get_name(desc),
                       "_NONE_",
                       pfdesc_get_type_name(desc));
                continue;
            }

            char *value_string = precord_field_fvalue_to_string(field);
            printf("fname: %s, fvalue: %s, ftype:%s\n", pfdesc_get_name(desc), value_string, ya_fvalue_type_name(value));
            ya_fvalue_free_string_repr(value_string);
        }
    }
}

struct tbl_log * dpi_tbl_create(struct tbl_log** tbl)
{
    int ret = 0;
    ret = rte_mempool_get(tbl_log_mempool, (void **)tbl);
    if (ret) {
        log_trace("dpi_tbl_create: rte_mempool_get failed, ret=%d", ret);
        return NULL;
    }
    rte_atomic16_set(&((*tbl)->ref_cnt), 1);
    (*tbl)->match_res_cnt = 0;
    return *tbl;
}

struct tbl_log * dpi_tbl_clone_ref(struct tbl_log *tbl)
{
    if (tbl) {
        rte_atomic16_inc(&tbl->ref_cnt);
        dpi_flow_clone(tbl->flow);
    }
    return tbl;
}

void dpi_tbl_free(struct tbl_log *tbl)
{
    if (tbl) {
        // rte_atomic16_dec(&tbl->ref_cnt);
        dpi_flow_free(tbl->flow, dpi_flow_timeout_free);
        if (rte_atomic16_dec_and_test(&tbl->ref_cnt)) {
            if (tbl->record) {
                precord_destroy(tbl->record);
                tbl->record = NULL;
            }
#ifdef DPI_FUTURE_MBUF
            if (tbl->pkt.mbuf) {
                rte_pktmbuf_free(tbl->pkt.mbuf);
            }
#endif //DPI_FUTURE_MBUF
            rte_mempool_put(tbl_log_mempool, tbl);
            tbl = NULL;
        }
    }
}

void tbl_log_file_close_writing(void)
{
    int i,thread_id;
    for (i = 0; i < TBL_LOG_MAX; i++) {
        for(thread_id=0; thread_id<(int)g_config.dissector_thread_num;thread_id++){
           /* 有文件，超时或超数 */
            if (NULL==tbl_log_array[i].fp_tbl[thread_id]) {
                continue;
            }
            tbl_log_array[i].log_num[thread_id] = 0;
            fclose(tbl_log_array[i].fp_tbl[thread_id]);
            tbl_log_array[i].fp_tbl[thread_id] = NULL;

            char filename[COMMON_FILE_PATH]={0};
            char filename_rename[COMMON_FILE_PATH]={0};
            snprintf(filename, sizeof(filename), "%s.tbl.writing", tbl_log_array[i].filename[thread_id]);
            /* 为空则删除,否则改名 */
            struct stat st;
            if(stat(filename, &st) == 0 && st.st_size == 0){
                remove(filename);
            }
            else{
                snprintf(filename_rename, sizeof(filename_rename), "%s.tbl", tbl_log_array[i].filename[thread_id]);
                rename(filename, filename_rename);
                printf("文件写入完成: %s\n", filename_rename);
            }
        }
    }
}


void  register_plugin_tbl_array(enum tbl_log_type type, int content, const char *name,call_dissector_init_func func )
{
    plugin_tbl_log_array[type].type=type;
    plugin_tbl_log_array[type].has_content=content;
    plugin_tbl_log_array[type].protoname=name;
    plugin_tbl_log_array[type].init_func=func;
}

void register_tbl_array(enum tbl_log_type type, int content, const char *name,call_dissector_init_func func )
{
    tbl_log_array[type].type=type;
    tbl_log_array[type].has_content=content;
    tbl_log_array[type].protoname=name;
    tbl_log_array[type].init_func=func;
}

dpi_field_table dpi_sdt_rule_field[] = {
    DPI_FIELD_D(EM_SDT_RULE_ID,                   EM_F_TYPE_UINT32,               "SDT_RULE_ID"),
    DPI_FIELD_D(EM_SDT_RULE_HASH_CODE,            EM_F_TYPE_UINT32,               "SDT_RULE_HASH_CODE"),
    DPI_FIELD_D(EM_SDT_RULE_GID,                  EM_F_TYPE_UINT32,               "SDT_RULE_GID"),
    DPI_FIELD_D(EM_SDT_RULE_UUID,                 EM_F_TYPE_UINT32,               "SDT_RULE_UUID"),
    DPI_FIELD_D(EM_SDT_RULE_SCORE,                EM_F_TYPE_UINT32,               "SDT_RULE_SCORE"),
    DPI_FIELD_D(EM_SDT_RULE_MSG,                  EM_F_TYPE_STRING,               "SDT_RULE_MSG"),
    DPI_FIELD_D(EM_SDT_RULE_CLASSTYPE,            EM_F_TYPE_STRING,               "SDT_RULE_CLASSTYPE"),
    DPI_FIELD_D(EM_SDT_RULE_REFERENCE,            EM_F_TYPE_STRING,               "SDT_RULE_REFERENCE"),
    DPI_FIELD_D(EM_SDT_RULE_ETAGS,                EM_F_TYPE_STRING,              "SDT_RULE_ETAGS"),
    DPI_FIELD_D(EM_SDT_RULE_TTAGS,                EM_F_TYPE_STRING,              "SDT_RULE_ETAGS"),
};


dpi_field_table dpi_common_field_trailer[] = {
    #include "dpi_common_field_trailer.h"           /* 从 TAGTYPE 到 resv7 */
};

dpi_field_table dpi_common_field[] = {
    #include "dpi_common_field_trailer.h"
    DPI_FIELD_D(EM_COMMON_RESV8,                     EM_F_TYPE_EMPTY,                "resv8"),
    DPI_FIELD_D(EM_COMMON_CAPDATE,                   EM_F_TYPE_STRING,               "CapDate"),
    DPI_FIELD_D(EM_COMMON_SRCIP,                     EM_F_TYPE_STRING,               "SrcIp"),
    DPI_FIELD_D(EM_COMMON_SRCCOUNTRY,                EM_F_TYPE_EMPTY,                "SrcCountry"),
    DPI_FIELD_D(EM_COMMON_SRCAREA,                   EM_F_TYPE_EMPTY,                "SrcArea"),
    DPI_FIELD_D(EM_COMMON_SRCCITY,                   EM_F_TYPE_EMPTY,                "SrcCity"),
    DPI_FIELD_D(EM_COMMON_SRCCARRIER,                EM_F_TYPE_EMPTY,                "SrcCarrier"),
    DPI_FIELD_D(EM_COMMON_DSTIP,                     EM_F_TYPE_STRING,               "DstIp"),
    DPI_FIELD_D(EM_COMMON_DSTCOUNTRY,                EM_F_TYPE_EMPTY,                "DstCountry"),
    DPI_FIELD_D(EM_COMMON_DSTAREA,                   EM_F_TYPE_EMPTY,                "DstArea"),
    DPI_FIELD_D(EM_COMMON_DSTCITY,                   EM_F_TYPE_EMPTY,                "DstCity"),
    DPI_FIELD_D(EM_COMMON_DSTCARRIER,                EM_F_TYPE_EMPTY,                "DstCarrier"),
    DPI_FIELD_D(EM_COMMON_SRCPORT,                   EM_F_TYPE_UINT16,               "SrcPort"),
    DPI_FIELD_D(EM_COMMON_DSTPORT,                   EM_F_TYPE_UINT16,               "DstPort"),
    DPI_FIELD_D(EM_COMMON_C2S,                       EM_F_TYPE_STRING,               "C2S"),
    DPI_FIELD_D(EM_COMMON_PROTO,                     EM_F_TYPE_UINT8,                "Proto"),
    DPI_FIELD_D(EM_COMMON_TTL,                       EM_F_TYPE_UINT8,                "TTL"),
};

dpi_field_table  dbbasic_field_array[] = {
    DPI_FIELD_D(EM_DBBASIC_DBTYPE,                   YA_FT_UINT32,                   "DbType"),
    DPI_FIELD_D(EM_DBBASIC_USERNAME,                 YA_FT_STRING,                   "UserName"),
    DPI_FIELD_D(EM_DBBASIC_PASSWORD,                 YA_FT_STRING,                   "Password"),
    DPI_FIELD_D(EM_DBBASIC_DBNAME,                   YA_FT_STRING,                   "DbName"),
    DPI_FIELD_D(EM_DBBASIC_DBSQL,                    YA_FT_STRING,                   "DbSql"),
    DPI_FIELD_D(EM_DBBASIC_DBIP,                     YA_FT_STRING,                   "DbIP"),
    DPI_FIELD_D(EM_DBBASIC_DBPORT,                   YA_FT_UINT16,                   "DbPort"),
    DPI_FIELD_D(EM_DBBASIC_ORACLE_HOST,              YA_FT_STRING,                   "OracleHost"),
};

struct rte_ring *tbl_ring[TBL_RING_MAX_NUM];
extern uint64_t log_total[TBL_LOG_MAX][TRAFFIC_NUM];
static dpi_file_table_type dpi_file_table[] = {
    {0x255044, "PDF"},
    {0x526563, "EML"},
    {0xD0CF11, "PPT"},
    {0x4D5AEE, "COM"},
    {0xE93B03, "COM"},
    {0x4D5A90, "dll"},
    {0x424D3E, "BMP"},
    {0x49492A, "TIF"},
    {0x384250, "PSD"},
    {0xC5D0D3, "EPS"},
    {0x0A0501, "PCS"},
    {0x89504E, "PNG"},
    {0x060500, "RAW"},
    {0x000002, "TGA"},
    {0x60EA27, "ARJ"},
    {0x526172, "RAR"},
    {0x504B03, "ZIP"},
    {0x495363, "CAB"},
    {0x1F9D8C, "Z"},
    {0x524946, "WAV"},
    {0x435753, "SWF"},
    {0x3026B2, "WMV"},
    {0x3026B2, "WMA"},
    {0x2E524D, "RM"},
    {0x00000F, "MOV"},
    {0x000077, "MOV"},
    {0x000001, "MPA"},
    {0xFFFB50, "MP3"},
    {0x234558, "m3u"},
    {0x3C2144, "HTM"},
    {0xFFFE3C, "XSL"},
    {0x3C3F78, "XML"},
    {0x3C3F78, "MSC"},
    {0x4C0000, "LNK"},
    {0x495453, "CHM"},
    {0x805343, "scm"},
    {0xD0CF11, "XLS"},
    {0x31BE00, "WRI"},
    {0x00FFFF, "MDF"},
    {0x4D4544, "MDS"},
    {0x5B436C, "CCD"},
    {0x00FFFF, "IMG"},
    {0xFFFFFF, "SUB"},
    {0x17A150, "PCB"},
    {0x2A5052, "ECO"},
    {0x526563, "PPC"},
    {0x000100, "DDB"},
    {0x42494C, "LDB"},
    {0x2A7665, "SCH"},
    {0x2A2420, "LIB"},
    {0x434841, "FNT"},
    {0x7B5C72, "RTF"},
    {0x7B5072, "GTD"},
    {0x234445, "PRG"},
    {0x000007, "PJT"},
    {0x202020, "BAS"},
    {0x000002, "TAG"},
    {0x4D5A50, "DPL"},
    {0x3F5F03, "HLP"},
    {0x3F5F03, "LHP"},
    {0xC22020, "NLS"},
    {0x5B5769, "CPX"},
    {0x4D5A16, "DRV"},
    {0x5B4144, "PBK"},
    {0x24536F, "PLL"},
    {0x4E4553, "NES"},
    {0x87F53E, "GBC"},
    {0x00FFFF, "SMD"},
    {0x584245, "XBE"},
    {0x005001, "XMV"},
    {0x000100, "TTF"},
    {0x484802, "PDG"},
    {0x000100, "TST"},
    {0x414331, "dwg"},
    {0xD0CF11, "max"},
};

#define MAX_KMP_SIZE 2000

void Nextval(char T[],int lenT,int *next)
{
  int k = -1;
  int j = 0;
  next[0] = -1;
  while (j<lenT) {
      if(k ==-1||T[j]==T[k]){
          j++;
          k++;
          if(T[j]!=T[k]){
              next[j] = k;
          }else {
              next[j] =next[k];
          }
      }else {
          k = next[k];
      }
  }
}

int KMP(char S[] ,int S_len,char T[],int lenT){
  int i = 0,j =0,lenS;
  lenS = S_len;
  int next[MAX_KMP_SIZE];

  Nextval(T,lenT,next);
  while(i<lenS&&j<lenT){
    if (j == -1||S[i]==T[j]) {
        i++;
        j++;
    }else {
        j=next[j];
    }
  }
  if(j==lenT){
    return i -j;
  }else {
   return -1;
  }
}

#define ASCII_SIZE 256
typedef struct ac_node {
  struct ac_node *children[ASCII_SIZE];
  struct ac_node *fail;
  char           *file_type;
} ac_node;
typedef struct ac_queue_node {
  ac_node           *data;
  struct ac_queue_node *next;
} ac_queue_node;

typedef struct {
  ac_queue_node *front;
  ac_queue_node *rear;
} ac_queue;

void ac_enqueue(ac_queue *q, ac_node *data) {
  ac_queue_node *new_node = (ac_queue_node *)malloc(sizeof(ac_queue_node));
  new_node->data = data;
  new_node->next = NULL;

  if (q->rear) {
   q->rear->next = new_node;
  }
  q->rear = new_node;

  if (!q->front) {
   q->front = q->rear;
  }
}

ac_node *ac_dequeue(ac_queue *q) {
  if (!q->front)
   return NULL;

  ac_queue_node *temp = q->front;
  ac_node    *data = temp->data;

  q->front = q->front->next;
  if (!q->front) {
   q->rear = NULL;
  }

  free(temp);
  return data;
}

int      ac_is_empty(ac_queue *q) { return q->front == NULL; }
ac_node *ac_create_node() {
  ac_node *node = (ac_node *)calloc(1, sizeof(ac_node));
  node->fail = NULL;
  node->file_type = NULL;
  return node;
}

void ac_insert(ac_node *root, const unsigned char *pattern, int len, const char *file_type) {
  ac_node *curr = root;
  for (int i = 0; i < len; i++) {
   int index = pattern[i];
   if (!curr->children[index]) {
        curr->children[index] = ac_create_node();
   }
   curr = curr->children[index];
  }
  curr->file_type = strdup(file_type);
}

void ac_build_failure_links(ac_node *root) {
  ac_queue q = {NULL, NULL};
  ac_enqueue(&q, root);
  root->fail = NULL;

  while (!ac_is_empty(&q)) {
   ac_node *curr = ac_dequeue(&q);

   for (int i = 0; i < ASCII_SIZE; i++) {
        if (!curr->children[i])
              continue;

        ac_node *fail = curr->fail;
        while (fail && !fail->children[i]) {
              fail = fail->fail;
        }
        curr->children[i]->fail = fail ? fail->children[i] : root;

        ac_enqueue(&q, curr->children[i]);
   }
  }
}

int ac_search(ac_node *root, const char *text, int len, char *result) {
  ac_node *curr = root;
  for (int i = 0; i < len; i++) {
   int index = (unsigned char)text[i];
   while (curr && !curr->children[index]) {
        curr = curr->fail;
   }
   curr = curr ? curr->children[index] : root;

   ac_node *tmp = curr;
   while (tmp) {
        if (tmp->file_type) {
              strncpy(result, tmp->file_type, 4);
              return 1;
        }
        tmp = tmp->fail;
   }
  }
  return 0;
}
static ac_node       *office_patterns = NULL;
static pthread_once_t office_once = PTHREAD_ONCE_INIT;

static void init_office_patterns() {
  // printf("init init_office_patterns!!!!\n");
  office_patterns = ac_create_node();
  const unsigned char doc_pattern[] = {0x57, 0x6F, 0x72, 0x64, 0x44, 0x6F, 0x63, 0x75, 0x6D, 0x65, 0x6E, 0x74};
  const unsigned char doc_wide_pattern[] = {0x57, 0x00, 0x6F, 0x00, 0x72, 0x00, 0x64, 0x00, 0x44, 0x00, 0x6F, 0x00, 0x63, 0x00,
      0x75, 0x00, 0x6D, 0x00, 0x65, 0x00, 0x6E, 0x00, 0x74};
  ac_insert(office_patterns, doc_pattern, sizeof(doc_pattern), "doc");
  ac_insert(office_patterns, doc_wide_pattern, sizeof(doc_wide_pattern), "doc");

  const unsigned char xls_pattern[] = {0x4D, 0x69, 0x63, 0x72, 0x6F, 0x73, 0x6F, 0x66, 0x74, 0x20, 0x45, 0x78, 0x63, 0x65, 0x6C};

  const unsigned char xls_wide_pattern[] = {0x4D, 0x00, 0x69, 0x00, 0x63, 0x00, 0x72, 0x00, 0x6F, 0x00, 0x73, 0x00, 0x6F, 0x00,
      0x66, 0x00, 0x74, 0x00, 0x20, 0x00, 0x45, 0x00, 0x78, 0x00, 0x63, 0x00, 0x65, 0x00, 0x6C, 0x00};
  ac_insert(office_patterns, xls_pattern, sizeof(xls_pattern), "xls");
  ac_insert(office_patterns, xls_wide_pattern, sizeof(xls_wide_pattern), "xls");

  const unsigned char ppt_pattern[] = {0x50, 0x50, 0x54};

  const unsigned char ppt_wide_pattern[] = {0x50, 0x00, 0x50, 0x00, 0x54};
  ac_insert(office_patterns, ppt_pattern, sizeof(ppt_pattern), "ppt");
  ac_insert(office_patterns, ppt_wide_pattern, sizeof(ppt_wide_pattern), "ppt");
  const unsigned char wps_pattern[] = {
      0x57, 0x00, 0x50, 0x00, 0x53, 0x00, 0x20, 0x00, 0x4F, 0x00, 0x66, 0x00, 0x66, 0x00, 0x69, 0x00, 0x63, 0x00, 0x65};
  ac_insert(office_patterns, wps_pattern, sizeof(wps_pattern), "wps");
  // Office 2007+ģʽ
  ac_insert(office_patterns, (const unsigned char *)"xl/", 3, "xlsx");
  ac_insert(office_patterns, (const unsigned char *)"word/", 5, "docx");
  ac_insert(office_patterns, (const unsigned char *)"ppt/", 4, "pptx");

  // WPSģʽ
  ac_insert(office_patterns, (const unsigned char *)"docProps/PK", 11, "wps");

  // XML/HTML���
  ac_insert(office_patterns, (const unsigned char *)"<?xml version=\"1.0\"", 19, "xml");
  ac_insert(office_patterns, (const unsigned char *)"<!DOCTYPE html>", 15, "html");
  ac_insert(office_patterns, (const unsigned char *)"JavaScript", 10, "js");

  ac_build_failure_links(office_patterns);
}
int dpi_detect_office_type(char *payload, uint16_t payload_len, char *buff,size_t buff_size) {
  pthread_once(&office_once, init_office_patterns);
  if (ac_search(office_patterns, payload, payload_len, buff)) {
   // printf("ac_search find !!! [%s]\n",buff);
   return 1;
  }

  return 0;
}
int detect_file_type(const char *payload, uint16_t payload_len, char *buff,size_t buff_size) {
  int ret = 0;
  if (buff == NULL || payload == NULL || payload_len < 5) {
   return 0;
  }

  if (payload[0] == '7' && payload[1] == 'z') {
   strncpy(buff, "7z", 2);
   buff[2] = 0;
   return 1;
  }
  if (*payload == '{' && *(payload + payload_len - 1) == '}') {
   strncpy(buff, "json", 4);
   buff[4] = 0;
   return 1;
  }
  int check_len = 56;
  if (check_len > (int)payload_len) {
   check_len = payload_len;
  }

  uint32_t header = 0;    // ǰ4�ֽ�
  uint32_t header_4 = 0;  // 4-8�ֽ�
  header = get_uint32_ntohl(payload, 0);
  uint32_t header_3bytes = (header & 0xffffff00) >> 8;
  uint32_t header_2bytes = (header & 0xffff0000) >> 16;
  header_4 = get_uint32_ntohl(payload, 4);

  if (header_2bytes == 0xffd8) {
   strncpy(buff, "JPG", 3);
  buff[3] = 0;

   return 1;
  }
  // ʹ��AC�Զ���ͳһ�������ģʽ
  if (dpi_detect_office_type((char*)payload, payload_len, buff,buff_size)) {
   return 1;
  }

  for (unsigned i = 0; i < sizeof(dpi_file_table) / sizeof(dpi_file_table[0]); i++) {
   if (header_3bytes == dpi_file_table[i].code) {
        if (dpi_file_table[i].file_type[0] == 'z' && dpi_file_table[i].file_type[1] == 'i' &&
            dpi_file_table[i].file_type[2] == 'p') {
              if (KMP((char*)payload, payload_len > 512 ? 512 : payload_len, "Android", 2) != -1) {
                strncpy(buff, "apk", 3);
                return 1;
              }
        }

        strncpy(buff, dpi_file_table[i].file_type, strlen(dpi_file_table[i].file_type));
        return 1;
   }
  }
  return 0;
}

static char hex2char[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
int write_hex_to_string(char *buffer, int buff_len, const uint8_t *data,uint32_t data_len)
{
    if(data == NULL || data_len == 0 || ((int)data_len * 2 > buff_len))
        return 0;

    uint32_t i;
    int index=0;
    for(i=0; i<data_len; i++){
        buffer[index]     = hex2char[data[i] >> 4];
        buffer[index + 1] = hex2char[data[i] & 0x0f];
        index += 2;
    }

    return index;
}

/************** write_xxxx_reconds TOP *********************/
int write_record_string_length(precord_t *record, int *idx, int max_len, const char *string, unsigned int length)
{
    pschema_t *schema = precord_get_schema(record);
    pfield_desc_t *desc = pschema_fdesc_get_by_index(schema, *idx);
    ya_ftenum_t ftenum =  pfdesc_get_type(desc);
    const char *proto = pschema_get_proto_name(schema);
    const char *field = pfdesc_get_name(desc);
    const char *typename = pfdesc_get_type_name(desc);

    if(length <= 0)
    {
        goto EXIT;
    }

    //本业务只受理 STRING 类型
    if(IS_YA_FT_STRING(ftenum))
    {
        ya_fvalue_t *value=ya_fvalue_fast_new_stringn(precord_get_fvalue_allocator(record), YA_FT_STRING, string, length);
        precord_fvalue_put_by_index(record, *idx, value);
    }
    else //如果字段定义类型为 [数值整型] 将 [STRING] 转换
    if(IS_YA_FT_INT(ftenum) || IS_YA_FT_UINT32(ftenum) || IS_YA_FT_UINT(ftenum))
    {
        const char *p = string;
        while(*p)
        {
            if(isdigit(*p))
            {
                p++;
            }
            else
            {
                //printf("WARN: [%s][%s]注册类型[%s] 赋值动作[STRING]内容[%s] 无法转换\n", proto, field, typename, string);
                goto EXIT;
            }
        }
        //预防 goto 导致的栈上成员被诡异
        {
            char *end = NULL;
            size_t var = strtol(string, &end, 10);
            //printf("SUCC: [%s][%s]注册类型[%s] 赋值动作[%s] 赋值内容[%s] 转换成功[%u]!\n", proto, field, typename, "STRING", string, var);
            ya_fvalue_t *value=ya_fvalue_fast_new_sinteger64(precord_get_fvalue_allocator(record), YA_FT_INT64, var);
            precord_fvalue_put_by_index(record, *idx, value);
        }
    }
    else
    {
        //printf("WARN: [%s][%s]注册类型[%s] 赋值动作[%s]\n", proto, field, typename, "STRING");
    }
EXIT:
    (*idx)++;
    return 0;
}

int write_one_chars_reconds(precord_t *record, int *idx, int max_len, const char *data, unsigned int data_len)
{
    write_record_string_length(record, idx, max_len, data, data_len);
    return 0;
}

int write_one_str_reconds(precord_t *record, int *idx, int max_len, const char *data, unsigned int data_len)
{
    write_record_string_length(record, idx, max_len, data, data_len);
    return 0;
}

int write_one_ip_reconds(precord_t *record, int *idx, uint8_t version, const uint8_t *str)
{
    char _str[64] = {0};
    if (version == 4)
        get_iparray_to_string(_str, sizeof(_str), str);
    else if (version == 6)
        get_ip6string(_str, sizeof(_str), str);
    else
        strncpy(_str, "0.0.0.0", sizeof("0.0.0.0"));
    write_one_str_reconds(record, idx, TBL_LOG_MAX_LEN, _str, strlen(_str));
    return 0;
}

int write_string_reconds(precord_t *record, int *idx, int max_len, const char *String)
{
    if(String)
        return write_one_str_reconds(record, idx, max_len, String, strlen(String));
    else
        return write_n_empty_reconds(record, idx, max_len, 1);
}

int write_fstring_reconds(precord_t *record, int *idx, int max_len, const char *fmt, ...)
{
    char buff[256];

    va_list ap;
    va_start(ap, fmt);
    vsnprintf(buff, sizeof(buff), fmt, ap);
    va_end(ap);

    return write_record_string_length(record, idx, max_len, buff, strlen(buff));
}


int write_one_snum_reconds(precord_t *record, int *idx, int max_len, long data)
{
    pschema_t *schema = precord_get_schema(record);
    pfield_desc_t *desc = pschema_fdesc_get_by_index(schema, *idx);
    ya_ftenum_t ftenum =  pfdesc_get_type(desc);
    //本业务只受理 NUMBER 类型
    if(IS_YA_FT_INT(ftenum) || IS_YA_FT_UINT32(ftenum) || IS_YA_FT_UINT(ftenum))
    {
        ya_fvalue_t *value=ya_fvalue_fast_new_sinteger64(precord_get_fvalue_allocator(record), YA_FT_INT64, data);
        precord_fvalue_put_by_index(record, *idx, value);
    }
    else
    {
        const char *proto = pschema_get_proto_name(schema);
        const char *field = pfdesc_get_name(desc);
        const char *typename = pfdesc_get_type_name(desc);
        //printf("WARN: [%s][%s]注册类型[%s] 赋值动作[%s]\n", proto, field, typename, "SIGNED_INT");
    }
    (*idx)++;
    return 0;
}

int write_uint64_reconds(precord_t *record, int *idx, int max_len, uint64_t data)
{
    pschema_t *schema = precord_get_schema(record);
    pfield_desc_t *desc = pschema_fdesc_get_by_index(schema, *idx);
    ya_ftenum_t ftenum =  pfdesc_get_type(desc);
    //本业务只受理 NUMBER 类型
    if(IS_YA_FT_INT(ftenum) || IS_YA_FT_UINT32(ftenum) || IS_YA_FT_UINT(ftenum))
    {
        ya_fvalue_t *value=ya_fvalue_fast_new_uinteger64(precord_get_fvalue_allocator(record), YA_FT_UINT64, data);
        precord_fvalue_put_by_index(record, *idx, value);
    }
    else
    {
        const char *proto = pschema_get_proto_name(schema);
        const char *field = pfdesc_get_name(desc);
        const char *typename = pfdesc_get_type_name(desc);
        //printf("WARN: [%s][%s]注册类型[%s] 赋值动作[%s]\n", proto, field, typename, "UNSIGNED_INT64");
    }
    (*idx)++;
    return 0;
}

// 对于长度类数字，为0无意义
int write_one_no_zero_num_reconds(precord_t *record, int *idx, int max_len, uint64_t data)
{
  if(data!= 0){
    write_uint64_reconds(record, idx, max_len, data);
  }else {
    write_n_empty_reconds(record, idx, max_len, 1);
  }
    return 0;
}
int write_one_num_reconds(precord_t *record, int *idx, int max_len, uint32_t data)
{
    pschema_t *schema = precord_get_schema(record);
    pfield_desc_t *desc = pschema_fdesc_get_by_index(schema, *idx);
    ya_ftenum_t ftenum =  pfdesc_get_type(desc);
    const char *proto = pschema_get_proto_name(schema);
    const char *field = pfdesc_get_name(desc);
    const char *typename = pfdesc_get_type_name(desc);

    //本业务只受理 NUMBER 类型
    if(IS_YA_FT_INT(ftenum) || IS_YA_FT_UINT32(ftenum) || IS_YA_FT_UINT(ftenum))
    {
        ya_fvalue_t *value=ya_fvalue_fast_new_uinteger(precord_get_fvalue_allocator(record), YA_FT_UINT32, data);
        precord_fvalue_put_by_index(record, *idx, value);
    }
    else
    if(IS_YA_FT_STRING(ftenum))
    {
        char buff[64];
        snprintf(buff, sizeof(buff), "%u", data);
        ya_fvalue_t *value=ya_fvalue_fast_new_stringn(precord_get_fvalue_allocator(record), YA_FT_STRING, buff, strlen(buff));
        precord_fvalue_put_by_index(record, *idx, value);
    }
    else
    {
        //printf("WARN: [%s][%s]注册类型[%s] 赋值动作[NUMBER]\n", proto, field, typename);
    }
    (*idx)++;
    return 0;
}

void endian_reverse(const unsigned char *i, int l, unsigned char *o, int s)
{
    int index = 0;
    for(index = 0; index < l; index++)
    {
        o[s-1-index] = i[index];
    }
}

//将数值型变量 以 十六进制型文本展示(区别于)
int write_one_number_to_hexnum_reconds(precord_t *record, int *idx, int max_len, const unsigned char *p, int size)
{
    if(size <= 0)
    {
        pschema_t *schema = precord_get_schema(record);
        pfield_desc_t *desc = pschema_fdesc_get_by_index(schema, *idx);
        ya_ftenum_t ftenum =  pfdesc_get_type(desc);
        const char *proto = pschema_get_proto_name(schema);
        const char *field = pfdesc_get_name(desc);
        const char *typename = pfdesc_get_type_name(desc);
        //printf("WARN: [%s][%s]注册类型[%s] write_one_number_to_hexnum_reconds 赋值错误 赋值[NULL]\n", proto, field, typename);
        (*idx)++;
        return 0;
    }

    // 将数字 输出为 十六进制 以文本显示, 需要转换端序, 否则 Intel 会与你作对.
    // 比如 Intel CPU 中程序的 字有一个为 :305419896
    // 你想 输出应该为 0x12345678 的字符串
    // 而实际上 内存里面是 0x78563412 (内存地址: 小 --> 大)
    ya_fvalue_t *value=NULL;
    uint64_t var64 = 0;
    uint32_t var32 = 0;
    uint16_t var16 = 0;
    switch(size)
    {
        case sizeof(uint64_t):
            endian_reverse((unsigned char *)p, size, (unsigned char *)&var64, 8);
            value=ya_fvalue_fast_new_bytes(precord_get_fvalue_allocator(record), YA_FT_BYTES, (const uint8_t *)&var64, 8);
            break;
        case sizeof(uint32_t):
            endian_reverse((unsigned char *)p, size, (unsigned char *)&var32, 4);
            value=ya_fvalue_fast_new_bytes(precord_get_fvalue_allocator(record), YA_FT_BYTES, (const uint8_t *)&var32, 4);
            break;
        case sizeof(uint16_t):
            endian_reverse((unsigned char *)p, size, (unsigned char *)&var16, 2);
            value=ya_fvalue_fast_new_bytes(precord_get_fvalue_allocator(record), YA_FT_BYTES, (const uint8_t *)&var16, 2);
            break;
        default:
            value=ya_fvalue_fast_new_bytes(precord_get_fvalue_allocator(record), YA_FT_BYTES, (const uint8_t *)p, size);
            break;
    }
    pschema_t *schema = precord_get_schema(record);
    pfield_desc_t *desc = pschema_fdesc_get_by_index(schema, *idx);
    ya_ftenum_t ftenum =  pfdesc_get_type(desc);
    //本业务只受理 BYTES 类型
    if(IS_YA_FT_BYTES(ftenum))
    {
        precord_fvalue_put_by_index(record, *idx, value);
    }
    else
    {
        const char *proto = pschema_get_proto_name(schema);
        const char *field = pfdesc_get_name(desc);
        const char *typename = pfdesc_get_type_name(desc);
        //printf("WARN: [%s][%s]注册类型[%s] 赋值动作[%s]\n", proto, field, typename, "BYTES");
    }
    (*idx)++;
    return 0;
}

int write_one_u64_hexnum_reconds(precord_t *record, int *idx, int max_len, uint64_t data)
{
    write_one_number_to_hexnum_reconds(record, idx, max_len, (unsigned char*)&data, sizeof(data));
    return 0;
}

int write_one_hexnum_reconds(precord_t *record, int *idx, int max_len, uint32_t data)
{
    write_one_number_to_hexnum_reconds(record,  idx, max_len, (unsigned char*)&data, sizeof(data));
    return 0;
}

int write_one_hex_reconds(precord_t *record, int *idx, int max_len, long long unsigned data)
{
    write_one_number_to_hexnum_reconds(record, idx, max_len, (unsigned char*)&data, sizeof(data));
    return 0;
}

int write_multi_num_reconds(precord_t *record, int *idx, int max_len, const uint8_t *data, uint32_t data_len)
{
    //乱七八糟, 什么样的数据都有!
    if(NULL == data || 0 == data_len)
    {
        (*idx)++;
        return 0;
    }
    write_one_number_to_hexnum_reconds(record, idx, max_len, data, data_len);
    return 0;
}

// bsdnum 这是什么东西?
int write_multi_bsdnum_reconds(precord_t *record, int *idx, int max_len, const uint8_t *data, uint32_t data_len)
{
    if(data_len <= 0)
    {
        pschema_t *schema = precord_get_schema(record);
        pfield_desc_t *desc = pschema_fdesc_get_by_index(schema, *idx);
        ya_ftenum_t ftenum =  pfdesc_get_type(desc);
        const char *proto = pschema_get_proto_name(schema);
        const char *field = pfdesc_get_name(desc);
        const char *typename = pfdesc_get_type_name(desc);
        //printf("WARN: [%s][%s]注册类型[%s] write_multi_bsdnum_reconds 赋值错误 赋值[NULL]\n", proto, field, typename);
        (*idx)++;
        return 0;
    }
    unsigned char *p = malloc(data_len);
    for(int i = 0; i < (int)data_len; i++)
    {
        unsigned char H = data[0] >> 4;
        unsigned char L = data[0] & 15;
        p[0] = (L << 4 | H);
    }
    pschema_t *schema = precord_get_schema(record);
    pfield_desc_t *desc = pschema_fdesc_get_by_index(schema, *idx);
    ya_ftenum_t ftenum =  pfdesc_get_type(desc);
    const char *proto = pschema_get_proto_name(schema);
    const char *field = pfdesc_get_name(desc);
    const char *typename = pfdesc_get_type_name(desc);
    //本业务只受理 STRING 类型
    if(IS_YA_FT_STRING(ftenum))
    {
        ya_fvalue_t *value=ya_fvalue_fast_new_bytes(precord_get_fvalue_allocator(record), YA_FT_BYTES, p, data_len);
        precord_fvalue_put_by_index(record, *idx, value);
    }
    else
    {
        //printf("WARN: [%s][%s]注册类型[%s] 赋值动作[%s]\n", proto, field, typename, "STRING");
    }
    (*idx)++;
    free(p);
    return 0;
}

int write_bytes_reconds(precord_t *record, int *idx, int max_len, const uint8_t *data,uint32_t data_len)
{
    pschema_t *schema = precord_get_schema(record);
    pfield_desc_t *desc = pschema_fdesc_get_by_index(schema, *idx);
    ya_ftenum_t ftenum =  pfdesc_get_type(desc);

    if(data_len <= 0)
    {
        goto EXIT;
    }
    //本业务只受理 BYTES 类型
    if(IS_YA_FT_BYTES(ftenum))
    {
        ya_fvalue_t *value=ya_fvalue_fast_new_bytes(precord_get_fvalue_allocator(record), YA_FT_BYTES, (const uint8_t *)data, data_len);
        precord_fvalue_put_by_index(record, *idx, value);
    }
    else
    {
        const char *proto = pschema_get_proto_name(schema);
        const char *field = pfdesc_get_name(desc);
        const char *typename = pfdesc_get_type_name(desc);
        //printf("WARN: [%s][%s]注册类型[%s] 赋值动作[%s]\n", proto, field, typename, "BYTES");
    }
EXIT:
    (*idx)++;
    return 0;
}

int write_multi_digit_reconds(precord_t *record, int *idx, int max_len, const uint8_t *data,uint32_t data_len)
{
    write_one_number_to_hexnum_reconds(record, idx, max_len, data, data_len);
    return 0;
}

int write_n_empty_reconds(precord_t *record, int *idx, int max_len, int n)
{
    ((void)record);
    ((void)idx);
    ((void)max_len);
    for(int i = 0; i < n; i++)
    {
        (*idx)++;
    }
    return 0;
}

int write_coupler_log(precord_t *record, int *idx, int max_len, uint8_t data_type,const uint8_t *data,uint64_t data_len)
{
    switch(data_type){
        case EM_F_TYPE_EMPTY:
            write_n_empty_reconds(record, idx, max_len, (int)data_len);
            break;
        case EM_F_TYPE_UINT8:
            write_one_num_reconds(record, idx, max_len, (uint8_t)data_len);
            break;
        case EM_F_TYPE_UINT16:
            write_one_num_reconds(record, idx, max_len, (uint16_t)data_len);
            break;
        case EM_F_TYPE_UINT32:
            write_one_num_reconds(record, idx, max_len, (uint32_t)data_len);
            break;
        case EM_F_TYPE_UINT64:
            write_uint64_reconds(record, idx, max_len, (uint64_t)data_len);
            break;
        case EM_F_TYPE_STRING:
            write_one_str_reconds(record, idx, max_len, (const char *)data, (uint32_t)data_len);
            break;
        case YA_FT_BYTES:
        case YA_FT_BYTES_MAYBE:
            write_bytes_reconds(record, idx, max_len, (const uint8_t *)data, (uint32_t)data_len);
            break;
        case EM_F_TYPE_HEX:
            write_multi_num_reconds(record, idx, max_len, data,( uint32_t)data_len);
            break;
        case EM_F_TYPE_NULL:
            write_n_empty_reconds(record, idx, max_len, 1);
            break;
        default:
            write_n_empty_reconds(record, idx, max_len, 1);
            break;
    }

    return 0;
}

int
write_fields_data_array(FieldValue_t *field_array, FieldType_t field_type, const uint8_t *field_ptr,uint64_t data_len)
{
    field_array->fType     = field_type;
    switch (field_array->fType)
    {
    case YA_FT_UINT8:
        field_array->fLen      = sizeof(u8);
        field_array->u.v_u8   = (u8)data_len;
        break;
    case YA_FT_BOOLEAN:
    case YA_FT_CHAR:
    case YA_FT_INT8:
        field_array->fLen      = sizeof(i8);
        field_array->u.v_i8   = (i8)data_len;
        break;

    case YA_FT_UINT16:
        field_array->fLen      = sizeof(u16);
        field_array->u.v_u16   = (u16)data_len;
        break;
    case YA_FT_INT16:
        field_array->fLen      = sizeof(i16);
        field_array->u.v_u16   = (i16)data_len;
        break;

    case YA_FT_UINT32:
        field_array->fLen      = sizeof(u32);
        field_array->u.v_u32   = (u32)data_len;
        break;
    case YA_FT_INT32:
        field_array->fLen      = sizeof(i32);
        field_array->u.v_u32   = (i32)data_len;
        break;

    case YA_FT_UINT56:
    case YA_FT_UINT64:
        field_array->fLen      = sizeof(u64);
        field_array->u.v_u64   = (u64)data_len;
        break;
    case YA_FT_INT56:
    case YA_FT_INT64:
        field_array->fLen      = sizeof(i64);
        field_array->u.v_u64   = (i64)data_len;
        break;

    case YA_FT_BYTES:
        field_array->fLen      = data_len;
        field_array->u.v_pBytes= (byte *)field_ptr;
        break;

    default:
        printf("%s 暂不支持的类型, FieldValue_t type:%d\n",__func__,field_type);
        field_array->fType=YA_FT_NONE;
        break;
    }

    return 1;
}

int std_write_ip_array(FieldValue_t *fp, FieldType_t type, uint8_t version, const uint8_t * str) {
    char _str[64] = {0};
    if (version == 4) {
        get_iparray_to_string(_str, sizeof(_str), str);
    } else if (version == 6) {
        get_ip6string(_str, sizeof(_str), str);
    } else {
        strncpy(_str, "0.0.0.0", sizeof("0.0.0.0"));
    }
    write_fields_data_array(fp, type, (const uint8_t *)_str, (uint64_t)strlen(_str));
    return 0;
}

precord_t*
write_sdt_syslog(SdtMatchResult *match_result)
{
    precord_t *record = NULL;
    dpi_precord_new_record(record, "dpi_sdt_rule", dpi_sdt_rule_field);
    player_t *layer = precord_layer_put_new_layer(record, "dpi_sdt_rule");

    int max_len = EM_SDT_RULE_MAX;
    int idx = 0;
    for(int i = 0; i < EM_SDT_RULE_MAX; i++)
    {
        switch (i){
        case EM_SDT_RULE_ID:
            write_one_num_reconds(record, &idx, max_len, match_result->ruleID);
            break;
        case EM_SDT_RULE_GID:
            if(*match_result->groupID){
                write_one_str_reconds(record, &idx, max_len, match_result->groupID, strlen(match_result->groupID));
            }else{
                write_n_empty_reconds(record, &idx, max_len, 1);
            }
            break;
        case EM_SDT_RULE_UUID:
            if(match_result->uuid>0){
                write_one_num_reconds(record, &idx, max_len, match_result->uuid);
            }else{
                write_n_empty_reconds(record, &idx, max_len, 1);
            }
            break;
        case EM_SDT_RULE_SCORE:
            if(match_result->score>0){
                write_one_num_reconds(record, &idx, max_len, match_result->score);
            }else{
                write_n_empty_reconds(record, &idx, max_len, 1);
            }
            break;
        case EM_SDT_RULE_MSG:
            if(match_result->msg){
                write_one_str_reconds(record, &idx, max_len, match_result->msg, strlen(match_result->msg));
            }else{
                write_n_empty_reconds(record, &idx, max_len, 1);
            }
            break;
        case EM_SDT_RULE_CLASSTYPE:
            if(match_result->classtype){
                write_one_str_reconds(record, &idx, max_len, match_result->classtype, strlen(match_result->classtype));
            }else{
                write_n_empty_reconds(record, &idx, max_len, 1);
            }
            break;
        case EM_SDT_RULE_REFERENCE:
            if(match_result->reference){
                write_one_str_reconds(record, &idx, max_len, match_result->reference, strlen(match_result->reference));
            }else{
                write_n_empty_reconds(record, &idx, max_len, 1);
            }
            break;

        default:
            write_n_empty_reconds(record, &idx, max_len, 1);
            break;
        }
    }
    return record;
}

/*
    通联信息：
    ips.path                protInfo            string        数据流完整协议路径 512字节
    ips.stime               sTime               date time     会话开始时间
    ips.etime               eTime               date time     会话结束时间

    mac.src                 srcMac              string        源MAC 6字节
    mac.dst                 dstMac              string        目的MAC 6字节

    ips.proto               protNum             string        IP头部协议字段 1字节
    ips.src                 srcAddr             ip            发送端IP 16字节
    ips.dst                 dstAddr             ip            接收端IP 16字节
    ips.inner_src           innSrcAddr          ip            隧道最内层发送端IP 16字节
    ips.inner_dst           innDstAddr          ip            隧道最内层接收端IP 16字节
    ips.class               addrType            bool          true:ipv4 false:ipv6
    ips.inner_class         innAddrType         bool          true:ipv4 false:ipv6
    ips.ttl.c               firTtlByCli         int           上行首包TTL 1字节
    ips.ttl.s               firTtlBySrc         int           下行首包TTL 1字节
    ips.port.c              srcPort             int           发送端口号
    ips.port.s              dstPort             int           接收端口号
    ips.app.protos          appProt             string        高层应用名称，未知unknown，16字节
    ips.direction           strDirec            int           0:未知方向，1：src为服务端，2：src为客户端
    ips.app.direction       appDirec            int           同上
    ips.tcp.flag.src        tcpFlagsByCli       string        记录上行SYN,RST,FIN置位包中标志位字节HEX序列 12字节
    ips.tcp.flag.dst        tcpFlagsBySrv       string        下行
    ips.datebytes           sesPayLen           int           双向会话负载长度
    ips.packets.c           upLinkPktNum        int           上行链接包数量
    ips.bytes.c             upLinkBytes         int           上行链接包总长度
    ips.databytes.c         upLinkDataBytes     int           上行数据中，最内层传输层之上的数据长度
    ips.desiredbytes.c      upLinkDesBytes      int           上行数据中，根据TCP序列号推算出的最内层传输层之上的数据长度
    ips.packets.s           downLinkPktNum      int           下行链接包数量
    ips.bytes.s             downLinkBytes       int           下行链接包总长度
    ips.databytes.s         downLinkDataBytes   int           下行数据中，最内层传输层之上的数据长度
    ips.desiredbytes.s      downLinkDesBytes    int           下行数据中，根据TCP序列号推算出的最内层传输层之上的数据长度

    tcp.seq.c               upLinkSynSeqNum     int           上行SYN包的SEQ号 4字节
    tcp.seq.s               downLinkSynSeqNum   int           下行SYN包的SEQ号 4字节
    tcp.winsize.c           upLinkSynTcpWins    int           上行SYN包TCP窗口 2字节
    tcp.winsize.s           downLinkSynTcpWins  int           下行SYN包TCP窗口 2字节
    tcp.options.c           upLinkTcpOpts       string        上行TCP SYN置位包选项字段 40字节
    tcp.options.s           downLinkTcpOpts     string        下行TCP SYN置位包选项字段 40字节

    trans.payload8.c.hex    upLinkTransPayHex   string        上行传输层负载HEX
    trans.payload8.s.hex    downLinkTransPayHex string        下行传输层负载HEX
    trans.paylen.set.c      upLinkPayLenSet     string        上行有负载前5包传输层负载序列
    trans.paylen.set.s      downLinkPayLenSet   string        下行有负载前5包传输层负载序列
    trans.packetlen.c.max   upLinkBigPktLen     int           上行链接最大包长
    trans.packetlen.c.min   upLinkSmaPktLen     int           上行链接最小包长
    trans.packinter.c.max   upLinkBigPktInt     int           上行链接最小包间隔
    trans.packinter.c.min   upLinkSmaPktInt     int           上行链接最小包间隔
    trans.packetlen.s.max   downLinkBigPktLen   int           下行链接最大包长
    trans.packetlen.s.min   downLinkSmaPktLen   int           下行链接最小包长
    trans.packinter.s.max   downLinkBigPktInt   int           下行链接最大包间隔
    trans.packinter.s.min   downLinkSmaPktInt   int           下行链接最小包间隔

    tcp.flags.syn.cnt       tcpFlagsSynCnt      int           syn计数
    tcp.flags.syn_ack.cnt   tcpFlagsSynActCnt   int           syn-ack计数
    tcp.flags.ack.cnt       tcpFlagsAckCnt      int           ack计数
    tcp.flags.psh.cnt       tcpFlagsPshCnt      int           psh计数
    tcp.flags.rst.cnt       tcpFlagsRstCnt      int           rst计数
    tcp.flags.urg.cnt       tcpFlagsUrgCnt      int           urg计数
    ct                      ct                  date time     日志生成时间

    ips.head_cv             ipHeadCV            int           ip头校验和
    ips.etags               etags               string        ip连接异常标签
    ips.ttags               ttags               string        ip连接威胁标签

*/

int write_sdt_rule_info(SdtMatchResult *match_result, precord_t *record)
{
    precord_layer_put_new_layer(record, "dpi_sdt_rule");

    int idx = 0;
    int i;
    for( i = 0; i < EM_SDT_RULE_MAX; i++){
        switch (i){
        case EM_SDT_RULE_ID:
            write_one_num_reconds(record, &idx, TBL_LOG_RULE_INFO, match_result->ruleID);
            break;
        case EM_SDT_RULE_GID:
            if(*match_result->groupID){
                write_one_str_reconds(record, &idx, TBL_LOG_RULE_INFO, match_result->groupID, strlen(match_result->groupID));
            }else{
                write_n_empty_reconds(record, &idx, TBL_LOG_RULE_INFO, 1);
            }
            break;
        case EM_SDT_RULE_UUID:
            if(match_result->uuid>0){
                write_one_num_reconds(record, &idx, TBL_LOG_RULE_INFO, match_result->uuid);
            }else{
                write_n_empty_reconds(record, &idx, TBL_LOG_RULE_INFO, 1);
            }
            break;
        case EM_SDT_RULE_SCORE:
            if(match_result->score>0){
                write_one_num_reconds(record, &idx, TBL_LOG_RULE_INFO, match_result->score);
            }else{
                write_n_empty_reconds(record, &idx, TBL_LOG_RULE_INFO, 1);
            }
            break;
        case EM_SDT_RULE_MSG:
            if(match_result->msg){
                write_one_str_reconds(record, &idx, TBL_LOG_RULE_INFO, match_result->msg, strlen(match_result->msg));
            }else{
                write_n_empty_reconds(record, &idx, TBL_LOG_RULE_INFO, 1);
            }
            break;
        case EM_SDT_RULE_CLASSTYPE:
            if(match_result->classtype){
                write_one_str_reconds(record, &idx, TBL_LOG_RULE_INFO, match_result->classtype, strlen(match_result->classtype));
            }else{
                write_n_empty_reconds(record, &idx, TBL_LOG_RULE_INFO, 1);
            }
            break;
        case EM_SDT_RULE_REFERENCE:
            if(match_result->reference){
                write_one_str_reconds(record, &idx, TBL_LOG_RULE_INFO, match_result->reference, strlen(match_result->reference));
            }else{
                write_n_empty_reconds(record, &idx, TBL_LOG_RULE_INFO, 1);
            }
            break;

        default:
            write_n_empty_reconds(record, &idx, TBL_LOG_RULE_INFO, 1);
            break;
        }
    }
    return 0;
}

int write_327ZDY_task_info(SdtMatchResult *match_result, precord_t *record, const char* pcap_file_path)
{
    char __str[256] = {0};

    precord_layer_move_cursor(record, "common");
    if(pcap_file_path == NULL){
        snprintf(__str, sizeof(__str),
                "{\"pcapName\":\"\",\"taskInfo\":{\"ruleId\":\"%u\",\"taskId\":%s}}",
                match_result->ruleID,
                match_result->taskID);
    }else{
        const char *filename = NULL;
        filename = strrchr(pcap_file_path, '/');
        if(!filename)
            filename = pcap_file_path;
        else
            filename++;
        snprintf(__str, sizeof(__str),
                "{\"pcapName\":\"%s\",\"taskInfo\":{\"ruleId\":\"%u\",\"taskId\":%s}}",
                filename,
                match_result->ruleID,
                match_result->taskID);
    }

    precord_put(record, "task_info",        string,     __str);

    return 0;
}


static int write_sdt_rule_common_fields(FILE *fd_fp)
{
    if(!fd_fp){
        return -1;
    }
    int i;
    /* sdt rule common field */
    for(i=0;i<(int)(sizeof(dpi_sdt_rule_field)/sizeof(dpi_sdt_rule_field[0]));i++){
        fwrite(dpi_sdt_rule_field[i].field_name,strlen(dpi_sdt_rule_field[i].field_name),1,fd_fp);
        fwrite("\n",1,1,fd_fp);
    }
    return 0;
}

void write_common(precord_t *record, int log_len_max, struct flow_info *flow, int direction)
{
    int      ret       = 0;
    char     __str[256] = {0};
    uint16_t src_port  = 0;
    uint16_t dst_port  = 0;
    int      idx  = 0;

    int tuple_direction = 0;
    ya_fvalue_t *value = NULL;
    player_t *layer = precord_layer_put_new_layer(record, "common");

    if (flow->drt_port_src[direction] > 0) {
        if (flow->drt_port_src[direction] > flow->drt_port_dst[direction]) {
            tuple_direction = FLOW_DIR_SRC2DST;  // c2s
        } else {
            tuple_direction = FLOW_DIR_DST2SRC;  // s2c
        }
        if (tuple_direction != direction) {
            tuple_direction = 1 - direction;
        }
    } else {
        tuple_direction = direction;
    }

    IPINFO srcIp, dstIp;
    if(g_config.ip_position_switch)    //打开geoip模块
        dissect_ip_position(flow, tuple_direction, &srcIp, &dstIp);

    // trailer 暂时先留空
    write_n_empty_reconds(record, &idx, log_len_max, EM_COMMON_RTL_BS + 1);
    // write_trailer_reconds(log_content, &idx, log_len_max, flow, g_config.trailer_type, g_config.net_type);

    precord_put(record, "DevNo",           string,     g_config.devno);
    precord_put(record, "LinkLayerType",   string,     g_config.devname);
    value =ya_fvalue_fast_new_uinteger(precord_get_fvalue_allocator(record), YA_FT_UINT16, flow->ip_version == 4 ? 0 : 1);
    precord_fvalue_put(record, "isIPv6", value);
    value =ya_fvalue_fast_new_uinteger(precord_get_fvalue_allocator(record), YA_FT_UINT16, flow->is_mpls ? 0 : 1);
    precord_fvalue_put(record, "isMPLS", value);

    if (flow->real_protocol_id == PROTOCOL_HTTP) {
        ret = snprintf(__str, 64, "%u%u%lu%u_%d", g_config.mac, flow->thread_id, flow->flow_id, flow->flow_cycle,
                       flow->sub_flow_id);
    } else {
        ret = snprintf(__str, 64, "%u%u%lu%u", g_config.mac, flow->thread_id, flow->flow_id,
                       flow->flow_cycle);  // EM_COMMON_RESV1:flow_id
    }
    // precord_put(record, "resv1", string, flow->is_mpls ? "1" : "0");

    // if (flow->tuple.outer.ip_version) {  // EM_COMMON_RESV2:tunnel type
    //     if (flow->tuple.outer.port_src)
    //     write_one_str_reconds(log_content, &idx, log_len_max, "l2tp", 4);
    //     else if (flow->tuple.outer.port_src == 0)
    //     write_one_str_reconds(log_content, &idx, log_len_max, "gre", 3);
    //     else
    //     write_n_empty_reconds(log_content, &idx, log_len_max, 1);
    // } else {
    //     write_n_empty_reconds(log_content, &idx, log_len_max, 1);
    // }
    // precord_put(log_content, "resv2", uinteger, flow->is_mpls ? 1 : 0);

    if (g_config.data_source == 3) {
        precord_put(record, "resv3", string, g_config.task_name);
        precord_put(record, "resv8", string, flow->path);
        timet_to_datetime_local(flow->timestamp / 1e6, __str, 64);
        precord_put(record, "CapDate", string, __str);
    } else {
        precord_put(record, "CapDate", string, g_config.time_str);
    }

    if (flow->timeout_flag) {
        precord_put(record, "resv4", uinteger, flow->src2dst_packets + flow->dst2src_packets);
        precord_put(record, "resv5", uinteger64, flow->src2dst_bytes + flow->dst2src_bytes);
    }
    precord_put(record, "resv6", string, g_config.task_id);
    // precord_put(record, "resv7", uinteger, flow->vlan_flag ? 1 : 0);

    if (tuple_direction == FLOW_DIR_SRC2DST){// c2s
        if (flow->ip_version == 4)
            get_iparray_to_string(__str, sizeof(__str), flow->tuple.inner.ip_src);
        else
            get_ip6string(__str, sizeof(__str), flow->tuple.inner.ip_src);
    }else{
        if (flow->ip_version == 4)
            get_iparray_to_string(__str, sizeof(__str), flow->tuple_reverse.inner.ip_src);
        else
            get_ip6string(__str, sizeof(__str), flow->tuple_reverse.inner.ip_src);
    }
    precord_put(record, "SrcIp", string, __str);

    if (g_config.ip_position_switch) {
        precord_put(record, "SrcCountry",  string, srcIp.country);
        precord_put(record, "SrcArea",     string, srcIp.area);
        precord_put(record, "SrcCity",     string, srcIp.city);
        precord_put(record, "DstCountry",  string, dstIp.country);
        precord_put(record, "DstArea",     string, dstIp.area);
        precord_put(record, "DstCity",     string, dstIp.city);
    }

    if (tuple_direction == FLOW_DIR_SRC2DST){ // c2s
        if (flow->ip_version == 4)
            get_iparray_to_string(__str, sizeof(__str), flow->tuple.inner.ip_dst);
        else
            get_ip6string(__str, sizeof(__str), flow->tuple.inner.ip_dst);
    }else{
        if (flow->ip_version == 4)
            get_iparray_to_string(__str, sizeof(__str), flow->tuple_reverse.inner.ip_dst);
        else
            get_ip6string(__str, sizeof(__str), flow->tuple_reverse.inner.ip_dst);
    }
    precord_put(record, "DstIp", string, __str);

    if (tuple_direction == FLOW_DIR_SRC2DST) {
        src_port = ntohs(flow->tuple.inner.port_src);
        dst_port = ntohs(flow->tuple.inner.port_dst);
    } else {
        src_port = ntohs(flow->tuple_reverse.inner.port_src);
        dst_port = ntohs(flow->tuple_reverse.inner.port_dst);
    }
    value =ya_fvalue_fast_new_uinteger(precord_get_fvalue_allocator(record), YA_FT_UINT16, src_port);
    precord_fvalue_put(record, "SrcPort", value);
    value =ya_fvalue_fast_new_uinteger(precord_get_fvalue_allocator(record), YA_FT_UINT16, dst_port);
    precord_fvalue_put(record, "DstPort", value);
    precord_put(record, "C2S",     string,     direction == FLOW_DIR_SRC2DST ? "C2S" : "S2C");
    value =ya_fvalue_fast_new_uinteger(precord_get_fvalue_allocator(record), YA_FT_UINT8, flow->tuple.inner.proto);
    precord_fvalue_put(record, "Proto", value);
    value =ya_fvalue_fast_new_uinteger(precord_get_fvalue_allocator(record), YA_FT_UINT8,  flow->ttl);
    precord_fvalue_put(record, "TTL", value);

    if(g_config._327_common_switch){
        idx = 0;
        player_t *layer = precord_layer_put_new_layer(record, "327_common");
        write_tbl_log_p327_header(record, &idx, log_len_max, &flow->p327_header[direction]);
    }

}

void write_tbl_common(struct tbl_log *log_ptr, int log_len_max, struct flow_info *flow, int direction,
                      SdtMatchResult *match_result) {
    if(!log_ptr || !flow)
        return;
    precord_t *record = log_ptr->record;
    write_common(record, log_len_max, flow, direction);
}

void write_tbl_log_common(struct flow_info *flow, int direction, struct tbl_log *log_ptr, int *idx, int log_len_max, SdtMatchResult *match_result)
{
    //return; //MARK
    int link_index = 0;
#ifdef DPI_SDT_ZDY
    write_tbl_common(log_ptr, log_len_max, flow, direction, match_result);
#else
    write_tbl_log_share_header(log_ptr, log_len_max, flow, direction);
    #ifndef DPI_SDT_YNAO
    write_link_log(flow, direction, log_ptr, &link_index, match_result);
    #endif
#endif
    return;
}

int write_proto_field_tab(dpi_field_table *proto_array,int max_len,const char *proto_name)
{
    if(proto_name==NULL || proto_array==NULL){return 0;}

    int i;
    char file_path[256]={0};
    snprintf(file_path,256,"%s/%s_f.txt",g_config.dpi_field_dir,proto_name);
    FILE *fp=fopen(file_path,"w+");
    if(fp){
        for(i=0;i<(int)manager_map_fields[PROTOCOL_SHARE_HEADER].protocol_field_array_num;i++){
            fwrite(manager_map_fields[PROTOCOL_SHARE_HEADER].protocol_field_array[i].field_name,
                   strlen(manager_map_fields[PROTOCOL_SHARE_HEADER].protocol_field_array[i].field_name),
                   1,fp);
            fwrite("\n",1,1,fp);
        }

        for(i=0;i<(int)manager_map_fields[PROTOCOL_LINK].protocol_field_array_num;i++){
            fwrite(manager_map_fields[PROTOCOL_LINK].protocol_field_array[i].field_name,
                   strlen(manager_map_fields[PROTOCOL_LINK].protocol_field_array[i].field_name),
                   1,fp);
            fwrite("\n",1,1,fp);
        }

        for(i=0;i< max_len;i++){
            fwrite(proto_array[i].field_name,strlen(proto_array[i].field_name),1,fp);
            fwrite("\n",1,1,fp);
        }

        fclose(fp);
    } else DPI_SYS_LOG(DPI_LOG_WARNING, "can't open field table file");

    return 1;
}

/*
* @Param : proto_array - unique field name.
* @Param : proto_len - the length of the "proto_array".
* @Param : proto_name - the name of a protocol.
* @Param : str_array - you need build the array.
* @Param : str_size - the length of "str_array".
* @Param : group_num - total size.
*/
int write_proto_field_tab_ex(dpi_field_table *proto_array, int proto_len, const char *proto_name, repeat_str* str_array, int str_size, int group_num)
{
    if ( NULL == proto_array || proto_len < 0 || NULL == proto_name || NULL == str_array || str_size <= 0 || group_num <= 0)
        return -1;

    int i = 0;
    int j = 0;
    int circle = 0;
    int entry = 0;

    char field_name[64];
    memset(field_name, 0, sizeof(field_name));

    char file_path[256] = { 0 };
    snprintf(file_path, 256, "%s/%s_f.txt", g_config.dpi_field_dir, proto_name);
    FILE *fp = fopen(file_path, "w+");
    if (fp) {
        /* common field */
        for(i=0;i<(int)(sizeof(dpi_sdt_rule_field)/sizeof(dpi_sdt_rule_field[0]));i++){
            fwrite(dpi_sdt_rule_field[i].field_name,strlen(dpi_sdt_rule_field[i].field_name),1,fp);
            fwrite("\n",1,1,fp);
        }
        /* common field */
        for (i = 0; i < (int)(sizeof(dpi_common_field) / sizeof(dpi_common_field[0])); i++) {
            fwrite(dpi_common_field[i].field_name, strlen(dpi_common_field[i].field_name), 1, fp);
            fwrite("\n", 1, 1, fp);
        }

        /* normal field */
        for (i = 0; i < proto_len; i++) {
            fwrite(proto_array[i].field_name, strlen(proto_array[i].field_name), 1, fp);
            fwrite("\n", 1, 1, fp);
        }

        /* repeat field */
        for (i = 0, j = 0; i < str_size * group_num && j < str_size; i++) {

            circle = i / str_size;
            entry = j % str_size;

            sprintf(field_name, "%s%d", str_array[entry], circle);

            if (entry == str_size - 1)
                j = 0;
            else
                j++;

            fwrite(field_name, strlen(field_name), 1, fp);
            fwrite("\n", 1, 1, fp);
        }

        fclose(fp);
    } else DPI_SYS_LOG(DPI_LOG_WARNING, "can't open proto field ex");

    return 0;
}

int plugin_write_proto_field_tab(char **proto_array,int max_len,const char *proto_name)
{
    if(proto_name==NULL || proto_array==NULL){return 0;}
    int i;
    for(i=0;i< max_len;i++){
        if(NULL==proto_array[i]){
            return 0;
        }
    }

    char file_path[256]={0};
    snprintf(file_path,256,"%s/%s_f.txt",g_config.dpi_field_dir,proto_name);
    FILE *fp=fopen(file_path,"w+");
    if(fp){
        /* common field */
        for(i=0;i<(int)(sizeof(dpi_common_field)/sizeof(dpi_common_field[0]));i++){
            fwrite(dpi_common_field[i].field_name,strlen(dpi_common_field[i].field_name),1,fp);
            fwrite("\n",1,1,fp);
        }

        for(i=0;i< max_len;i++){
            fwrite(proto_array[i],strlen(proto_array[i]),1,fp);
            fwrite("\n",1,1,fp);
        }

        fclose(fp);
    } else DPI_SYS_LOG(DPI_LOG_WARNING, "can't open field table file");

    for(i=0;i< max_len;i++){
        if(proto_array[i])
        {
            free(proto_array[i]);
            proto_array[i] = NULL;
        }
    }
    if(proto_array){
        free(proto_array);
    }

    return 1;
}

int get_now_datetime(char *time_str, int len)
{
    time_t now = g_config.g_now_time;
    struct tm tm_tmp;
    localtime_r(&now, &tm_tmp);

    snprintf(time_str, len, "%04d-%02d-%02d %02d:%02d:%02d",
                        tm_tmp.tm_year + 1900,
                        tm_tmp.tm_mon + 1,
                        tm_tmp.tm_mday,
                        tm_tmp.tm_hour,
                        tm_tmp.tm_min,
                        tm_tmp.tm_sec);
    return 0;
}

int get_macstring(char *__str, int len, const uint8_t *mac)
{
    memset(__str, 0, len);
    sprintf(__str,"%02x:%02x:%02x:%02x:%02x:%02x", mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
    return 0;
}

int get_ip4string(char *__str, int len, uint32_t ip)
{
    uint8_t *tmp = (uint8_t *)&ip;
    snprintf(__str, len, "%u.%u.%u.%u", tmp[0], tmp[1], tmp[2], tmp[3]);
    return 0;
}

int get_ip6string(char *__str, int len, const uint8_t *ip)
{
    struct in6_addr addr;
    memcpy(&addr, ip, 16);
    inet_ntop(AF_INET6, &addr, __str, len);

    return 0;
}

int get_iparray_to_string(char *__str, int len, const uint8_t* ip)
{
    snprintf(__str, len, "%u.%u.%u.%u", ip[0], ip[1], ip[2], ip[3]);
    return 0;
}

void get_ipstring(uint8_t version, uint8_t *input, char *output, uint16_t o_len)
{
  int af = 0;
  if (version == 4) {
    af = AF_INET;
  } else if (version == 6) {
    af = AF_INET6;
  }
  inet_ntop(af, input, output, o_len);
}

uint32_t dotted_to_addr(const char* ip_string)
{
    int addr[4];

    if (sscanf(ip_string, "%d.%d.%d.%d", addr+0, addr+1, addr+2, addr+3) != 4 )
    {
        return 0;
    }

    if ( addr[0] < 0 || addr[1] < 0 || addr[2] < 0 || addr[3] < 0 ||
         addr[0] > 255 || addr[1] > 255 || addr[2] > 255 || addr[3] > 255 )
    {
        DPI_LOG(DPI_LOG_ERROR, "mismatch conditon ipstring :%s",ip_string);
        return 0;
    }

    uint32_t a = (addr[0] << 24) | (addr[1] << 16) | (addr[2] << 8) | addr[3];
    a = htonl(a);

    return a;
}

static
void json_write_head(enum tbl_log_type type, int thread_id)
{
    int i = 0;
    FILE *fp = NULL;    // 线程文件描述符
    char buf[8192] = { 0 };
    struct tbl_log_file * attr = &tbl_log_array[type];
    fp = attr->fp_tbl[thread_id];

    // 原本计划在头部写上字段映射表的，不过 fjeld_json 目录中已经有json文件映射表了，就不在这多此一举了
    cJSON * root = cJSON_CreateObject();

    const char * desc_value = "这里是描述";
    cJSON_AddItemToObject(root, "desc", cJSON_CreateString(desc_value));

    const char * proto_val = "proto_name";
    cJSON_AddItemToObject(root, "proto", cJSON_CreateString(proto_val));

    uint16_t comm_cnt;
    cJSON * json = cJSON_CreateArray();
    comm_cnt = sizeof(dpi_common_field) / sizeof(dpi_common_field[0]);
    for (i = 0; i < comm_cnt; ++i) {
        cJSON_AddItemToArray(json, cJSON_CreateString(dpi_common_field[i].field_name));
    }
    for (i = 0; i < g_json_field[type].field_cnt; ++i) {
        char * str = g_json_field[type].field[i];
        cJSON_AddItemToArray(json, cJSON_CreateString(str));
    }

    cJSON_AddItemToObject(root, "fields", json);

    cJSON *empty_arr = cJSON_CreateArray();
    cJSON_AddItemToObject(root, "values", empty_arr);

    char * data = cJSON_Print(root);

    fwrite(data, strlen(data), 1, fp);
}

static
void tbl_send_kafka(const uint8_t  *payload, uint16_t payload_len)
{
    uint8_t ret = -1;
    DPMHeader dmpheader;
    PAYLOAD_HEADER payload_header;
    memset(&dmpheader, 0, sizeof(DPMHeader));
    memset(&payload_header, 0, sizeof(PAYLOAD_HEADER));
    ret = kafka_producer_send_message(g_kafka_handle, &dmpheader, &payload_header, payload, payload_len);
}

int recoder_verifiable(precord_t *record)
{
    int index = 0;
    player_t *player = NULL;
    const char *name = NULL;
    for(player = precord_layer_get_first(record); NULL != player; player = precord_layer_get_next(record, player))
    {
        name = precord_layer_get_layer_name(player);
        //printf("%s [%s]\n", __func__, name);
        index++;
    }
    return index;
}

int record_write_json(precord_t *record, cJSON *json)
{
    for(player_t *player = precord_layer_get_first(record); NULL != player; player = precord_layer_get_next(record, player))
    {
        for (pfield_t *field = precord_field_get_first_from_layer_of(player); field != NULL; field = precord_field_get_next_from_layer_of(player, field))
        {
            pfield_desc_t *fdesc  = precord_field_get_fdesc(field);
            ya_fvalue_t   *fvalue = precord_field_get_fvalue(field);
            const char *field_name = pfdesc_get_name(fdesc);
            char *value_string = fvalue ? ya_fvalue_to_string_repr(fvalue, BASE_NONE) : "";
            cJSON_AddItemToObject(json, field_name, cJSON_CreateString(value_string));
            fvalue?ya_fvalue_free_string_repr(value_string):42;
        }
    }
    return 0;
}

int record_2_string(precord_t *record, char *buff, int size)
{
    cJSON *json = cJSON_CreateObject();
    record_write_json(record, json);
    char *str = cJSON_PrintUnformatted(json);
    int len = strlen(str);
    len = len >= size ? size-1 : len;
    memcpy(buff, str, len);
    free(str);
    cJSON_Delete(json);
    return len;
}

int record_2_file(precord_t *record, FILE *fp)
{
    int max = 1024*1024*10; //10M
    char *str = malloc(max);
    int len = record_2_string(record, str, max);
    int ret = fwrite(str, len, 1, fp);
    free(str);
    if(ret != len)
    {
        perror(__func__);
    }
	return 0;
}

static
void report_tbl_2_json(struct tbl_log *log, padapt_engine_t * adapt_engine)
{
    int i = 0;
    int ret = 0;
    char tbl_fp_key[SDT_RULE_KEY_SIZE];
    snprintf(tbl_fp_key, SDT_RULE_KEY_SIZE, "%s_%s_%d_%d", log->out_elem->match_result->taskID,
                                                      log->out_elem->match_result->groupID,
                                                      log->thread_id,
                                                      log->log_type);
    gpointer _value = g_hash_table_lookup(tbl_log_file_table, tbl_fp_key);
    if (!_value) {
        printf("WARN tbl_log_file_table hash find failed, key = %s, LINE=%u\n", tbl_fp_key, __LINE__);
        return;
    }

    struct tbl_log_file_node *value = (struct tbl_log_file_node*)_value;
    struct tbl_log_file *log_attr = &tbl_log_array[log->log_type];
    cJSON * val_elem = cJSON_CreateObject();

    //////////////将规则相关信息填充到JSON内容中////////////////////////
    char buff_rule_id[12];
    snprintf(buff_rule_id, sizeof(buff_rule_id), "%u", log->out_elem->match_result->ruleID);
#ifdef DPI_SDT_YNAO
    precord_layer_move_cursor(log->record, "common");
    precord_put(log->record, "tags_task",  string, log->out_elem->match_result->taskID);
    precord_put(log->record, "tags_rule",  string, buff_rule_id);
#else
    struct {
        const char*name;
        const char*care;
    } taskinfo[] = {
        {"RuleID",      buff_rule_id},
        {"UintID",      log->out_elem->match_result->unitID},
        {"TaskID",      log->out_elem->match_result->taskID},
        {"GroupName",   log->out_elem->match_result->groupName},
        {"GroupID",     log->out_elem->match_result->groupID},
        {"Method",      log->out_elem->match_result->method},
        {"TopicName",   log->out_elem->match_result->topicName},
        {NULL,          NULL}
    };
    for(i = 0; taskinfo[i].name; i++)
    {
        cJSON_AddItemToObject(val_elem, taskinfo[i].name, cJSON_CreateString(taskinfo[i].care));
    }
#endif
    //////////////将规则相关信息填充到JSON内容中////////////////////////
    if (g_config.adapt_type == 0) {
        precord_t * adapt_record = NULL;
        ret = dpi_padapt_record_adapt(adapt_engine, &adapt_record, log->record);
        if (adapt_record) {
          record_write_json(adapt_record, val_elem);
          #ifdef ENABLE_ARKIME

          // 发送session数据到Arkime ES
            int arkime_result = dpi_arkime_send_session_to_es(log->flow,adapt_record);
            if (arkime_result != 0) {
                // 记录发送失败，但不影响正常的日志输出流程
                printf("WARN: Failed to send session data to Arkime ES for flow_id=%lu\n",
                       log->record->flow->flow_id);
            }
            #endif
            precord_destroy(adapt_record);
        } else {
            record_write_json(log->record, val_elem);
        }
    } else {
        record_write_json(log->record, val_elem);
    }

    cJSON * obj = cJSON_CreateObject();
    if (manager_map_fields[log->proto_id].protocol_reflect_protoname) {
        cJSON_AddItemToObject(obj, manager_map_fields[log->proto_id].protocol_reflect_protoname, val_elem);
    } else {
        cJSON_AddItemToObject(obj, log_attr->protoname, val_elem);
    }

    FILE *fp = value->fp_tbl;
    char *field_val = cJSON_PrintUnformatted(obj);
    fwrite(field_val, strlen(field_val), 1, fp);
    fwrite("\n", 1, 1, fp);

    if (g_config.sdt_send_kafka)
    {
        tbl_send_kafka((const uint8_t *)field_val, strlen(field_val));
    }

    cJSON_Delete(obj);
    free(field_val);
}

/**
*每类tbl日志的文件的切换
*/
static void _close_and_open_file(enum tbl_log_type type , int timeout, int thread_id, sdt_out_status *out_elem)
{
    char filename[256];
    char filename_rename[256];
    char dirname[128];
    size_t len;
    struct tbl_log_file * attr = &tbl_log_array[type];
    char tbl_fp_key[SDT_RULE_KEY_SIZE];
    snprintf(tbl_fp_key, SDT_RULE_KEY_SIZE, "%s_%s_%d_%d", out_elem->match_result->taskID,
                                                      out_elem->match_result->groupID,
                                                      thread_id,
                                                      type);
    gpointer _value = g_hash_table_lookup(tbl_log_file_table, tbl_fp_key);
    struct tbl_log_file_node *value = (struct tbl_log_file_node*)_value;
    /* 没文件，程序启动以来刚产生tbl-log*/
    if (!timeout && value == NULL ) {
        value = (struct tbl_log_file_node*)dpi_malloc(sizeof(struct tbl_log_file_node));
        if(!value){
            DPI_LOG(DPI_LOG_WARNING, "malloc tbl_fp failed");
            return;
        }
        memset(value, 0, sizeof(struct tbl_log_file_node));
        struct timeval tv;
        len = sizeof(value->filename);
        char * name = value->filename;
#ifdef DPI_SDT_ZDY
        dpi_zdy_get_filename(type, thread_id, name);
#else
        //snprintf(dirname, sizeof(dirname),"%s/%s",g_config.tbl_out_dir,time_to_date(g_config.g_now_time));
        if (type > TBL_LOG_FTP_CONTROL &&  manager_map_fields[type+1].protocol_reflect_protoname)
            snprintf(dirname, sizeof(dirname),"/tmp/tbls/sdt/report/%s/%s", manager_map_fields[type+1].protocol_reflect_protoname, time_to_date(g_config.g_now_time));
        else if (manager_map_fields[type].protocol_reflect_protoname)
            snprintf(dirname, sizeof(dirname),"/tmp/tbls/sdt/report/%s/%s", manager_map_fields[type].protocol_reflect_protoname, time_to_date(g_config.g_now_time));
        else
            snprintf(dirname, sizeof(dirname),"/tmp/tbls/sdt/report/%s/%s", tbl_log_array[type].protoname, time_to_date(g_config.g_now_time));
        if (access(dirname, F_OK))
            mkdirs(dirname);

        int get_new_file_seq = dpi_safe_get_filename_seq(FILE_SEQ_TYPE_GLOBAL);

        snprintf(name, len,
                "%s/%s-%s-report-%u",
                dirname,
                g_config.sdt_out_produce_data_dev_name,
                g_config.sdx_config.sdx_ip_str,
                get_new_file_seq);

        if(g_config.show_task_id){
            snprintf(name + strlen(name), len, "%s", g_config.task_id);
        }

#endif
        snprintf(filename, sizeof(filename), "%s.%s.tmp", name, g_config.yv_data_suffix);
        value->fp_tbl = fopen(filename, "w");
        log_trace("new_JSON_file:%s timeout:%u", filename, g_config.web_config.json_truncation);
        if (value->fp_tbl == NULL)
            DPI_SYS_LOG(DPI_LOG_WARNING, "can't open data log file");

        value->timeout_sec = g_config.g_now_time;
        g_hash_table_insert(tbl_log_file_table, strdup(tbl_fp_key), value);
    }
}

static void _get_file(int type, int thread_id, sdt_out_status *out_elem)
{

    if(type>=PLUGIN_PROTO_START_ID){
    }else if(type<TBL_LOG_MAX && type>=TBL_LOG_IPP){
        _close_and_open_file(type, 0, thread_id, out_elem);
    }

    return;
}

static void writer_record_to_tbl(struct tbl_log *log)
{
    int  i = 0;
    int value_string_len;
    FILE *fp = NULL;
    precord_t * record = NULL;
    char tbl_fp_key[SDT_RULE_KEY_SIZE];
    snprintf(tbl_fp_key, SDT_RULE_KEY_SIZE, "%s_%s_%d_%d", log->out_elem->match_result->taskID,
             log->out_elem->match_result->groupID, log->thread_id, log->log_type);
    gpointer _value = g_hash_table_lookup(tbl_log_file_table, tbl_fp_key);
    if (!_value) {
        printf("WARN tbl_log_file_table hash find failed, key = %s, LINE=%u\n", tbl_fp_key, __LINE__);
        return;
    }

    struct tbl_log_file_node *value    = (struct tbl_log_file_node *)_value;
    struct tbl_log_file      *log_attr = &tbl_log_array[log->log_type];

    fp = value->fp_tbl;
    record = log->record;

    write_327ZDY_task_info(log->out_elem->match_result, record, (const char *)log->flow->path);
    for(player_t *player = precord_layer_get_first(record); NULL != player; player = precord_layer_get_next(record, player))
    {
        for (pfield_t *field = precord_field_get_first_from_layer_of(player); field != NULL; field = precord_field_get_next_from_layer_of(player, field))
        {
            pfield_desc_t *fdesc  = precord_field_get_fdesc(field);
            ya_fvalue_t   *fvalue = precord_field_get_fvalue(field);
            const char *field_name = pfdesc_get_name(fdesc);
            char *value_string = fvalue ? ya_fvalue_to_string_repr(fvalue, BASE_NONE) : "";

            value_string_len = strlen(value_string);
            for(i=0; i < value_string_len; i++){
                if ((value_string[i] == '\r' && i+1 < value_string_len && value_string[i+1] == '\n') || value_string[i] == '\n')
                    value_string[i] = ' ';
                if (value_string[i] == '|')
                    value_string[i] = '_';
            }
            fwrite(value_string, strlen(value_string), 1, fp);
            fwrite("|", 1, 1, fp);
            fvalue?ya_fvalue_free_string_repr(value_string):42;
        }
    }

    fwrite("\n", 1, 1, fp);
}

static void write_file(struct tbl_log *log, padapt_engine_t *adapt_engine)
{
    int i;
    struct tbl_log_file * attr = &tbl_log_array[log->log_type];
    if (log->log_type >= TBL_LOG_MAX) return;
    for(int i = 0; i < log->match_res_cnt; ++i)
    {
        log->out_elem = log->match_info[i];
        _get_file(log->log_type, log->thread_id, log->out_elem);
        char tbl_fp_key[SDT_RULE_KEY_SIZE];
        snprintf(tbl_fp_key, SDT_RULE_KEY_SIZE, "%s_%s_%d_%d", log->out_elem->match_result->taskID,
                                                          log->out_elem->match_result->groupID,
                                                          log->thread_id,
                                                          log->log_type);
        gpointer _value = g_hash_table_lookup(tbl_log_file_table, tbl_fp_key);

        if (unlikely(!_value)) {
            log_warn("tbl_log_file_table hash find failed, key = %s\n", tbl_fp_key);
            return;
        }

        struct tbl_log_file_node *value = (struct tbl_log_file_node*)_value;
        if (value->fp_tbl) {
#ifdef DPI_SDT_ZDY
            writer_record_to_tbl(log);
#else //DPI_SDT_ZDY
            report_tbl_2_json(log, adapt_engine);
#endif //DPI_SDT_ZDY
            log_total[log->log_type][0]++;
            value->log_num++;
        }
        fflush(value->fp_tbl);
    }
    return;
}


static int get_tll_log_file(struct tbl_log *log, struct tbl_log_file_node **value)
{
    struct tbl_log_file_node *_value;
    char tbl_fp_key[SDT_RULE_KEY_SIZE];

    // 构造 hash key
    snprintf(tbl_fp_key, SDT_RULE_KEY_SIZE, "TLL_%d_%d", log->thread_id, log->log_type);

    _value = (struct tbl_log_file_node*)g_hash_table_lookup(tbl_log_file_table, tbl_fp_key);

    if (_value) {
        *value = _value;
        return 0;
    }

    // 没有则创建
    _value = (struct tbl_log_file_node*)dpi_malloc(sizeof(struct tbl_log_file_node));
    memset(_value, 0, sizeof(struct tbl_log_file_node));

    FILE    *fp;
    int     idx = 0;
    char    dirname[128];
    char    tmp_fname[256];

    // 构建目录
    //snprintf(dirname, sizeof(dirname), "%s/%s/tll", g_config.tbl_out_dir, time_to_date(g_config.g_now_time));
    snprintf(dirname, sizeof(dirname), "/tmp/tbls/sdt/tll/%s", time_to_date(g_config.g_now_time));
    if (access(dirname, F_OK))
        mkdirs(dirname);

    // 构建文件名, _value->filename 不含后缀
    idx = snprintf(_value->filename, sizeof(_value->filename),
            "%s/%s-%s-tll-%u",
            dirname,
            g_config.sdt_out_produce_data_dev_name,
            g_config.sdx_config.sdx_ip_str,
            dpi_safe_get_filename_seq(FILE_SEQ_TYPE_GLOBAL));

    if (g_config.show_task_id) {
        idx += snprintf(_value->filename + idx, sizeof(_value->filename) - idx, "-%s", g_config.task_id);
    }

    // 真实文件名
    snprintf(tmp_fname, sizeof(tmp_fname), "%s.%s.tmp", _value->filename, g_config.yv_data_suffix);

    fp = fopen(tmp_fname, "w");

    if (fp == NULL) {
        dpi_free(_value);
        DPI_SYS_LOG(DPI_LOG_ERROR, "can't open TLL log file");
        return -1;
    }

    log_trace("new_JSON_file:%s timeout:%u", tmp_fname, g_config.web_config.json_truncation);

    // 初始化属性
    _value->fp_tbl      = fp;
    _value->timeout_sec = g_config.g_now_time;
    _value->log_num     = 0;

    // 插入新元素
    g_hash_table_insert(tbl_log_file_table, strdup(tbl_fp_key), _value);

    *value = _value;
    return 0;
}


static void write_tll_file(struct tbl_log *log, padapt_engine_t *adapt_engine)
{
    struct  tbl_log_file_node *log_file;

    if (get_tll_log_file(log, &log_file) < 0)
    {
        return;
    }

    cJSON      *obj = cJSON_CreateObject();
    cJSON *val_elem = cJSON_CreateObject();

    if (adapt_engine) {
        precord_t * adapt_record = NULL;
        dpi_padapt_record_adapt(adapt_engine, &adapt_record, log->record);
        if (adapt_record) {
            record_write_json(adapt_record, val_elem);
            precord_destroy(adapt_record);
        } else {
            record_write_json(log->record, val_elem);
        }
    } else {
        record_write_json(log->record, val_elem);
    }

    cJSON_AddItemToObject(obj, "tll", val_elem);

    char *field_val = cJSON_PrintUnformatted(obj);
    fwrite(field_val, strlen(field_val), 1, log_file->fp_tbl);
    fwrite("\n", 1, 1, log_file->fp_tbl);
    fflush(log_file->fp_tbl);
    log_file->log_num++;

    if (g_config.sdt_send_kafka)
    {
        tbl_send_kafka((const uint8_t *)field_val, strlen(field_val));
    }

    cJSON_Delete(obj);
    free(field_val);
}

/**
 * @brief 初始化log指针
 *
 * @param log_ptr log指针
 * @param flow
 * @param tbl_log_proto_id 传入本条tbl的PROTO_XXX 目的是解决嵌套中输出协议字段表匹配
 */
void init_log_ptr_data(struct tbl_log *log_ptr, struct flow_info *flow,int tbl_log_proto_id)
{
    rte_atomic16_set(&log_ptr->ref_cnt, 1);
#ifndef ENABLE_ARKIME
    log_ptr->flow = dpi_flow_clone(flow);
#endif
    log_ptr->match_res_cnt = 0;
    if (tbl_log_proto_id == EM_TBL_LOG_ID_BY_DEFAULT) {
        if (flow)
            log_ptr->proto_id = flow->real_protocol_id;
    }else {
        log_ptr->proto_id = tbl_log_proto_id;
    }

    if (!flow) return;

    log_ptr->match_data_len = flow->match_data_len;

    //ethhdr 值的是 MBUF 内存中的地址
    //pkt_data_len 就在当初的报文长度
#ifdef DPI_FUTURE_MBUF
    if(flow->pkt)
    {
        memcpy(&log_ptr->pkt, flow->pkt, sizeof(struct pkt_info));
        log_ptr->pkt.mbuf = rte_mbuf_clone_0(flow->pkt->mbuf);
        log_ptr->pkt.flow = NULL;
    }
    else
    {
        memset(&log_ptr->pkt, 0, sizeof(struct pkt_info));
    }
#else
    if(flow->pkt && flow->pkt->ethhdr && flow->pkt->pkt_len < (int)sizeof(log_ptr->pkt_data))
    {
        memcpy(log_ptr->pkt_data, flow->pkt->ethhdr, flow->pkt->pkt_len);
        log_ptr->pkt_data_len = flow->pkt->pkt_len;
    }
#endif
}

/*
 * 解析线程写日志的调用接口
*/
int write_tbl_log(struct tbl_log     *log)
{
    if(NULL == log->flow)
    {
        printf("write_tbl_log log_type %u flow is null.\n", log->log_type);//ARP没有FLOW?
        return -1;
    }

    //record_show(log->record);
    int dir = !!log->flow->flow_direction_flag;
    if (g_config.sdt_mac_forward_flag && log->flow->pSDTMacHeader[dir]) {
        memcpy(&log->mac_hdr, log->flow->pSDTMacHeader[dir], sizeof(struct mac_packet_header));
    }

    int ring_id = log->thread_id % g_config.app_match_thread_num;
    int ret = rte_ring_mp_enqueue(app_match_ring[ring_id],(void *)log);
    if(0==ret){
    }else{
        rte_atomic64_inc(&app_match_fail_pkts);
        DPI_LOG(DPI_LOG_WARNING, "enqueue app sdt match ring ring failed, ring:%d log_type %d",
                                 log->thread_id % g_config.app_match_thread_num,
                                 log->log_type);
        return -1;
    }
    return 1;
}

uint8_t equeue_app_fields_by_copy(void *tlog)
{
    int ring_id = 0;
    struct tbl_log *log=(struct tbl_log *)tlog;
    int ret=0;
    if(NULL == log->out_elem)
    {
        printf("ERROR: %s NULL == log->out_elem\n", __func__);
        abort();
    }

    if(NULL == log->out_elem->match_result)
    {
        printf("ERROR:NULL log->out_elem->match_result\n");
        abort();
    }

    //原则
    //内存节点 谁创建谁释放. 入队创建出队释放. -- 其他的管不着,也懒得管.
    //外面的这个 tlog 来源复杂 -- 这里不对其托管. 它爱咋咋地.
    struct tbl_log  *new_tbl = NULL;
    if (rte_mempool_get(tbl_log_mempool, (void **)&new_tbl) < 0) {
        DPI_LOG(DPI_LOG_WARNING, "in %s rte_mempool_get not enough memory", __func__);
        return 0; //安全
    }

    //副本  -- record 克隆
    memcpy(new_tbl, log, sizeof(struct tbl_log));
    new_tbl->record = sdt_precord_clone(log->record);
    new_tbl->flow = dpi_flow_clone(log->flow);

AGAIN:
    //折叠为一行, 便于GREP检索
    ring_id = new_tbl->thread_id % g_config.log_thread_num;
    ret = rte_ring_mp_enqueue(tbl_ring[ring_id],(void *)new_tbl);
    if(ret==0){
        return 1;
    }

    //等待直到入队成功
    usleep(200*1000);
    goto AGAIN;

    return 0;
}

uint8_t dpi_app_match_res_enqueue(void *tbl)
{
  int             ring_id = 0;
  struct tbl_log *log     = (struct tbl_log *)tbl;
  struct tbl_log *new_tbl = NULL;
  int             ret     = 0;
//   if (NULL == log->out_elem) {
//     printf("ERROR: %s NULL == log->out_elem\n", __func__);
//     abort();
//   }

//   if (NULL == log->out_elem->match_result) {
//     printf("ERROR:NULL log->out_elem->match_result\n");
//     abort();
//   }
#ifdef ENABLE_ARKIME
  ring_id = log->thread_id % g_config.log_thread_num;
  ret     = rte_ring_mp_enqueue(tbl_ring[ring_id], (void *)tbl);
  if (ret != 0) {
    dpi_tbl_free(tbl);
    return 1;
  }
  return 0;
#else

  new_tbl = dpi_tbl_clone_ref(log);

  // 折叠为一行, 便于GREP检索
  ring_id = new_tbl->thread_id % g_config.log_thread_num;
  ret     = rte_ring_mp_enqueue(tbl_ring[ring_id], (void *)new_tbl);
  if (ret != 0) {
    dpi_tbl_free(new_tbl);
    return 1;
  }

  return 0;
#endif
}

static gboolean _close_tbl_file(gpointer key, gpointer value, gpointer user_data);
struct close_tbl_file
{
    uint8_t close_flag;
    long ring_id;
};


static inline
void *tbl_out_UNITTEST(DpiTblThreadCtx *ctx, struct tbl_log *tbl)
{
    if (unlikely(!ctx->test_tbl_fp))
    {
        char tbl_path[PATH_MAX];
        snprintf(tbl_path, PATH_MAX, "%s/test_tbl_%d.txt", g_config.pcap_dir, ctx->ring_id);
        ctx->test_tbl_fp = fopen(tbl_path, "w");
        if (!ctx->test_tbl_fp)
        {
            perror("fopen");
            exit(1);
        }
    }

    for (int j=0; j<tbl->match_res_cnt; j++)
    {
        SdtMatchResult *res = tbl->match_info[j]->match_result;
        fprintf(ctx->test_tbl_fp, "%s|%s|%u|\n", res->unitID, res->taskID, res->ruleID);
    }

    return NULL;
}


uint8_t   tbl_out_running = 1;
static pthread_t tbl_out_pthread[MAX_THREAD_NUM] = {0};

static void
tbl_out_loop(DpiTblThreadCtx *ctx)
{
    int j, dequeue_num;
    struct rte_ring     *ring = tbl_ring[ctx->ring_id];
    struct tbl_log      *tbl, *tbl_burst[TBL_MAX_BURST];
    padapt_engine_t     *adapt_engine     = NULL;
    padapt_engine_t     *adapt_tll_engine = NULL;
    gpointer            key, value;
    GHashTableIter      iter;

    struct close_tbl_file user_data;
    user_data.close_flag = 0;
    user_data.ring_id    = ctx->ring_id;

    if (g_config.adapt_type == 0) {
        adapt_engine = dpi_padapt_engine_create();
        if (g_config.protocol_switch[PROTOCOL_TLL] != 0) {
            adapt_tll_engine = dpi_padapt_tll_engine_create();
        }
    }

    while (1)
    {
        // 线程 结束信号
        if (unlikely(tbl_out_running == 0)) {
            if (rte_ring_empty(ring))
                break;
        }

        // 线程 暂停信号
        if (unlikely(g_sdt_hash_db_clear_flag))
        {
            // 规则即将重置, 已匹配的数据不重要了, 尽快清空队列
            do {
                usleep(1000*100); // todo: 如果匹配线程还有在匹配 尚未入队的数据?
                dequeue_num = rte_ring_sc_dequeue_burst(ring, (void **)tbl_burst, TBL_MAX_BURST, NULL);
                for (j = 0; j < dequeue_num; j++) {
                    dpi_tbl_free(tbl_burst[j]);
                }
            } while (dequeue_num > 0);

            ATOMIC_SUB_FETCH(&tbl_out_thfunc_signal);
            while(g_sdt_hash_db_clear_flag)
            {
                usleep(1000*100);
            }
            ATOMIC_ADD_FETCH(&tbl_out_thfunc_signal);
        }

        // 正常干活
        dequeue_num = rte_ring_sc_dequeue_burst(ring, (void **)tbl_burst, TBL_MAX_BURST, NULL);

        for (j = 0; j < dequeue_num; j++) {
            tbl = tbl_burst[j];

            if (g_config.output_write != 1) {
                dpi_tbl_free(tbl);
                continue;
            }

            // 普通日志
            write_file((struct tbl_log *)tbl, adapt_engine);

            // 通联日志需要特殊处理
            if (g_config.protocol_switch[PROTOCOL_TLL] == 1
                && tbl->flow->tll_flag == 1)
            {
                dpi_tll_record_backfill(tbl->record, &tbl->flow->tll_msg);
                write_tll_file((struct tbl_log *)tbl, adapt_tll_engine);
            }

            dpi_tbl_free(tbl);
        }

        if (0 == dequeue_num) {
            usleep(200 * 1000);
        }

        g_hash_table_foreach_remove(tbl_log_file_table, _close_tbl_file, &user_data);

        // 判断是否还有未关闭fd
        g_hash_table_iter_init(&iter, tbl_log_file_table);
        if (0 == g_hash_table_iter_next(&iter, &key, &value))
            g_config.opened_fd_flag[ctx->ring_id] = 0;
        else if (g_config.opened_fd_flag[ctx->ring_id] == 0)
            g_config.opened_fd_flag[ctx->ring_id] = 1;
    }

        // 结束清理
    user_data.close_flag = 1;
    g_hash_table_foreach_remove(tbl_log_file_table, _close_tbl_file, &user_data);
    g_config.tblwrite_flag_per_core[ctx->ring_id] = 1;
    g_config.opened_fd_flag[ctx->ring_id] = 0;
}

/**
 * 自动测试专用输出行为
 */
static void
tbl_out_loop_test(DpiTblThreadCtx *ctx)
{
    int j, dequeue_num;
    struct rte_ring *ring = tbl_ring[ctx->ring_id];
    struct tbl_log  *tbl, *tbl_burst[TBL_MAX_BURST];

    while (1)
    {
        // 线程 结束信号
        if (unlikely(tbl_out_running == 0)) {
            if (rte_ring_empty(ring))
                break;
        }

        // 线程 暂停信号
        if (unlikely(g_sdt_hash_db_clear_flag))
        {
            do {
                usleep(1000*100); // todo: 如果匹配线程还有在匹配 尚未入队的数据?
                dequeue_num = rte_ring_sc_dequeue_burst(ring, (void **)tbl_burst, TBL_MAX_BURST, NULL);
                for (j = 0; j < dequeue_num; j++)
                {
                    tbl_out_UNITTEST(ctx, tbl_burst[j]);
                    dpi_tbl_free(tbl_burst[j]);
                }
            } while (dequeue_num > 0);

            ATOMIC_SUB_FETCH(&tbl_out_thfunc_signal);
            while(g_sdt_hash_db_clear_flag)
            {
                usleep(1000*100);
            }
            ATOMIC_ADD_FETCH(&tbl_out_thfunc_signal);
        }

        // 正常干活
        dequeue_num = rte_ring_sc_dequeue_burst(ring, (void **)tbl_burst, TBL_MAX_BURST, NULL);

        for (j = 0; j < dequeue_num; j++)
        {
            tbl_out_UNITTEST(ctx, tbl_burst[j]);
            dpi_tbl_free(tbl_burst[j]);
        }
    }

    g_config.tblwrite_flag_per_core[ctx->ring_id] = 1;
    g_config.opened_fd_flag[ctx->ring_id] = 0;
    if (ctx->test_tbl_fp)
        fclose(ctx->test_tbl_fp);
}
#ifdef ENABLE_ARKIME
//调用dpi_arkime_tbl_log中实现的线程(元数据模式)
void *write_tbl_log_to_file_func(void *arg);

#else
/*
* 规则命中的协议解析结果进行落盘写日志线程
*/
void *write_tbl_log_to_file_func(void * arg)
{
    if(g_config.log_thread_type == 2){
      printf("筛选模式不支持输出流表!命令行参数有误\n");
      exit(0);
    }
    DpiTblThreadCtx   *ctx = (DpiTblThreadCtx *)arg;

    pthread_t   thread = pthread_self();
    long        core_id = ctx->core_id;
    int         ret = 0;
    char        thread_name[16];

    ret = create_tbl_log_file_hash_table();
    if (ret) exit(-1);

    ATOMIC_ADD_FETCH(&tbl_out_thfunc_signal);

    snprintf(thread_name, 16, "dpi_output_%ld", core_id);
    pthread_setname_np(pthread_self(), thread_name);
    ret = dpi_pthread_setaffinity(thread, core_id);
    if (!ret) {
        DPI_LOG(DPI_LOG_ERROR, "Logging thread failed to bind core_id!!!");
    }
    log_info("Logging running thread %ld, thread_id %ld. on core %ld", syscall(SYS_gettid), thread, core_id);

    if (g_config.test_mode) {
        tbl_out_loop_test(ctx);
    } else {
        tbl_out_loop(ctx);
    }
    free(arg);
    ATOMIC_SUB_FETCH(&tbl_out_thfunc_signal);
    return NULL;
}
#endif
static int init_tbl_log_mempool(void)
{
    if (tbl_log_mempool == NULL) {
        char name_buff[COMMON_FILE_NAME]={0};
        snprintf(name_buff,COMMON_FILE_NAME,"tbl_log_mp_%d",g_config.socketid);
        tbl_log_mempool = rte_mempool_create(name_buff, g_config.tbl_ring_size * g_config.log_thread_num,
                sizeof(struct tbl_log), RTE_MEMPOOL_CACHE_MAX_SIZE, 0,
                NULL, NULL,
                NULL, NULL,
                g_config.socketid,
                0);

        if (tbl_log_mempool == NULL) {
            DPI_LOG(DPI_LOG_ERROR, "Cannot init tbl_log_mempool");
            exit(-1);
        } else
            DPI_LOG(DPI_LOG_DEBUG, "Allocated tbl_log_mempool");
    }

    /*有内容的日志分成两个内存池，大小分别是4k（多）和256k（少），写内容时按内容的大小分配*/
    // if (tbl_log_content_mempool_256k == NULL) {
    //     char name_buff[COMMON_FILE_NAME]={0};
    //     snprintf(name_buff,COMMON_FILE_NAME,"content_mempool_big_%d",g_config.socketid);
    //     tbl_log_content_mempool_256k = rte_mempool_create(name_buff, g_config.tbl_log_content_256k_num,
    //                                         1024*256, RTE_MEMPOOL_CACHE_MAX_SIZE, 0,
    //                                         NULL, NULL,
    //                                         NULL, NULL,
    //                                         g_config.socketid,
    //                                         0);

    //     if (tbl_log_content_mempool_256k == NULL) {
    //         DPI_LOG(DPI_LOG_ERROR, "Cannot init tbl_log_content_mempool_256k");
    //         exit(-1);
    //     } else
    //         DPI_LOG(DPI_LOG_DEBUG, "Allocated tbl_log_content_mempool_256k");
    // }
    return 0;
}


static
void init_kafka_handle(void)
{
    g_kafka_handle = kafka_create_producer_handle(g_config.sdt_kafka_ip, "yaDpiSdt");
    if (g_kafka_handle == NULL) {
      printf("kafka handle failed!!!");
    }
}


int init_tbl_log(void)
{
    int status;
    long i;
    int core_id;
    #ifndef ENABLE_ARKIME
    if (g_config.adapt_type == 0) {
        dpi_padapt_load_script(g_config.adapt_dir_lua);
        // dpi_padapt_print_all_schema();
        if (g_config.protocol_switch[PROTOCOL_TLL] != 0) {
            dpi_padapt_tll_load_script(g_config.adapt_dir_lua);
        }
    }
    #endif
    for (int i = 0; i < g_config.log_thread_num; i++) {
        char ring_name[64] = {0};
        snprintf(ring_name, sizeof(ring_name), "tbl_ring_%d_%d",g_config.socketid, i);
        tbl_ring[i] = rte_ring_create(ring_name, g_config.tbl_ring_size, g_config.socketid, RING_F_SC_DEQ);
        if (tbl_ring[i] == NULL) { // rte_ring_create
            DPI_LOG(DPI_LOG_ERROR, "error while create tbl ring\n");
            exit(-1);
        }
        if (rte_ring_lookup(ring_name) != tbl_ring[i]) {
            DPI_LOG(DPI_LOG_ERROR, "Cannot lookup ring from its name\n");
            exit(-1);
        }
    }
#ifdef ENABLE_ARKIME
      #include "arkime/output/dpi_precord_writer_IF.h"

    /* 需要在 pthread_create write_tbl_log_to_file_func 之前创建 writer keeper */
    dpi_output_create_writer_keeper_for_n_thread(g_config.log_thread_num, g_config.tbl_out_dir,g_config.yv_record_type);
#endif //ENABLE_ARKIME
    for(i=0; i < g_config.log_thread_num; i++){
        DpiTblThreadCtx *args = dpi_malloc(sizeof(DpiTblThreadCtx));
        memset(args, 0, sizeof(DpiTblThreadCtx));
        if (g_config.log_core_id[i] != 0) {
            args->core_id = g_config.log_core_id[i];
        } else {
            args->core_id = dpi_numa_get_suitable_core(g_config.socketid);
        }
        args->ring_id = i;
        printf("tbl log core id = %d\n", args->core_id);
        status = pthread_create(&tbl_out_pthread[i], NULL, write_tbl_log_to_file_func, (void *)args);
        if(status != 0) {
            DPI_SYS_LOG(DPI_LOG_ERROR, "error on create log thread");
            exit(-1);
        }
        dpi_numa_set_used_core(args->core_id);
    }

    if (access(g_config.tbl_out_dir, F_OK))
        mkdir(g_config.tbl_out_dir, 0666);

    init_tbl_log_mempool();
    if (g_config.sdt_send_kafka)
      init_kafka_handle();

    dpi_register_proto_schema(dpi_sdt_rule_field, EM_SDT_RULE_MAX, "dpi_sdt_rule");

    dpi_pschema_dump_proto_schemas(g_config.dpi_field_dir);
    return 0;
}

void tbl_out_stop(void)
{
    int i;

    tbl_out_running = 0;

    for(i=0; i < g_config.log_thread_num; i++) {
        pthread_join(tbl_out_pthread[i], NULL);
    }

    log_trace("tbl输出线程退出");
}


static char* getStr_longitude_latitude(const char* buf, char* name, const int nameLen){
  if(!buf || !name || nameLen <= 0) return NULL;
    char* tmp0 =  (char*)buf;

  if(tmp0 && tmp0+4)
    {
        char* tmp1 = strchr(tmp0+4,'<');
        if(tmp1 && tmp1+1)
        {
                int hit_start = 0;
                int hit_last = 0;
                int len = tmp1 - tmp0 - 1;
                memcpy(name, tmp0 ,len < nameLen ? len : nameLen-1);
                for(unsigned long i =0;i<strlen(name) ;i++)
                {
                    //提取数据
                    //": \n          47.253800 <double>\n        \"longitude\": \n          -1.484300 <double>\n
                    switch(name[i])
                    {
                        case '0' ... '9':
                        case '.':
                            if(0 == hit_start)
                            {
                                hit_start = i;
                            }
                            hit_last = i;

                        default:
                            break;
                    }
                    if(hit_start && hit_last)
                    {
                        memmove(name, name+hit_start, hit_last - hit_start+1+1); //+1 补一个数, +1 '\0'
                    }
                }
                return name;
        }
    }
    return NULL;
}

void get_isp_from_ip(const char *ip, char *isp, int len) {

    memset(isp, 0, len);

    if (g_config.mmdb_asn_switch == 0 || !ip)
        return;

    MMDB_entry_data_s entry_data;
    MMDB_lookup_result_s result;
    int gai_error, mmdb_error;

    result = MMDB_lookup_string(&g_config.mmdb_asn, ip, &gai_error, &mmdb_error);
    if (0 != gai_error) {
        return;
    }

    if (MMDB_SUCCESS != mmdb_error) {
        return;
    }


    /* ISP DB INFO Example
    {
       "autonomous_system_number":
         4808 <uint32>
       "autonomous_system_organization":
         "China Unicom Beijing Province Network" <utf8_string>
     }
    */
    if (result.found_entry) {
        /* 获取ASN */
        int status = MMDB_get_value(&result.entry, &entry_data, "autonomous_system_organization", NULL);

        if (MMDB_SUCCESS == status && entry_data.has_data) {/* MMDB_get_value 成功 */
            if (entry_data.type == MMDB_DATA_TYPE_UTF8_STRING) {
                // 不是null-terminated 字符串
                strncpy(isp, entry_data.utf8_string, DPI_MIN((int)entry_data.data_size, len-1));
            }
        }
    }
}

static char* getStrName(const char* buf, char* name, const int nameLen)
{
    if(!buf || !name || nameLen <= 0) return NULL;
    char* tmp0 =  strstr(buf,"\"en\"");
    if(tmp0 && tmp0+4)
    {
        char* tmp1 = strchr(tmp0+4,'\"');
        if(tmp1 && tmp1+1)
        {
            tmp0 = strchr(tmp1+1,'\"');
            if(tmp0)
            {
                int len = tmp0 - tmp1 - 1;
                memcpy(name, tmp1+1, len < nameLen ? len : nameLen-1);
                return name;
            }
        }
    }
    return NULL;
}

static void ip_position_check(IPINFO *pIp)
{
    static const char *state_list[] = {"Taiwan", "Hong Kong", "Macao"};

    for (int i=0; i < (int)array_length(state_list); i++)
    {
        if (strcmp(pIp->country, state_list[i]) == 0)
        {
            strcpy(pIp->state,   pIp->country);
            strcpy(pIp->country, "China");
            break;
        }
    }
}
void get_ip_position_by_mmdb(const char* ip_address, IPINFO* pIp)
{
    if(!ip_address || !pIp)
        return;
    if(!g_config.mmdb_city_switch)
        return;
    int status, gai_error, mmdb_error;
    MMDB_entry_data_s entry_data;
    int               len = sizeof(pIp->area);

    MMDB_lookup_result_s result = MMDB_lookup_string(&g_config.mmdb_city, ip_address, &gai_error, &mmdb_error);
    if (gai_error || MMDB_SUCCESS != mmdb_error || !result.found_entry)
        return;

    status = MMDB_get_value(&result.entry, &entry_data, "city", "names", "en", NULL);
    if (MMDB_SUCCESS == status && entry_data.has_data) { /* MMDB_get_value 成功 */
        if (entry_data.type == MMDB_DATA_TYPE_UTF8_STRING) {
            // 不是null-terminated 字符串
            size_t safe_len = DPI_MIN(entry_data.data_size, len - 1L);
            strncpy(pIp->city, entry_data.utf8_string, safe_len);
        }
    }
    status = MMDB_get_value(&result.entry, &entry_data, "subdivisions","0", "names", "en", NULL);
    if (MMDB_SUCCESS == status && entry_data.has_data) { /* MMDB_get_value 成功 */
        if (entry_data.type == MMDB_DATA_TYPE_UTF8_STRING) {
            // 不是null-terminated 字符串
            size_t safe_len = DPI_MIN(entry_data.data_size, len - 1L);
            strncpy(pIp->state, entry_data.utf8_string, safe_len);
        }
    }
    status = MMDB_get_value(&result.entry, &entry_data,  "country", "names", "en", NULL);
    if (MMDB_SUCCESS == status && entry_data.has_data) { /* MMDB_get_value 成功 */
        if (entry_data.type == MMDB_DATA_TYPE_UTF8_STRING) {
            // 不是null-terminated 字符串
            size_t safe_len = DPI_MIN(entry_data.data_size, len - 1L);
            strncpy(pIp->country, entry_data.utf8_string, safe_len);
        }
    }
    status = MMDB_get_value(&result.entry, &entry_data,  "continent", "names", "en", NULL);
    if (MMDB_SUCCESS == status && entry_data.has_data) { /* MMDB_get_value 成功 */
        if (entry_data.type == MMDB_DATA_TYPE_UTF8_STRING) {
            // 不是null-terminated 字符串
            size_t safe_len = DPI_MIN(entry_data.data_size, len - 1L);
            strncpy(pIp->area, entry_data.utf8_string, safe_len);
        }
    }
    status = MMDB_get_value(&result.entry, &entry_data,  "autonomous_system_number", NULL);
    if (MMDB_SUCCESS == status && entry_data.has_data) { /* MMDB_get_value 成功 */
        if (entry_data.type == MMDB_DATA_TYPE_UTF8_STRING) {
            // 不是null-terminated 字符串
            size_t safe_len = DPI_MIN(entry_data.data_size, len - 1L);
            strncpy(pIp->asn, entry_data.utf8_string, safe_len);
        }
    }
    status = MMDB_get_value(&result.entry, &entry_data,  "location", "latitude", NULL);
    if (MMDB_SUCCESS == status && entry_data.has_data) { /* MMDB_get_value 成功 */
        if (entry_data.type == MMDB_DATA_TYPE_DOUBLE) {
            // 不是null-terminated 字符串
            snprintf(pIp->latitude,sizeof(pIp->latitude), "%.2f", entry_data.double_value);

        }
    }
    status = MMDB_get_value(&result.entry, &entry_data,  "location", "longitude", NULL);
    if (MMDB_SUCCESS == status && entry_data.has_data) { /* MMDB_get_value 成功 */
        if (entry_data.type == MMDB_DATA_TYPE_DOUBLE) {
            // 不是null-terminated 字符串
            snprintf(pIp->longitude,sizeof(pIp->longitude), "%.2f", entry_data.double_value);
        }
    }
    status = MMDB_get_value(&result.entry, &entry_data, "autonomous_system_organization", NULL);
    if (MMDB_SUCCESS == status && entry_data.has_data) { /* MMDB_get_value 成功 */
        if (entry_data.type == MMDB_DATA_TYPE_UTF8_STRING) {
            // 不是null-terminated 字符串
            size_t safe_len = DPI_MIN(entry_data.data_size, len - 1L);
            strncpy(pIp->isp, entry_data.utf8_string, safe_len);
        }
    }

    return;
}

static int is_private_ip(uint8_t *ipv4)
{
    uint32_t ip4 = get_uint32_ntohl(ipv4, 0);
    if(   ((ip4 & 0xff000000) == 0x0a000000)
       || ((ip4 & 0xfff00000) == 0xac100000)
       || ((ip4 & 0xffff0000) == 0xc0a80000))
    {
        return 1;
    }

    return 0;
}

void dissect_ip2region(ip2region_t st, uint8_t *ip4, IPINFO* IP)
{
    datablock_entry entry;
    char* s[6]; //国家 地区 省 市县 运营商
    if(ip2region_memory_search(st, get_uint32_ntohl(ip4, 0), &entry)){
        if(entry.region[0] == '0'){
            strcpy(IP->country, "中国");
            return;
        }
        int i, j=0;
        s[j++] = entry.region;
        for(i=0; entry.region[i]; ++i){
            if(entry.region[i] == '|'){
                entry.region[i] = '\0';
                s[j++] = entry.region + i + 1;
            }
        }
        if(j<4) return;
        strcpy(IP->country, s[0]);
        strcpy(IP->area,    s[1]);
        strcpy(IP->state,   s[2]);
        strcpy(IP->city,    s[3]);
        strcpy(IP->isp,     s[4]);

    }

    char __str[64]={0};
    get_iparray_to_string(__str, sizeof(__str), ip4);
    get_ip_asn_info(&g_config.mmdb_asn,(const char *)__str, IP->asn, sizeof(IP->asn));

    get_ip_latitude_longitude_info(&g_config.mmdb_city,(const char *)__str, "latitude",IP->latitude, sizeof(IP->latitude));
    get_ip_latitude_longitude_info(&g_config.mmdb_city,(const char *)__str, "longitude",IP->longitude, sizeof(IP->longitude));
}

int get_ip_asn_info(MMDB_s *const mmdb,const char *ip_address,char *buff, int len)
{
    if(0==g_config.mmdb_asn_switch){
        return -1;
    }

    int status=0;
    int gai_error, mmdb_error;
    MMDB_entry_data_s entry_data;
    MMDB_lookup_result_s result =
        MMDB_lookup_string(mmdb, ip_address, &gai_error, &mmdb_error);

    if (0 != gai_error) {
        return -1;
    }

    if (MMDB_SUCCESS != mmdb_error) {
        return -1;
    }

    if (result.found_entry) {
        /* 获取国家名称(简体中文) */
        status = MMDB_get_value(&result.entry, &entry_data, "autonomous_system_number", NULL);
        if (MMDB_SUCCESS == status) {/* MMDB_get_value 成功 */
            if (entry_data.has_data) {/* 找到了想要的数据 */
                if (entry_data.type == MMDB_DATA_TYPE_UINT32) {
                    snprintf(buff,len,"%u",entry_data.uint32);
                }
            }
        }
    }
    return 0;
}

int get_ip_latitude_longitude_info(MMDB_s *const mmdb,const char *ip_address,const char *itude, char *buff, int len)
{
    int status=0;
    int gai_error, mmdb_error;
    MMDB_entry_data_s entry_data;
    MMDB_lookup_result_s result =
        MMDB_lookup_string(mmdb, ip_address, &gai_error, &mmdb_error);

    if (0 != gai_error) {
        return -1;
    }

    if (MMDB_SUCCESS != mmdb_error) {
        return -1;
    }

    if (result.found_entry) {
        /* 获取国家名称(简体中文) */
        status = MMDB_get_value(&result.entry, &entry_data, "location", itude, NULL);
        if (MMDB_SUCCESS == status) {/* MMDB_get_value 成功 */
            if (entry_data.has_data) {/* 找到了想要的数据 */
                if (entry_data.type == MMDB_DATA_TYPE_DOUBLE) {
                    snprintf(buff,len,"%f",entry_data.double_value);
                }
            }
        }
    }
    return 0;
}

void get_ip_position_from_ip(const char* ip_address, IPINFO* pIp)
{
    if(!ip_address || !pIp)
        return;

    memset(pIp, 0, sizeof(IPINFO));
    uint32_t ip = dotted_to_addr(ip_address);
    if(g_config.ip_position_switch == 1){
        dissect_ip2region(&g_config.entry, (uint8_t*)&ip, pIp);
    }else if(is_private_ip((uint8_t*)&ip)){
        strcpy(pIp->area,    "Asia-");
        strcpy(pIp->country, "China");
    }else if(g_config.mmdb_switch){
            get_ip_position_by_mmdb(ip_address, pIp);
            get_asn_from_ip(ip_address,pIp->asn,64);
            get_isp_from_ip(ip_address,pIp->isp,64);
    }

    ip_position_check(pIp);

    return ;
}

void dissect_ip_position(struct flow_info *flow, int direction, IPINFO *srcIp, IPINFO *dstIp)
{
    char src_str[64];
    char dst_str[64];

    memset(srcIp, 0, sizeof(IPINFO));
    memset(dstIp, 0, sizeof(IPINFO));
    uint8_t *dst_ip = NULL;
    uint8_t *src_ip = NULL;
    if (direction == FLOW_DIR_SRC2DST)
    {
    src_ip = flow->tuple.inner.ip_src;
    dst_ip = flow->tuple.inner.ip_dst;
    }else {
    dst_ip = flow->tuple.inner.ip_src;
    src_ip = flow->tuple.inner.ip_dst;
    }

    if(flow->ip_version == 4)
    {
      get_iparray_to_string(src_str, sizeof(src_str), src_ip);
      get_iparray_to_string(dst_str, sizeof(dst_str), dst_ip);
    }else if (flow->ip_version == 6) {
      get_ip6string(src_str, sizeof(src_str), src_ip);
      get_ip6string(dst_str, sizeof(dst_str), dst_ip);
    }
    get_ip_position_from_ip(src_str, srcIp);
    get_ip_position_from_ip(dst_str, dstIp);

    if(0 == strlen(srcIp->country) ||srcIp->country[0] == 0){
        strcpy(srcIp->country, g_config.ip_position_switch == 1 ? "中国" : "China");
    }
    if(0 == strlen(srcIp->country) ||dstIp->country[0] == 0){
        strcpy(dstIp->country, g_config.ip_position_switch == 1 ? "中国" : "China");
    }

}

int get_filename(char *path_name, char *name)
{
    if(path_name==NULL || name==NULL || strlen(path_name)<=0 ){
        return 0;
    }
    int i=0,k=0;
    for(i=strlen(path_name);i>=0;i--){
        if(path_name[i]!='/'){
            k++;
        }
        else
            break;
    }
    strcpy(name,path_name+(strlen(path_name)-k)+1);
    return 1;
}

void free_buf(char* p)
{
    if(NULL != p)
    {
        free(p);
        p = NULL;
    }
}

//获取随机数+微秒级时间戳
char* getRandByMicrosecond(char* ouput, int size, int len)
{
    if(NULL == ouput || len >= size-20) return NULL;
    struct timeval m_time;
    //获取微秒级时间戳
    gettimeofday(&m_time, NULL);
    srand(m_time.tv_usec);
    long long timeuse = 1000000 * m_time.tv_sec + m_time.tv_usec;
    int flag, i;
    for (i = 0; i < len; i++)
    {
        flag = rand() % 3;
        switch (flag)
        {
            case 0:
                ouput[i] = 'A' + rand() % 26;
                break;
            case 1:
                ouput[i] = 'a' + rand() % 26;
                break;
            case 2:
                ouput[i] = '0' + rand() % 10;
                break;
            default:
                ouput[i] = 'x';
                break;
        }
    }
    char buf[20] = {0};
    snprintf(buf,20,"%lld",timeuse);
    strncat(ouput,buf,20);
    return ouput;
}

/*
* 说明:对应配置客户字段和自有字段的映射json，初始化前调用函数
* @field_hash:自有字段hash表，字段与index；
* @proto_json:客户字段与自有字段映射json表
* @reflect_array:用于存储json表顺序对应自有字段所在索引位置；
* @proto_name: 协议名，用于写字段表时的， 字段表名；
*/
int init_protocol_reflect_fields(void   *field_hash,
                                char **reflect_protoname,
                                int **reflect_array,
                                int *reflect_array_num,
                                const char *proto_name,
                                enum PROTOCOL_TYPE protocol_id)
{
    if(NULL==field_hash|| NULL==proto_name || strlen(proto_name)<=0){
        return -1;
    }


    char file_path[COMMON_FILE_PATH] = {0};
    snprintf(file_path,COMMON_FILE_PATH,"%s/%s.json", g_config.dpi_field_json_dir,proto_name);
    if(access(file_path, F_OK))
    {
        return -1;
    }

    FILE *fp=fopen(file_path,"rb");
    if(!fp)
    {
        printf("init_protocol_reflect_fields 文件打开错误[%s]\n", file_path);
        return -1;
    }

    fseek(fp, 0, SEEK_END);
    int len=ftell(fp);

    char *buff=(char *)malloc(len+1);
    if(NULL==buff)
    {
        printf("malloc failed!\n");
        return -1;
    }

    fseek(fp, 0, SEEK_SET );
    fread(buff, 1, len, fp);
    buff[len] = 0;  //末尾置 0
    fclose(fp);

    cJSON *root = cJSON_Parse(buff);
    if(!root){
        printf(" get root failed!\n%s\n",buff);
        exit(-1);
    }

    cJSON *to_proto_name = cJSON_GetObjectItem(root, "to_proto_name");
    if (to_proto_name && cJSON_IsString(to_proto_name)) {
        *reflect_protoname = (char*)alloc_memdup(get_global_memAc(),
                                                to_proto_name->valuestring,
                                                strlen(to_proto_name->valuestring));
    }

    cJSON *js_field = cJSON_GetObjectItem(root, "field");
    if(!js_field){
        printf("not exist field\n");
        exit(-1);
    }

    int size    = cJSON_GetArraySize(js_field);
    *reflect_array = (int *)malloc(sizeof(int)*size);
    if(NULL == *reflect_array){
        exit(-1);
    }
    *reflect_array_num=size;

    int    i            = 0;
    int    count        = 0;
    cJSON *item         = NULL;
    memset(file_path, 0, COMMON_FILE_PATH);
    snprintf(file_path,COMMON_FILE_PATH,"%s/%s_json.txt",g_config.dpi_field_dir,proto_name);
    FILE *fd_fp=fopen(file_path,"w+");
    if(!fd_fp){
        printf("opened failed!\n");
        return -1;
    }

    /* common fields */
    for(i=0;i<(int)manager_map_fields[PROTOCOL_SHARE_HEADER].protocol_field_array_num;i++){
        fwrite(manager_map_fields[PROTOCOL_SHARE_HEADER].protocol_field_array[i].field_name,
               strlen(manager_map_fields[PROTOCOL_SHARE_HEADER].protocol_field_array[i].field_name),
               1,fd_fp);
        fwrite("\n",1,1,fd_fp);
    }

    /* link fields */
    for(i=0;i<(int)manager_map_fields[PROTOCOL_LINK].protocol_field_array_num;i++){
        fwrite(manager_map_fields[PROTOCOL_LINK].protocol_field_array[i].field_name,
               strlen(manager_map_fields[PROTOCOL_LINK].protocol_field_array[i].field_name),
               1,fd_fp);
        fwrite("\n",1,1,fd_fp);
    }

    uint16_t json_pos = 0;
    for(i = 0; i < size; i++){
        //取到某一行
        item=cJSON_GetArrayItem(js_field, i);

        //展开这一行
        const cJSON *item_pos   = item->child;
        const cJSON *item_yv    = NULL;
        const cJSON *item_json  = NULL;
        while(item_pos)
        {
            if(cJSON_String == item_pos->type && 0 == strcmp(item_pos->string, "from_name"))
            {
                item_yv = item_pos;
            }
            else
            if(cJSON_String == item_pos->type && 0 == strcmp(item_pos->string, "to_name"))
            {
                item_json = item_pos;
            }
            item_pos = item_pos->next;
        }

        if(NULL == item_yv || NULL == item_json)
        {
            continue;
        }

        int *index=NULL;
        gpointer result;
        result = g_hash_table_lookup((GHashTable *)field_hash, (gpointer)item_yv->valuestring);
        if(result ){
            (*reflect_array)[i]=*(int *)result;
        }else{
            (*reflect_array)[i]=-1;
        }

        fwrite(item_json->valuestring,strlen(item_json->valuestring),1,fd_fp);
        fwrite("\n",1,1,fd_fp);

        // 存储各个协议的字段表
        uint16_t field_cnt = g_json_field[protocol_id].field_cnt;
        snprintf(g_json_field[protocol_id].field[field_cnt], 32, "%s", item_json->valuestring);
        g_json_field[protocol_id].field_cnt++;
    }

    if(fd_fp){
        fclose(fd_fp);
    }
    if(root){
        cJSON_Delete(root);
    }
    if(buff){
        free(buff);
    }
    return 0;
}

int init_reflect_protoname_for_lua(const char *file_path)
{
    if(access(file_path, F_OK))
    {
        return -1;
    }

    char *from_proto = NULL;
    int from_proto_len = 0;
    char *to_proto = NULL;
    int to_proto_len = 0;
    FILE *fp=fopen(file_path,"rb");
    if(!fp)
    {
        printf("init_reflect_protoname_for_lua 文件打开错误[%s]\n", file_path);
        return -1;
    }

    fseek(fp, 0, SEEK_END);
    int len=ftell(fp);

    char *buff=(char *)malloc(len+1);
    if(NULL==buff)
    {
        printf("malloc failed!\n");
        return -1;
    }

    fseek(fp, 0, SEEK_SET );
    fread(buff, 1, len, fp);
    buff[len] = 0;  //末尾置 0
    fclose(fp);
    get_data_key_value(buff, len, "from_proto_name = \"", "\",", &from_proto, &from_proto_len);
    get_data_key_value(buff, len, "to_proto_name = \"", "\",", &to_proto, &to_proto_len);
    if(from_proto && to_proto){
       for(int i=1; i < TBL_LOG_MAX; i++) {
           if(tbl_log_array[i].protoname && memcmp(tbl_log_array[i].protoname, from_proto, strlen(tbl_log_array[i].protoname) > (uint32_t)from_proto_len?(uint32_t)from_proto_len:strlen(tbl_log_array[i].protoname)) == 0){
               if(i > TBL_LOG_FTP_CONTROL) 
                   i++;
               manager_map_fields[i].protocol_reflect_protoname = (char*)alloc_memdup(get_global_memAc(), to_proto, to_proto_len);
               break;
           }
       }
    }
    if(buff){
        free(buff);
    }
    return 0;
}

static void destroy_key(gpointer hash_key)
{
    printf("manager_map_fields destroy key:%s\n",(char *)hash_key);
}

static void destroy_value(gpointer hash_value)
{
    printf("manager_map_fields destroy value:%d\n", *(int *)hash_value);
}

int map_fields_info_register(dpi_field_table *protocol_array,
                                enum PROTOCOL_TYPE protocol_id,
                                int max_fields_num,
                                const char *protocol_name)
{
    int i;
    int ret=0;

    if(!protocol_array || !protocol_name || protocol_id>=PROTOCOL_MAX){
        return -1;
    }


    manager_map_fields[protocol_id].protocol_field_array_num=max_fields_num;
    manager_map_fields[protocol_id].protocol_field_array=protocol_array;
    manager_map_fields[protocol_id].protocol_hash = g_hash_table_new_full(g_str_hash, g_str_equal, destroy_key, destroy_value);
    if (NULL == manager_map_fields[protocol_id].protocol_hash){
        printf("create %s field hash table failed error!\n",protocol_name);
        return -1;
    }

    GHashTable *hash_table = manager_map_fields[protocol_id].protocol_hash;
    for( i=0; i<max_fields_num; i++)
    {
        dpi_field_table *field = &(manager_map_fields[protocol_id].protocol_field_array[i]);
        g_hash_table_insert(hash_table, (gpointer)field->field_name, (gpointer)&(field->index));
        //printf("protocol_name[%s] field_name[%s]\n", protocol_name, field->field_name);
    }

    ret=sdtAppProtoDict_Register(manager_map_fields[protocol_id].protocol_field_array,
                                 manager_map_fields[protocol_id].protocol_hash,
                                 protocol_id, max_fields_num, protocol_name);
    if(ret==0){
        printf("telnet register fileds dict failed!\n");
        exit(-1);
    }


    init_protocol_reflect_fields(manager_map_fields[protocol_id].protocol_hash,
                                 &manager_map_fields[protocol_id].protocol_reflect_protoname,
                                 &manager_map_fields[protocol_id].protocol_reflect_array,
                                 &manager_map_fields[protocol_id].protocol_reflect_array_num,
                                 protocol_name,
                                 protocol_id);

    return 0;
}

int map_fields_get_raw_num(enum PROTOCOL_TYPE protocol_id)
{
    return manager_map_fields[protocol_id].protocol_field_array_num;

}

int map_fields_get_num(enum PROTOCOL_TYPE protocol_id)
{
    return manager_map_fields[protocol_id].protocol_reflect_array_num;

}

int *map_fields_get_array(enum PROTOCOL_TYPE protocol_id)
{
    return manager_map_fields[protocol_id].protocol_reflect_array;

}

void *map_fields_get_hash_tab(enum PROTOCOL_TYPE protocol_id)
{
    return (void *)manager_map_fields[protocol_id].protocol_hash;

}

void get_asn_from_ip(const char *ip, char *asn, int len) {
    memset(asn, 0, len);
    if (g_config.mmdb_asn_switch == 0 || !ip)
        return;

    MMDB_entry_data_s entry_data;
    MMDB_lookup_result_s result;
    int gai_error, mmdb_error;

    result = MMDB_lookup_string(&g_config.mmdb_asn, ip, &gai_error, &mmdb_error);
    if (0 != gai_error) {
        return;
    }

    if (MMDB_SUCCESS != mmdb_error) {
        return;
    }

    /* ASN DB INFO Example
    {
       "autonomous_system_number":
         4808 <uint32>
       "autonomous_system_organization":
         "China Unicom Beijing Province Network" <utf8_string>
     }
    */
    if (result.found_entry) {
        /* 获取ASN */
        int status = MMDB_get_value(&result.entry, &entry_data, "autonomous_system_number", NULL);

        if (MMDB_SUCCESS == status && entry_data.has_data) {/* MMDB_get_value 成功 */
            if (entry_data.type == MMDB_DATA_TYPE_UINT32) {
                // 不是null-terminated 字符串
                sprintf(asn, "%u", entry_data.uint32);
            }
        }
    }
}

static inline void _free_key(gpointer data)
{
    free(data);
}

static inline void _free_key_value(gpointer data)
{
    struct tbl_log_file_node *_value = (struct tbl_log_file_node *)data;

    if (_value->fp_tbl){
        fclose(_value->fp_tbl);
        _value->fp_tbl = NULL;
    }
    dpi_free(data);
}

int create_tbl_log_file_hash_table(void)
{
    tbl_log_file_table = g_hash_table_new_full(g_str_hash, g_str_equal, _free_key, _free_key_value);
    if (tbl_log_file_table == NULL) {
        DPI_LOG(DPI_LOG_DEBUG, "ghash table create error");
        return -1;
    }
    return 0;
}

static gboolean _close_tbl_file_unsafe(gpointer key, gpointer value, gpointer user_data)
{
    struct close_tbl_file *cache_data = (struct close_tbl_file *)user_data;
    char *_key = (char *)key;
    struct tbl_log_file_node *_value = (struct tbl_log_file_node *)value;
    char filename[256];
    char filename_rename[256];
    int timeout = 0;

    if(_value->timeout_sec + g_config.web_config.json_truncation < g_config.g_now_time)
        timeout = 1;
    if(cache_data->close_flag) //程序关闭时关闭负责的所有fd
        timeout = 2;
    snprintf(filename, sizeof(filename), "%s.%s.tmp", _value->filename, g_config.yv_data_suffix);

    struct stat st;
    /* 有文件，超时或超数 */
    if (_value->fp_tbl && (stat(filename, &st) == 0) && (timeout || st.st_size >= g_config.web_config.json_size)) {
        _value->log_num = 0;
        fclose(_value->fp_tbl);
        _value->fp_tbl = NULL;

        /* 为空则删除,否则改名 */
        if(stat(filename, &st) == 0 && st.st_size == 0){
            remove(filename);
        } else{
            snprintf(filename_rename, sizeof(filename_rename), "%s.%s", _value->filename, g_config.yv_data_suffix);
            rename(filename, filename_rename);
        }

        return TRUE;
    }

    return FALSE;
}

#include <pthread.h>
static pthread_mutex_t mutex_g_hash = PTHREAD_MUTEX_INITIALIZER;
static gboolean _close_tbl_file(gpointer key, gpointer value, gpointer user_data)
{
    // pthread_mutex_lock(&mutex_g_hash);
    gboolean ret = _close_tbl_file_unsafe(key, value, user_data);
    // pthread_mutex_unlock(&mutex_g_hash);
    return ret;
}

int dpi_register_proto_schema_ex(dpi_field_table *proto_array, int max_len, const char *proto_name, char *common_field_schema)
{
    if (NULL == proto_array || NULL == proto_name)
    {
        return -1;
    }

    return dpi_pschema_register_proto_field(proto_name, proto_array, max_len, common_field_schema);
}

int dpi_register_proto_schema(dpi_field_table *proto_array, int max_len, const char *proto_name)
{
    if (NULL == proto_array || NULL == proto_name)
    {
        return -1;
    }

    return dpi_register_proto_schema_ex(proto_array, max_len, proto_name, NULL);
}

int dpi_register_proto(const char * proto_name, const char * proto_full_name, char * common_field_schema)
{
    if (NULL == proto_name) {
        return -1;
    }

    return dpi_pschema_register_proto(proto_name, proto_full_name, common_field_schema);
}

int dpi_pschema_get_common_field_trailer(dpi_field_table *field_table_array[])
{
    *field_table_array = dpi_common_field_trailer;
    return ARRAY_LEN(dpi_common_field_trailer);
}

precord_t* sdt_precord_new_record(const char *proto_name)
{
    ATOMIC_FETCH_ADD(&record_new_cnt);
    precord_t *p = dpi_pschema_new_record(proto_name);
    return p;
}

precord_t *sdt_precord_clone(precord_t *record)
{
    precord_t *clone = precord_clone(record);
    ATOMIC_FETCH_ADD(&record_new_cnt);
    return clone;
}

void sdt_precord_destroy(precord_t *record)
{
    if(NULL == record)
    {
        return;
    }

    ATOMIC_FETCH_ADD(&record_destroy_cnt);
    //printf("计数器 new %zu, free %zu\n", record_new_cnt, record_destroy_cnt);
    precord_destroy(record);
}

const char *dpi_precord_get_proto_name(precord_t *record)
{
    return precord_get_proto_name(record);
}

void record_show(precord_t *record)
{
    for(player_t *player = precord_layer_get_first(record); NULL != player; player = precord_layer_get_next(record, player))
    {
        const char *layer_name = precord_layer_get_layer_name(player);
        for (pfield_t *field = precord_field_get_first_from_layer_of(player); field != NULL; field = precord_field_get_next_from_layer_of(player, field))
        {
            pfield_desc_t *fdesc  = precord_field_get_fdesc(field);
            ya_fvalue_t   *fvalue = precord_field_get_fvalue(field);
            const char *field_name = pfdesc_get_name(fdesc);
            char *value_string = fvalue ? ya_fvalue_to_string_repr(fvalue, BASE_NONE) : "";
            printf("[%s][%s][%s]\n", layer_name, field_name, value_string);
            fvalue?ya_fvalue_free_string_repr(value_string):42;
        }
    }
    printf("\n");
}

void del_not_match_tbl_file(precord_t *record, uint16_t proto_id)
{
    player_t *layer = NULL;
    if (proto_id == PROTOCOL_HTTP) {
        layer = precord_layer_get_by_name(record, "http");
    } else if (proto_id == PROTOCOL_FTP_CONTROL || proto_id == PROTOCOL_FTP_DATA) {
        layer = precord_layer_get_by_name(record, "ftp");
    } else if (proto_id == PROTOCOL_TELNET) {
        layer = precord_layer_get_by_name(record, "telnet");
    } else if (proto_id == PROTOCOL_MAIL_ESMTP || proto_id == PROTOCOL_MAIL_POP || proto_id == PROTOCOL_MAIL_IMAP ||
               proto_id == PROTOCOL_MAIL_SMTP) {
        layer = precord_layer_get_by_name(record, "email");
    } else {
        return;
    }

    if (!layer)
        return;

    ya_fvalue_t   *fvalue = precord_fvalue_get_from_layer_of(layer,"localFilepath");
    char *value_string = fvalue ? ya_fvalue_to_string_repr(fvalue, BASE_NONE) : "";
    if(strlen(value_string)>0)
        remove(value_string);

    return;
}
