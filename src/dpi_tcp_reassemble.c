/****************************************************************************************
 * 文 件 名 : dpi_tcp_reassemble.c
 * 项目名称 : 
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/07/06
编码: wangy            2018/07/06
修改: 
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#include <stdlib.h>
#include <string.h>
#include <rte_mempool.h>
#include <rte_mbuf.h>
#include "list.h"
#include "dpi_tcp_reassemble.h"
#include "dpi_log.h"
#include "libsdt/sdt_types.h"
#include "sdt_action_out.h"

#include "dpi_dpdk_wrapper.h"

extern int cloneIndex, freeIndex;
extern rte_atomic64_t rsm_fail_get;


#define SEQ_EQ(a,b)  ((int32_t)((a) - (b)) == 0)
#define SEQ_LT(a,b)  ((int32_t)((a) - (b)) <  0)
#define SEQ_LEQ(a,b) ((int32_t)((a) - (b)) <= 0)
#define SEQ_GT(a,b)  ((int32_t)((a) - (b)) >  0)
#define SEQ_GEQ(a,b) ((int32_t)((a) - (b)) >= 0)




/*
 *  flag   c2s  0, s2c 1
*/
int tcp_reassemble_add_double_item(struct list_head *head, uint32_t *rsm_total_len,struct tcp_reassemble_args tcp_args,const uint8_t *payload)
{
    uint8_t f_add;
    uint16_t i;
    uint16_t rsm_num;
    uint16_t rsm_len;
    struct tcp_reassemble *pos;
    struct tcp_reassemble *new_item;

    uint32_t payload_len=tcp_args.payload_len;
    //const uint8_t  *payload=tcp_args.payload;
    uint32_t seq=tcp_args.seq;
    uint32_t ack=tcp_args.ack;
    
    if (payload_len == 0)
        return 0;
    
    rsm_num = (payload_len - 1) / TCP_PAYLOAD_MAX_LEN + 1;
    for (i = 0; i < rsm_num; i++) {
        f_add = 0;
        if (i < rsm_num - 1)
            rsm_len = TCP_PAYLOAD_MAX_LEN;
        else
            rsm_len = payload_len - (rsm_num - 1) * TCP_PAYLOAD_MAX_LEN;
        
        new_item = malloc(sizeof(struct tcp_reassemble));
        memset(new_item, 0, sizeof(struct tcp_reassemble));

        new_item->ack=tcp_args.ack;
        new_item->flag=tcp_args.flag;
        new_item->seq = seq + i * TCP_PAYLOAD_MAX_LEN;
        new_item->payload_len = rsm_len;
        memcpy(new_item->payload, payload + i * TCP_PAYLOAD_MAX_LEN, new_item->payload_len);
        INIT_LIST_HEAD(&new_item->node);
        list_for_each_entry(pos, head, node) {
            if(pos->flag==new_item->flag){
                if (pos->seq == new_item->seq) {
                    f_add = 1;
                    free(new_item);
                    break;
                } else if (pos->seq < new_item->seq) {
                    f_add = 1;
                    list_add(&new_item->node, pos->node.prev);
                    *rsm_total_len += payload_len;
                    break;
                }
            }else{
                if (pos->ack <= new_item->seq) {
                    f_add = 1;
                    list_add(&new_item->node, pos->node.prev);
                    *rsm_total_len += payload_len;
                    break;
                }
            }
        }
        
        if (f_add == 0) {
            list_add(&new_item->node, pos->node.prev);
            *rsm_total_len += payload_len;
        }
    }
    
    return 0;
}

int tcp_reassemble_add_item(struct list_head *head, uint32_t *rsm_total_len, uint32_t seq, const uint8_t *payload, const uint16_t payload_len)
{
    uint8_t f_add;
    uint16_t i;
    uint16_t rsm_num;
    uint16_t rsm_len;
    struct tcp_reassemble *pos;
    struct tcp_reassemble *new_item;
    uint32_t pre_seq=0;

    if (payload_len == 0)
        return 0;
    
    rsm_num = (payload_len - 1) / TCP_PAYLOAD_MAX_LEN + 1;

    for (i = 0; i < rsm_num; i++) {
        f_add = 0;
        if (i < rsm_num - 1)
            rsm_len = TCP_PAYLOAD_MAX_LEN;
        else
            rsm_len = payload_len - (rsm_num - 1) * TCP_PAYLOAD_MAX_LEN;
        
        new_item = malloc(sizeof(struct tcp_reassemble));
        memset(new_item, 0, sizeof(struct tcp_reassemble));
        new_item->seq = seq + i * TCP_PAYLOAD_MAX_LEN;
        new_item->payload_len = rsm_len;
        memcpy(new_item->payload, payload + i * TCP_PAYLOAD_MAX_LEN, new_item->payload_len);
        INIT_LIST_HEAD(&new_item->node);

        /*
         * 重传，重叠：左重叠，右重叠，目前左重叠还未处理
        */
        list_for_each_entry(pos, head, node) {
            /* 丢掉完全重复报文和重复子报文 */
            if (pos->seq == new_item->seq && pos->payload_len >= new_item->payload_len) {
                //printf("[1]--完全重复报文或子报文\n");
                f_add = 1;
                free(new_item);
                break;
            } 
            /* seq相同，但是长度比原队列长 */
            else if(pos->seq == new_item->seq && pos->payload_len < new_item->payload_len) {
                f_add = 1;
                if((pre_seq==0 )|| (pre_seq!=0 && pre_seq>=new_item->seq+new_item->payload_len)){
                    pos->payload_len=new_item->payload_len;
                    memcpy(pos->payload,new_item->payload,new_item->payload_len);
                }
                free(new_item);
                break;
            }
            /* 丢掉部分重复报文，且该报文属于pos的一部分*/
            else if(new_item->seq > pos->seq && new_item->seq < pos->seq+pos->payload_len
                     && new_item->seq+new_item->payload_len <= pos->seq+pos->payload_len ){
                f_add = 1;
                free(new_item);
                break;
            }
            /* 该报文seq>pos, 长度在报文之外*/ 
            else if(new_item->seq > pos->seq && new_item->seq<pos->seq+pos->payload_len
                     && new_item->seq+new_item->payload_len > pos->seq+pos->payload_len){
                f_add = 1;
                uint32_t repeat_len=pos->seq+pos->payload_len-new_item->seq;
                new_item->seq+=repeat_len;
                new_item->payload_len-=repeat_len;
               if( (pre_seq==0) || (pre_seq!=0 && pre_seq >= new_item->seq+new_item->payload_len)){
                    memmove(new_item->payload, (char*)&new_item->payload+repeat_len, new_item->payload_len);
                    list_add(&new_item->node, pos->node.prev);
                    *rsm_total_len +=  new_item->payload_len;
                }else{
                    free(new_item);
                }
                break;
            } else if (new_item->seq > pos->seq) {
                if (pre_seq !=0 && new_item->seq+new_item->payload_len > pre_seq){ // 尾踩头，去掉重复部分再添加
                    uint32_t repeat_len=new_item->seq+new_item->payload_len-pre_seq;
                    new_item->payload_len-=repeat_len;
                    memmove(new_item->payload, (char*)&new_item->payload, new_item->payload_len);
                }
                f_add = 1;
                list_add(&new_item->node, pos->node.prev);
                *rsm_total_len += payload_len;
                break;
            }

            pre_seq = pos->seq;
        }
        if (f_add == 0) {
            list_add(&new_item->node, pos->node.prev);
            *rsm_total_len += payload_len;
        }
    }
    
    return 0;
}

int tcp_reassemble_do(struct list_head *head, uint8_t *result, uint32_t *result_len)
{
    struct tcp_reassemble *pos;

    uint32_t remainlen = *result_len;
    uint32_t index = 0;
        
    list_for_each_entry_reverse(pos, head, node) {
        if (remainlen >= pos->payload_len) {
            memcpy(result + index, pos->payload, pos->payload_len);
            index += pos->payload_len;
            remainlen -= pos->payload_len;
        } else 
            break;
    }

    *result_len = index;
    return 0;
}

int tcp_reassemble_do_guesslen(struct list_head *head, uint32_t *result_len, uint32_t *expect_len)
{
    struct tcp_reassemble *pos;
    
    //uint32_t remainlen = *result_len;
    uint32_t index = 0;

    uint32_t pkt_count=0;
    uint32_t total_len=0,first_seq=0;
    list_for_each_entry_reverse(pos, head, node){
        if(pkt_count==0){
            first_seq=pos->seq;
            pkt_count=1;
        }
        index += pos->payload_len;

        total_len = pos->seq-first_seq+pos->payload_len;

    } 
    
    *result_len = index;
    *expect_len = total_len;
    
    return 0;
}


int tcp_reassemble_do_new(struct list_head *head, uint8_t *result, uint32_t *result_len, uint32_t *expect_len)
{
    struct tcp_reassemble *pos;
    
    uint32_t remainlen = *result_len;
    uint32_t index = 0;

    uint32_t pkt_count=0;
    uint32_t total_len=0,first_seq=0;
    list_for_each_entry_reverse(pos, head, node){
        if (remainlen >= pos->payload_len) {
            if(pkt_count==0){
                first_seq=pos->seq;
            }
            pkt_count=1;
            total_len=pos->seq-first_seq+pos->payload_len;

            
            memcpy(result + index, pos->payload, pos->payload_len);
            index += pos->payload_len;
            remainlen -= pos->payload_len;
        } else 
            break;
    } 
    *result_len = index;
    *expect_len = total_len;
    return 0;

}


int tcp_reassemble_do_padding(struct list_head *head, uint8_t *result, uint32_t *result_len, uint32_t expect_len)
{
    struct tcp_reassemble *pos;
    
    uint32_t remainlen = expect_len;
    uint32_t index = 0;

    uint32_t pkt_count=0;
    uint32_t total_len=0,pre_seq=0,pre_payload_len=0;
    uint32_t drop_len=0;
    uint8_t  buff[2000]={0};
    memset(buff, 0x0c, 2000);

    
    list_for_each_entry_reverse(pos, head, node){
        if(pkt_count==0){
            pre_seq=pos->seq;
            pre_payload_len=pos->payload_len;
        
            if(index+pos->payload_len>=expect_len){break;}
            memcpy(result + index, pos->payload, pos->payload_len);
            index+=pos->payload_len;
            
            remainlen-=pos->payload_len;
            pkt_count=1;
            continue;
        }
        if(pos->seq<(pre_seq+pre_payload_len)){
            continue;
        }
        drop_len=(int)(pos->seq-pre_seq-pre_payload_len);
        if(drop_len>0 && drop_len< expect_len){
            while(drop_len>0){
                if(drop_len>TCP_PADDING_ELEMEMT_LEN){
                    if(index+TCP_PADDING_ELEMEMT_LEN>=expect_len){break;}
                    memcpy(result + index, buff, TCP_PADDING_ELEMEMT_LEN);
                    index+=TCP_PADDING_ELEMEMT_LEN;
                    
                    drop_len-=TCP_PADDING_ELEMEMT_LEN;
                    remainlen-=TCP_PADDING_ELEMEMT_LEN;
                }else{
                    if(index+drop_len>=expect_len){break;}
                    memcpy(result + index, buff, drop_len);
                    index+=drop_len;
                    
                    drop_len-=drop_len;
                    remainlen-=drop_len;
                }
            }
        }
        
        if(index+pos->payload_len>expect_len){break;}
        memcpy(result + index, pos->payload, pos->payload_len);
        index += pos->payload_len;
        
        pre_seq=pos->seq;
        pre_payload_len=pos->payload_len;
        
        remainlen-=pos->payload_len;
    } 
    *result_len=index;
    

    return 0;
}




int tcp_reassemble_free(struct list_head *head, uint32_t *rsm_total_len)
{
    struct tcp_reassemble *pos;
    struct tcp_reassemble *n;

    if (head == NULL)
        return 0;
    
    list_for_each_entry_safe(pos, n, head, node) {
        list_del(&pos->node);
        free(pos);
    }
    *rsm_total_len = 0;

    return 0;
}

int pkt_single_stream_add_item(struct list_head *head, struct packet_stream * new_item)
{
    if (new_item==NULL)
        return 0;

    INIT_LIST_HEAD(&new_item->snode);

    list_add_tail(&new_item->snode, head);

    return 1;
}


#ifdef DPI_FUTURE_MBUF
int pkt_stream_add_item(struct list_head *head, uint32_t pkt_cnt, ProtoRecord *pRec, struct pkt_info *pkt)
{
    assert(pkt->mbuf);
#else
int pkt_stream_add_item(struct list_head *head, uint32_t pkt_cnt, ProtoRecord *pRec)
{
#endif

    struct packet_stream *new_item = NULL;

    if(pkt_cnt >= g_config.sdt_cache_max_pkts){
        //printf("pkt_stream_add_item 启用环形缓存 pkt_cnt %u>= g_config.sdt_cache_max_pkts %u\n", pkt_cnt, g_config.sdt_cache_max_pkts);
        new_item = (struct packet_stream *)list_entry(head->next, typeof(*new_item), node);
        list_del(&new_item->node);
        pktstream_reset(new_item);
    }
    else
    {
        new_item = pktstream_alloc();
        if (new_item == NULL) {
            //log_error("pktstream_pool 内存池空间不足");
            return -1;
        }
    }

    new_item->l3h_start       = pRec->l3h_start;
    new_item->l4h_start       = pRec->l4h_start;
    new_item->l4payload_start = pRec->l4payload_start;
    new_item->ip_len          = pRec->ip_len;
    new_item->seq             = pRec->seq;
    new_item->direction       = pRec->direction;
#ifdef DPI_FUTURE_MBUF
    new_item->mbuf            = rte_mbuf_clone_0(pkt->mbuf);
    new_item->pkt_data_len    = rte_pktmbuf_data_len(pkt->mbuf);
    new_item->pkt_data        = rte_pktmbuf_mtod(pkt->mbuf, uint8_t*); 
#else
    new_item->pkt_data_len    = pRec->pPayload.len;
    memcpy(new_item->pkt_data,
           pRec->pPayload.pBuff,
           pRec->pPayload.len>TCP_PAYLOAD_MAX_LEN?TCP_PAYLOAD_MAX_LEN:pRec->pPayload.len);
#endif
    list_add_tail(&new_item->node, head);
    return 1;
}

int pkt_stream_free_limit(struct list_head *head, int limit, int* count)
{
    struct packet_stream *pos;
    struct packet_stream *n;

    if (head == NULL || count == NULL)
        return 0;

    list_for_each_entry_safe(pos, n, head, node) {
        if (!(*count > limit)){
            break;
        }
        if(!list_empty(&pos->node)){
            list_del(&pos->node);
            INIT_LIST_HEAD(&pos->node);
        }
        free(pos);
        if (list_empty(head)) {
            break;
        }
        --*count;
    }
    return 0;
}

int pkt_stream_free_node(struct list_head *head)
{
    struct packet_stream *pos;
    struct packet_stream *n;

    if (head == NULL || list_empty(head)) {
        return 0;
    }
    list_for_each_entry_safe(pos, n, head, node) {
        if (list_empty(head)) {
            break;
        }

        if(!list_empty(&pos->node)){
            list_del(&pos->node);
            INIT_LIST_HEAD(&pos->node);
        }

        pktstream_free(pos);
    }

    return 0;
}





