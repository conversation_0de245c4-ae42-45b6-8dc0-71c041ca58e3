#include <netinet/in.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <rte_mempool.h>
#include "dpi_dpdk_wrapper.h"
#include "dpi_log.h"
#include "dpi_proto_ids.h"
#include "dpi_common.h"
#include "sdt_action_out.h"
#include "dpi_sdt_match.h"
#include "dpi_data_input.h"
#include "dpi_pschema.h"
#include "dpi_lua_adapt.h"

#include <rte_hash.h>
#include <yaFtypes/ftypes.h>
#include "sdtapp_interface.h"
#include "libsdt/sdt_types.h"

rule_web_stat  g_static_stat;

extern pthread_mutex_t mutex_g_config;
extern struct global_config g_config;

/*
 * 协议字典类型声明，由 sdt-app 定义
 */
typedef struct ProtoDict
{
    int              proto_num;
    int              actual_num;
    void             *hashArray[PROTOCOL_MAX];
    dpi_field_table   *tblArray[PROTOCOL_MAX];
    ProtoDef         ProtoInfoArray[PROTOCOL_MAX];
}ProtoDict;

ProtoDict globalProtoDict;
static char temp_var[128]; //这种谁想出的大聪明到处都是, 烦啊!

typedef  FieldValue_t* (*pkt_ber_callback)(ProtoRecord *pRec);

typedef struct _pkt_ber_value {
    const char          *str_key;
    pkt_ber_callback    func;
} pkt_ber_value_t;





/****************************************************************************************************
 *                                    protocol fields dict
*****************************************************************************************************/

int sdtAppProtoDict_Register(dpi_field_table *protoTbl,
                                    void *protoHash,
                                    int protoID,
                                    int fieldNum,
                                    const char *name)
{
    if(protoID>PROTOCOL_MAX || NULL==name || strlen(name)==0){
        return 0;
    }

    if(protoTbl==NULL || protoHash==NULL){
        return 0;
    }

    globalProtoDict.tblArray[protoID] = protoTbl;
    globalProtoDict.hashArray[protoID]=protoHash;
    globalProtoDict.ProtoInfoArray[protoID].fieldCnt=fieldNum;
    globalProtoDict.ProtoInfoArray[protoID].protoId=protoID;
    int s = sizeof(globalProtoDict.ProtoInfoArray[protoID].protoName);
    snprintf(globalProtoDict.ProtoInfoArray[protoID].protoName, s, "%s", name);

    switch (protoID) {
    case PROTOCOL_IP:
        globalProtoDict.ProtoInfoArray[protoID].protoLayer = SPL_layer_network;
        break;
    case PROTOCOL_TCP:
    case PROTOCOL_UDP:
        globalProtoDict.ProtoInfoArray[protoID].protoLayer = SPL_layer_transport;
        break;
    default:
        globalProtoDict.ProtoInfoArray[protoID].protoLayer = SPL_layer_application;
    }

    return 1;
}


void* sdtAppProtoDict_Init(void)
{
    globalProtoDict.proto_num=PROTOCOL_MAX;
    return &globalProtoDict;
}

int  sdtAppProtoDict_Fini(void *dict)
{

    return 1;
}



int sdtAppProtoDict_getProtoCnt(void *vdict)
{
    if(NULL==vdict){
        return 0;
    }

    ProtoDict *dict=(ProtoDict *)vdict;

    return dict->proto_num;
}


ProtoDef  *sdtAppProtoDict_getProtoByIndex(void *vdict, int index)
{
    ProtoDict *dict=(ProtoDict *)vdict;
    if(NULL==dict ||
       index>dict->proto_num){
        return NULL;
    }

    if(dict->ProtoInfoArray[index].fieldCnt<=0){
        return NULL;
    }

    return &dict->ProtoInfoArray[index];
}


FieldHdrDef *sdtAppProtoDict_getFieldDefByIndex(ProtoDef *fieldDef, int fieldIndex)
{
    if(fieldDef==NULL || fieldIndex>fieldDef->fieldCnt){
        return NULL;
    }

    ProtoDict* dict=(ProtoDict *)sdtAppProtoDict_Init();
    if(dict==NULL){
        return NULL;
    }
    dpi_field_table  *protoTbl=dict->tblArray[fieldDef->protoId];
    if(protoTbl==NULL){
        return NULL;
    }

    FieldHdrDef *protoRes = (FieldHdrDef*)temp_var;
    protoRes->type = protoTbl[fieldIndex].type;
    protoRes->index = protoTbl[fieldIndex].index;
    protoRes->protoID = fieldDef->protoId;
    protoRes->protoLayer = dict->ProtoInfoArray[fieldDef->protoId].protoLayer;
    snprintf(protoRes->field_name, 64, "%s", protoTbl[fieldIndex].field_name);

    return  protoRes;
}

static int ip_proto_id      = -1;
static int tcp_proto_id     = -1;
static int udp_proto_id     = -1;
static int sctp_proto_id    = -1;
static int common_proto_id  = -1;
static int link_proto_id    = -1;

FieldHdrDef*  sdtAppProtoDict_getFieldDefByName(void* vdict, char *protoName, char *fieldName)
{
  int               i = 0;
  int               protoID = -1;
  int               index;
  char              field_buf[MAX_FIELD_LEN] = { 0 };
  GHashTable       *shared_hash = NULL;
  GHashTable       *link_hash = NULL;
  GHashTable       *proto_hash = NULL;
  int               shared_pos = -1, link_pos = -1, proto_pos = -1; // 第 0 个位置都有字段，所以 pos 初始要设置为 -1
  int               get_flag = 0;             //
  dpi_field_table   *tblArray  = NULL;
  gpointer result;

  pschema_t *schema = dpi_pschema_get_proto(protoName);
  if (schema == NULL)
    return NULL;

  pfield_desc_t *fdesc = pschema_fdesc_get_by_name(schema, fieldName);
  if(NULL == fdesc)
  {
      printf("ERROR: sdtAppProtoDict_getFieldDefByName 空值 [%s]:[%s]\n", protoName, fieldName);
      return NULL;
  }

  // 字段属性赋值
  FieldHdrDef *protoRes = (FieldHdrDef*)temp_var;
  protoRes->type = (FieldType_t)pfdesc_get_type(fdesc);
  protoRes->index = pfdesc_get_index(fdesc);
  protoRes->protoID=pschema_get_index(schema);
  snprintf(protoRes->field_name, 64, "%s", fieldName);
  //printf("字段ID映射 [%s][%s]-->[%u][%u] 值类型[%u]\n", protoName, fieldName, protoRes->protoID, protoRes->index, protoRes->type);

  //记录Only
  if(0 == strcmp("ip", protoName))
  {
      ip_proto_id = protoRes->protoID;
  }
  else
  if(0 == strcmp("tcp", protoName))
  {
      tcp_proto_id = protoRes->protoID;
  }
  else
  if(0 == strcmp("udp", protoName))
  {
      udp_proto_id = protoRes->protoID;
  }
  else
  if(0 == strcmp("sctp", protoName))
  {
      sctp_proto_id = protoRes->protoID;
  }
  else
  if(0 == strcmp("common", protoName))
  {
      common_proto_id = protoRes->protoID;
  }
  else
  if(0 == strcmp("link", protoName))
  {
      link_proto_id = protoRes->protoID;
  }

  return protoRes;
}

int sdt_is_ascii(const char *p, int len)
{
    if(len <=0)
    {
        return 0;
    }

    int i = 0;
    for(int i = 0; i < len; i++)
    {
        if(0 == isascii(*p))
        {
            return 0;
        }
    }

    return 1;
}

void _temp_debug(precord_t * record, int protoID, int fieldID)
{
    for (player_t * layer = precord_layer_get_first(record); layer ; layer = precord_layer_get_next(record, layer)){
        pschema_t * schema = precord_layer_get_schema(layer);
        int index = pschema_get_index(schema);
        log_debug("layer name %s, index %d", precord_layer_get_layer_name(layer), index);
        if (index != protoID) continue;
        pfield_desc_t *fdesc =  pschema_fdesc_get_by_index(schema, fieldID);
        log_debug("protoid %d, proto name %s, fieldID %d, fieldname %s", protoID, pschema_get_proto_name(schema), fieldID, pfdesc_get_name(fdesc));
    }
}

int value_type_convert(ya_fvalue_t *src, struct value_type *dst)
{
    ya_ftenum_t type = ya_fvalue_type_ftenum(src);
    switch(type)
    {
        case YA_FT_BOOLEAN:
            dst->type   = VALUE_TYPE_BOOL;
            dst->val    = (void*)(size_t)ya_fvalue_get_boolean(src);
            return 0;
        case YA_FT_CHAR:
        case YA_FT_INT8:
        case YA_FT_INT16:
        case YA_FT_INT32:
            dst->type   = VALUE_TYPE_INT;
            dst->val    = (void*)(ssize_t)ya_fvalue_get_sinteger(src);
            return 0;
        case YA_FT_UINT8:
        case YA_FT_UINT16:
        case YA_FT_UINT32:
            dst->type   = VALUE_TYPE_UINT;
            dst->val    = (void*)(size_t)ya_fvalue_get_uinteger(src);
            return 0;
        case YA_FT_INT56:
        case YA_FT_INT64:
            dst->type   = VALUE_TYPE_INT;
            dst->val    = (void*)(ssize_t)ya_fvalue_get_sinteger64(src);
            return 0;
        case YA_FT_UINT56:
        case YA_FT_UINT64:
            dst->type   = VALUE_TYPE_UINT;
            dst->val    = (void*)(size_t)ya_fvalue_get_uinteger64(src);
            return 0;
        case YA_FT_BYTES:
            dst->type   = VALUE_TYPE_BYTES;
            dst->len    = ya_fvalue_length(src);
            dst->val    = (void*)ya_fvalue_get_bytes(src);
            return 0;
        case YA_FT_STRING:
            dst->type   = VALUE_TYPE_BYTES;
            dst->len    = ya_fvalue_length(src);
            dst->val    = (void*)ya_fvalue_get_string(src);
            return 0;
        default:
            return -1;
    }
    return -1;
}

extern dpi_field_table ip_field_array[];
extern dpi_field_table tcp_field_array[];
extern dpi_field_table udp_field_array[];
extern dpi_field_table sctp_field_array[];
extern dpi_field_table common_field_array[];
extern dpi_field_table link_field_array[];

struct value_type *sdtAppProtoRecord_getFieldByIDv3(ProtoRecord *pRec, int protoID, int fieldID)
{
    int        index  = 0;
    pschema_t *schema = NULL;
    precord_t *record = NULL;
    dpi_field_table *field_array = NULL;

//ynao 避免链路层使用回调函数callback参与规则匹配,并保留于link中
#ifndef DPI_SDT_YNAO
    if(protoID == ip_proto_id)
    {
        field_array = ip_field_array;
    }
    else
    if(protoID == tcp_proto_id)
    {
        field_array = tcp_field_array;
    }
    else
    if(protoID == udp_proto_id)
    {
        field_array = udp_field_array;
    }
    else
    if(protoID == sctp_proto_id)
    {
        field_array = sctp_field_array;
    }
    else
    if(protoID == common_proto_id)
    {
        field_array = common_field_array;
    }
    else
    if(protoID == link_proto_id)
    {
        field_array = link_field_array;
    }

    if(field_array)
    {
        if(NULL == field_array[fieldID].callback)
        {
            printf("字段[%s]的回调 待实现\n", field_array[fieldID].field_name);
            return NULL;
        }
        else
        {
            return field_array[fieldID].callback(pRec);
        }
    }
#endif

    struct value_type *val = &pRec->val_temp;
    record = pRec->record;
    for (player_t *layer = precord_layer_get_first(record); layer; layer = precord_layer_get_next(record, layer))
    {
        schema = precord_layer_get_schema(layer);
        index  = pschema_get_index(schema);
        if (index != protoID) {
            continue;
        }

        ya_fvalue_t *fvalue = precord_fvalue_get_by_schema_index(pRec->record, protoID, fieldID);
        if (NULL == fvalue)
        {
            log_trace("precord_fvalue_get_by_schema_index [%d][%d] is NULL", protoID, fieldID);
            return NULL;
        }

        int rc = value_type_convert(fvalue, &pRec->val_temp);
        if(rc)
        {
            printf("ERROR: value_type_convert 规则字段求值 类型不支持 [%u][%u] 类型:%u\n", protoID, fieldID, ya_fvalue_type_ftenum(fvalue));
            abort();
        }
        return &pRec->val_temp;
    }
    return NULL;
}

FieldValue_t*
sdtAppProtoRecord_getFieldByID(ProtoRecord *pRec, int protoID, int fieldID)
{
    return NULL;
    if(protoID>PROTOCOL_MAX){
        return NULL;
    }
#if 0
    uint16_t         fields_num;
    FieldValue_t     *fields_array;
    if(protoID==PROTOCOL_SHARE_HEADER){
        fields_num=pRec->common_fields_num;
        fields_array=pRec->common_fields_array;
    }else if(protoID==PROTOCOL_LINK){
        fields_num=pRec->link_fields_num;
        fields_array=pRec->link_fields_array;
    }else if(protoID==pRec->proto_id){
        fields_num=pRec->fields_num;
        fields_array=pRec->fields_array;
    }else{
        return NULL;
    }

    if(fieldID>fields_num){
        return NULL;
    }

    if(0==fields_array[fieldID].fLen){
        return NULL;
    }

    if(protoID==PROTOCOL_IP  ||
       protoID==PROTOCOL_ARP || //ARP  的REC不是经过TBL转换的, 直接返回
       protoID==PROTOCOL_UDP ||
       protoID==PROTOCOL_TCP){
        return &fields_array[fieldID];
    }

    char str_tmp[32]={0};
    if(fields_array[fieldID].fType>=YV_FT_UINT8 &&
       fields_array[fieldID].fType<=YV_FT_INT64){
        int len=(int)fields_array[fieldID].fLen;
        if(len>32){
            len=31;
        }

        //鉴于最近要演示，这个大改是来不及的, 必须整体重构.  -- 没有银弹.

        // COMMON 的来源有两种在方式：
        // 1. 在应用层 通过 TBL 转换为 fields_value
        // 2. 在IP/TCP/UDP层 有准备好的 fields_value

        //这是一个 数字 ,不是由TBL转换过来的
        if(0 == (0xFFFFFFFF00000000UL & fields_array[fieldID].u.v_u64))
        {
            return  &fields_array[fieldID];
        }

        //否则, 需要将TBL字符串 转成 数字
        strncpy(str_tmp,(char *)fields_array[fieldID].u.v_pBytes, len);

        pRec->tmp_fields_array.fType=fields_array[fieldID].fType;
        pRec->tmp_fields_array.fLen=fields_array[fieldID].fLen;
        switch(fields_array[fieldID].fType){
        case YV_FT_UINT8:
            pRec->int64_tmp = 0;
            pRec->int64_tmp = atoi(str_tmp);
            pRec->tmp_fields_array.u.v_u8=(uint8_t)pRec->int64_tmp;
            break;
        case YV_FT_UINT16:
            pRec->int64_tmp = 0;
            pRec->int64_tmp = atoi(str_tmp);
            pRec->tmp_fields_array.u.v_u16=(uint16_t)pRec->int64_tmp;
            break;
        case YV_FT_UINT32:
            pRec->int64_tmp = 0;
            pRec->int64_tmp = atoi(str_tmp);
            pRec->tmp_fields_array.u.v_u32=(uint32_t)pRec->int64_tmp;
            break;
        case YV_FT_UINT56:
        case YV_FT_UINT64:
            pRec->int64_tmp = 0;
            pRec->int64_tmp = atol(str_tmp);
            pRec->tmp_fields_array.u.v_u64=(uint64_t)pRec->int64_tmp;
            break;
        case YV_FT_INT8:
            pRec->int64_tmp = 0;
            pRec->int64_tmp = atoi(str_tmp);
            pRec->tmp_fields_array.u.v_i8=(int8_t)pRec->int64_tmp;
            break;
        case YV_FT_INT16:
            pRec->int64_tmp = 0;
            pRec->int64_tmp = atoi(str_tmp);
            pRec->tmp_fields_array.u.v_i16=(int16_t)pRec->int64_tmp;
            break;
        case YV_FT_INT32:
            pRec->int64_tmp = 0;
            pRec->int64_tmp = atoi(str_tmp);
            pRec->tmp_fields_array.u.v_i32=(int32_t)pRec->int64_tmp;
            break;
        case YV_FT_INT56:
        case YV_FT_INT64:
            pRec->int64_tmp = 0;
            pRec->int64_tmp = atol(str_tmp);
            pRec->tmp_fields_array.u.v_i64=(int64_t)pRec->int64_tmp;
            break;
        default:
            break;
        }
        return &pRec->tmp_fields_array;
    }

    return &fields_array[fieldID];
#endif
}

int
sdtAppProtoRecord_getProtoId(ProtoRecord *pRec)
{
    return pRec->proto_id;
}

const char*
sdtAppProtoRecord_getProtoName(ProtoRecord *pRec)
{
    return  protocol_name_array[pRec->proto_id];
}

int
sdtAppProtoRecord_getFieldCnt(ProtoRecord *pRec)
{
    // return pRec->fields_num;
    return 0;
}

int
sdtAppProtoRecord_getIpTuple(ProtoRecord *pRec, struct pkt_tuple_t *pTuple)
{
    memcpy(pTuple, &pRec->tuple, sizeof(struct pkt_tuple_t));
    return 0;
}

int sdtAppProtoRecord_getPkts(ProtoRecord *pRec, ys_packets *pPkts, int num){

    int i = 0;
    struct packet_stream *pos = NULL;
    struct packet_stream *n   = NULL;

    if (list_empty(&pRec->flow->sdt_flow.pkt_stream_head)){
        return 0;
    }

    list_for_each_entry_safe_reverse(pos, n, &pRec->flow->sdt_flow.pkt_stream_head, node){
        if(&pos->node == &pRec->flow->sdt_flow.pkt_stream_head){
            break;
        }
        pPkts[i].dir = pos->direction;
        if (pRec->ip_version == 4)pPkts[i].ip_hdr = (struct iphdr *)&pos->pkt_data[pos->l3h_start];
        if (pRec->seq != 0)pPkts[i].tcp_hdr = (struct tcphdr *)&pos->pkt_data[pos->l4h_start];
        int plen=(int)(pos->ip_len+pos->l3h_start-pos->l4payload_start);

        if(plen>0){
            pPkts[i].payload_len = plen > 8192 ? 8192 : plen;
        }else{
            pPkts[i].payload_len = 0;
        }

        pPkts[i].payload = (char*)&pos->pkt_data[pos->l4payload_start];
        // 待处理 pPkts[i]->pcap_hdr
        if(++i>=num) break;
    }
    return i;
}

int sdtAppProtoRecord_getStream(ProtoRecord *pRec, ys_stream *pStream){
    int i = 0;
    struct packet_stream *pos = NULL;
    struct packet_stream *n   = NULL;

    pStream->c_num = 0;
    pStream->s_num = 0;

    if (list_empty(&pRec->flow->sdt_flow.single_stream_head[0])){
        return 0;
    }
    list_for_each_entry_safe_reverse(pos, n, &pRec->flow->sdt_flow.single_stream_head[0], snode){
        if(&pos->node == &pRec->flow->sdt_flow.single_stream_head[0]){
            break;
        }
        if (pos->ip_len + pos->l3h_start == pos->l4payload_start) continue;
        pStream->c_num++;
        pStream->c_pkt_len[i] = pos->pkt_data_len;
        pStream->c_pkt_data[i] = (char*)&pos->pkt_data[pos->l4payload_start];
        if(++i>=12) break;
    }


    if (list_empty(&pRec->flow->sdt_flow.single_stream_head[1])){
        return 1;
    }

    i = 0;
    list_for_each_entry_safe_reverse(pos, n, &pRec->flow->sdt_flow.single_stream_head[1], snode){
        if(&pos->node == &pRec->flow->sdt_flow.single_stream_head[1]){
            break;
        }
        if (pos->ip_len + pos->l3h_start == pos->l4payload_start) continue;
        pStream->s_num++;
        pStream->s_pkt_len[i] = pos->pkt_data_len;
        pStream->s_pkt_data[i] = (char*)&pos->pkt_data[pos->l4payload_start];
        if(++i>=12) break;
    }

    return 2;

}


int sdtAppProtoRecord_getIpSequence(ProtoRecord *pRec,ys_ipseq *ipSeqs, int dir)
{
    if(!ipSeqs){
        return 0;
    }
    if(dir<0 || dir>1){
        return 0;
    }

    if(pRec->proto_id!=PROTOCOL_IPP){
        return 0;
    }

    int i = 0;
    struct packet_stream *pos = NULL;
    struct packet_stream *n   = NULL;

    if (list_empty(&pRec->flow->sdt_flow.single_stream_head[dir])){
        return 0;
    }
    list_for_each_entry_safe(pos, n, &pRec->flow->sdt_flow.single_stream_head[dir], snode){
        if(&pos->node == &pRec->flow->sdt_flow.single_stream_head[dir]){
            break;
        }

        ipSeqs->seqs[i]=pos->ip_len+pos->l3h_start-pos->l4h_start;

        if(++i>=8) break;
    }
    ipSeqs->nums=i;

    return i;
}



int SdtAppRuleHash_ruleTransactStart(int transactId)
{
    log_info(__FUNCTION__);

    printf("TransactStart: 开始规则编译, 停止收包...\n");
    g_config.stop_rcv_pkts=1;

    //sdt_clean_flow_update_rule();

    tbl_log_file_close_writing();
    int num = 0;
    while((num=get_flow_total_num())){
        printf("等待解析线程超时 %u.\n", num);
        sleep(1);
    }

    sdt_rule_hash_db_clean();

    return 0;
}

extern time_t rule_update;
int SdtAppRuleHash_ruleTransactFinish(int transactId, SdtErrorMsg_t *pErr)
{
    log_info(__FUNCTION__);
    rule_update = time(NULL);
    if(pErr && pErr->lErrorCode<0){
        sdt_rule_hash_db_clean();
        printf("\t Transact execute failed，failed code=%d, reason:%s\n",
                            pErr->lErrorCode,pErr->pszErrorBuff);
    }

    sdt_set_rule_proto_switch();
    sdx_match_status_clean();

    printf("TransactFinish: 规则编译成功, 恢复收包.\n");
    g_config.stop_rcv_pkts=0;

    //清空启动前的 网卡芯片 寄存器的计数
    int pcnt =  g_config.sdx_config.sdx_rx_port_num;
    for (int port_index = 0; port_index < pcnt; port_index++)
    {
        rte_eth_stats_reset(port_index);
    }

    return 0;
}

int SdtAppRuleHash_RuleInitClear(void)
{
    log_info(__FUNCTION__);
    sdt_rule_hash_db_clean();
    return 1;
}

int SdtAppRuleHash_RuleInitStart(void)
{
    log_info(__FUNCTION__);
    return 1;
}

int SdtAppRuleHash_ruleInitEnd(void)
{
    log_info(__FUNCTION__);
    return 1;
}

/*
*  初始化 rule hash表
*
* return：
*   -1 出错
    0  重复插入，该规则已存在；
    1  插入成功
*/
int SdtAppRuleHash_insertRules(SdtMatchResult *ruleAction)
{
    if(!ruleAction){
        DPI_LOG(DPI_LOG_ERROR, "rule insert faile action is NULL");
        return -1;
    }
    int ret=-1;
    ret=sdt_rule_hash_db_insert(ruleAction);
    if(ret<0){
        DPI_LOG(DPI_LOG_ERROR, "rule insert faile action insert failed");
        return -1;
    }

    return 1;
}


/* web前端获取统计数据增量记录 */
rule_web_stat  *SdtAppRuleHash_getStatistics(uint32_t          rule_hash_code)
{
    return &g_static_stat;
}


uint32_t      sdtAppProtoRecord_getMatchDataLen(ProtoRecord *pRec)
{
    return pRec->pPayload.len > 0 ? pRec->pPayload.len : pRec->match_data_len;
}

int get_sdt_sysconfig(enum EN_SDT_SYSCONFIG type)
{
    switch(type)
    {
        case SDT_SURVIVE_BYTES:
            //触发阈值后, 开始告警
            return g_config.web_config.traffic;
            break;

        case SDT_SURVIVE_ALERT:
            //直到 N秒 结束告警
            return g_config.web_config.duration;
            break;

        case SDT_SURVIVE_DYNAMIC:
            //动态规则, 存活时长
            return g_config.web_config.dynamic_old_time;
            break;

        default:
            DPI_LOG(DPI_LOG_ERROR, "get_sdt_sysconfig 不支持的类型 %u", type);
            abort();
    }
    return 0;
}

void* get_sdt_sysconfig2(enum EN_SDT_SYSCONFIG type)
{
    switch (type)
    {
        case SDT_MONITOR_RULE_PATH:
            return g_config.sdx_rule_path;
        case SDT_PLUGIN_DIR:
            return g_config.sdx_plugin_dir;
        default:
            DPI_LOG(DPI_LOG_ERROR, "get_sdt_sysconfig2 不支持的类型 %u", type);
            abort();
    }

    return NULL;
}


int set_sdt_sysconfig(enum EN_SDT_SYSCONFIG type, void *arg)
{
    switch(type)
    {
        case SDT_TBL_OUT_DIR:
            pthread_mutex_lock(&mutex_g_config);
            strncpy(g_config.tbl_out_dir, (char*)arg, sizeof(g_config.tbl_out_dir));
            if (access(g_config.tbl_out_dir, F_OK))
                mkdirs(g_config.tbl_out_dir);
            pthread_mutex_unlock(&mutex_g_config);
            break;
        case SDT_PAUSE_SCAN_PCAP_FLAG:
            pthread_mutex_lock(&mutex_g_config);
            sscanf((char*)arg,"%u", &g_config.pause_scan_pcap);;
            pthread_mutex_unlock(&mutex_g_config);
            break;
        default:
            DPI_LOG(DPI_LOG_ERROR, "set_sdt_sysconfig 不支持的类型 %u", type);
            return -1;
    }

    return 0;
}


/***************************************以下为测试临时模拟函数，具体功能不再此处实现*************************************************/

static void print_bin(PayloadBuff *get_result)
{
    int i;
    printf("\npkt_len:%d,data:\n",get_result->len);
    for(i=0;i<get_result->len;i++){
        if(i!=0 && i%16==0){
            printf("\n");
        }
        printf("%02x ",get_result->pBuff[i]);
    }
}

//各个协议是否 允许 IPFF ?
int proto_mode_would_ipff(int protoid)
{
    struct {
        int protoid;
        int ipff;
    } proto_ID_list[]={
        {PROTOCOL_ARP,          1},
        {PROTOCOL_DBBASIC,      0},//数据库
        {PROTOCOL_MYSQL,        0},
        {PROTOCOL_TNS,          0},
        {PROTOCOL_TDS,          0},
        {PROTOCOL_PGSQL,        0},
        {PROTOCOL_EMAIL,        0},//邮件
        {PROTOCOL_MAIL_SMTP,    0},
        {PROTOCOL_MAIL_ESMTP,   0},
        {PROTOCOL_MAIL_IMAP,    0},
        {PROTOCOL_MAIL_POP,     0},
        {PROTOCOL_HTTP,         0},
        {PROTOCOL_SOCKS,        1},
        {PROTOCOL_SYSLOG,       1},
        {PROTOCOL_GRE,          1},
        {PROTOCOL_SSL,          0},
        {PROTOCOL_SSH,          0},
        {PROTOCOL_SMB,          0},
        {PROTOCOL_SCTP,         1},
        {PROTOCOL_X509,         0},
        {PROTOCOL_CWMP,         0},//未知
        {PROTOCOL_FTP_CONTROL,  0},//FTP
        {PROTOCOL_FTP_DATA,     0},
        {PROTOCOL_TFTP,         0},
        {PROTOCOL_LINK,         1},
        {PROTOCOL_SHARE_HEADER, 1},
        {PROTOCOL_SNMP,         1},
        {PROTOCOL_BGP,          0},
        {PROTOCOL_VNC,          0},
        {PROTOCOL_MODBUS,       1},
        {PROTOCOL_S7COMM,       1},
        {PROTOCOL_DNS,          1},
        {PROTOCOL_ICMP,         1},
        {PROTOCOL_RDP,          0},
        {PROTOCOL_TELNET,       0},
    };

    int size = sizeof(proto_ID_list)/sizeof(proto_ID_list[0]);
    for(int i = 0; i < size; i++)
    {
        if(proto_ID_list[i].protoid == protoid)
        {
            return proto_ID_list[i].ipff;
        }
    }

    //默认不允许 IPFF
    return 0;
}

const char* get_sdt_sysconfig_device_id(void)
{
    return g_config.sdt_out_produce_data_dev_name;
}

//0、无规则则为空闲，
//1、存在有效规则且有数据在处理即为工作中,
//2、存在有效规则且无数据在处理则为已完成
int get_sdt_program_status(void)
{
    if(0  == dpi_input_is_existed_sdtEngine_active_rules())
        return 0;
    else if(dpi_existed_data_not_del()){
        return 1;
    }else{
        return 2;
    }
}
