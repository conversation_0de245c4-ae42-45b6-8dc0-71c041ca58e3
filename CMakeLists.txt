cmake_minimum_required(VERSION 3.15)

set(CMAKE_C_STANDARD 11)
set(CMAKE_CXX_STANDARD 14)

set(ProName "yaDpiSdt")
project(${ProName} LANGUAGES C CXX)

# 获取 gcc 版本
execute_process(
  COMMAND ${CMAKE_C_COMPILER} -dumpversion
  OUTPUT_VARIABLE GCC_VERSION
  OUTPUT_STRIP_TRAILING_WHITESPACE
)

message(STATUS "gcc version ${GCC_VERSION}")

#  版本设置
if (EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/VERSION")
  file(READ "${CMAKE_CURRENT_SOURCE_DIR}/VERSION" PROGRAM_VERSION)
  string(STRIP "${PROGRAM_VERSION}" PROGRAM_VERSION)
  string(TIMESTAMP PROJECT_VERSION_TWEAK "%Y%m%d")
else()
  message(FATAL_ERROR "FILE ${CMAKE_CURRENT_SOURCE_DIR}/VERSION not found")
endif()

message("program version ${PROGRAM_VERSION}")
message("patch verson ${PROJECT_VERSION_TWEAK}")

# 生成 version.h
configure_file(
  cmake/version.h.in
  include/version.h
  @ONLY
)


set(AppName "${ProName}_${PROGRAM_VERSION}")

set(dpi_files "")
set(dpi_dirs "")
set(dpi_bins "")

# 适配 旧版 makefile
add_definitions(-DPROJECT_VERSION_STR="${PROGRAM_VERSION}")

if (NOT CMAKE_BUILD_TYPE)
  set(CMAKE_BUILD_TYPE Debug CACHE STRING "Build Type" FORCE)
endif()

#add_compile_options(-fsanitize=address)
#add_compile_options(-fstack-protector-all)
#add_compile_options(-fno-omit-frame-pointer)
#add_compile_options(-static-libasan)

set(warn_flags  "-Wall -Wextra -Wno-unused-parameter -Wno-unused-variable   \
                -Wno-unused-function -Wno-unused-but-set-variable -Wno-cast-qual  \
                -Wno-format-truncation -Wno-address-of-packed-member")

set(CMAKE_CXX_FLAGS_DEBUG "-O0 -g")

# 是否生成 compile_command.json
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

option(EMABLE_MEMORY_CHECK "Enable memory check" ON)
option(USE_CLANG_TIDY  "Use clang-tidy for static analysis" OFF)

if (USE_CLANG_TIDY)
  find_program(CLANG_TIDY_EXE NAMES "clang-tidy")
  if (CLANG_TIDY_EXE)
    set(CMAKE_CXX_CLANG_TIDY "${CLANG_TIDY_EXE}")
    set(CMAKE_C_CLANG_TIDY "${CLANG_TIDY_EXE}")
  else()
    message(WARNING "clang-tidy not found")
  endif()
endif()

if (EMABLE_MEMORY_CHECK)
  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fsanitize=address")
  set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fsanitize=address")
endif()

include(GNUInstallDirs)
# set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/${CMAKE_INSTALL_BINDIR})
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/run)

if (CMAKE_INSTALL_PREFIX_INITIALIZED_TO_DEFAULT)
  set(CMAKE_INSTALL_PREFIX "/root/program" CACHE PATH "..." FORCE)
endif()
message(STATUS "install prefix: ${CMAKE_INSTALL_PREFIX}")

set(INSTALL_LIBDIR ${CMAKE_PROJECT_NAME}/${CMAKE_INSTALL_LIBDIR} CACHE PATH "Installation directory for libraries")
set(INSTALL_BINDIR ${CMAKE_PROJECT_NAME} CACHE PATH "Installation directory for executables")
set(INSTALL_INCLUDEDIR ${CMAKE_PROJECT_NAME}/${CMAKE_INSTALL_INCLUDEDIR} CACHE PATH "Installation directory for header files")
set(INSTALL_ROOTDIR ${CMAKE_PROJECT_NAME} CACHE PATH "Installation directory for files")
set(INSTALL_CMAKEDIR ${CMAKE_PROJECT_NAME} CACHE PATH "Installation directory for header files")

# 报告安装位置
message(STATUS "Project will be installed to ${CMAKE_INSTALL_PREFIX}")
foreach(p LIB BIN INCLUDE CMAKE ROOT)
  file(TO_NATIVE_PATH ${CMAKE_INSTALL_PREFIX}/${INSTALL_${p}DIR} _path)
  message(STATUS "Installing ${p} components to ${_path}")
  unset(_path)
endforeach()

# dpdk 编译导出的 cflags
list(APPEND dpdk_cflags
  -march=native
  -DRTE_MACHINE_CPUFLAG_SSE
  -DRTE_MACHINE_CPUFLAG_SSE2
  -DRTE_MACHINE_CPUFLAG_SSE3
  -DRTE_MACHINE_CPUFLAG_SSSE3
  -DRTE_MACHINE_CPUFLAG_SSE4_1
  -DRTE_MACHINE_CPUFLAG_SSE4_2
  -DRTE_MACHINE_CPUFLAG_AES
  -DRTE_MACHINE_CPUFLAG_PCLMULQDQ
  -DRTE_MACHINE_CPUFLAG_AVX
)

set(ENV{PKG_CONFIG_PATH} "$ENV{PKG_CONFIG_PATH}:/usr/local/lib64/pkgconfig")

add_subdirectory(src)
add_subdirectory(test)

add_custom_target(add_proto
  COMMAND
    bash ${CMAKE_CURRENT_SOURCE_DIR}/src/write_protocol.sh
)
